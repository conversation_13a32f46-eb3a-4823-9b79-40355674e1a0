# Collection Initialization Feature

## Overview

This feature automatically initializes MongoDB collections from JSON files during application startup. It handles cases where the mongo folder or JSON files don't exist by creating them from the current database content.

## How It Works

### 1. Initialization Process

When `Application.initApplication()` is called, it triggers the collection initialization process:

1. **Check Mongo Folder**: Verifies if `/src/main/resources/mongo/` folder exists
2. **Process Each Collection**: For each collection in `Defaults.COLLECTION_NAMES`:
   - Check if collection exists in database
   - Look for corresponding JSON file
   - Process accordingly

### 2. Scenarios Handled

#### Scenario A: Mongo folder and JSON files exist
- Reads JSON files
- Checks each document's `_id` against database
- Inserts missing documents
- Updates JSON file with current database state

#### Scenario B: Mongo folder exists but JSON file missing
- Skips JSON file processing
- Creates JSON file from current database content

#### Scenario C: Mongo folder doesn't exist
- Creates the mongo folder
- Creates JSON files from current database content

### 3. File Structure

```
koraliso/src/main/resources/mongo/
├── user.json
├── company.json
├── settings.json
├── path.json
└── label.json
```

## Configuration

### Collection Names

Collections are defined in `Defaults.java`:

```java
public static List<String> COLLECTION_NAMES = new ArrayList<>(Arrays.asList(
    "path", "user", "company", "settings", "label"
));
```

### JSON File Format

JSON files should contain arrays of documents in MongoDB extended JSON format:

```json
[
    {
        "_id": {"$oid": "507f1f77bcf86cd799439011"},
        "creation": {"$date": "2019-07-23T12:00:00.000Z"},
        "lastUpdate": {"$date": "2019-07-23T12:00:00.000Z"},
        "name": "Example Document",
        "email": "<EMAIL>"
    }
]
```

## Key Methods

### BaseDao.collectionExists(String collectionName)
- Checks if a MongoDB collection exists
- Returns boolean indicating existence

### Application.initializeCollections()
- Main initialization method
- Processes all collections defined in COLLECTION_NAMES

### Application.updateJsonFileWithDatabaseContent(String collectionName)
- Creates/updates JSON files with current database content
- Handles MongoDB extended JSON format conversion
- Creates directory structure if needed

## Error Handling

- Comprehensive logging for all operations
- Graceful handling of missing files/folders
- Exception catching to prevent application startup failure
- Detailed error messages for debugging

## Usage

The initialization runs automatically during application startup. No manual intervention required.

### Manual Testing

To test the functionality:

1. **Delete mongo folder**: Remove `/src/main/resources/mongo/`
2. **Start application**: The folder and files will be created from database content
3. **Check logs**: Look for initialization messages in application logs

### Adding New Collections

1. Add collection name to `Defaults.COLLECTION_NAMES`
2. Optionally create JSON file in `/src/main/resources/mongo/`
3. Restart application

## Logging

The feature provides detailed logging:

```
INFO - Starting collection initialization...
INFO - Processing collection: user
INFO - Collection 'user' exists in database: true
INFO - Found JSON file for collection: user
INFO - Found 1 documents in JSON file for collection: user
INFO - Document with _id: 507f1f77bcf86cd799439011 already exists in collection: user
INFO - Successfully wrote 1 documents to JSON file for collection 'user'
```

## Benefits

1. **Automatic Setup**: No manual database seeding required
2. **Consistency**: Ensures all environments have required data
3. **Flexibility**: Handles missing files gracefully
4. **Maintainability**: Easy to add new collections
5. **Robustness**: Comprehensive error handling
