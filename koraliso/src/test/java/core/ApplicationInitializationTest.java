package core;

import dao.BaseDao;
import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;
import utils.Defaults;

/**
 * Test class for Application initialization functionality
 */
public class ApplicationInitializationTest {

    @Before
    public void setUp() {
        // Initialize Core if needed for testing
        // Note: This would require a test MongoDB instance
    }

    @Test
    public void testCollectionNamesExist() {
        // Test that collection names are properly defined
        assertNotNull("Collection names should not be null", Defaults.COLLECTION_NAMES);
        assertFalse("Collection names should not be empty", Defaults.COLLECTION_NAMES.isEmpty());
        
        // Verify expected collections are present
        assertTrue("Should contain 'path' collection", Defaults.COLLECTION_NAMES.contains("path"));
        assertTrue("Should contain 'user' collection", Defaults.COLLECTION_NAMES.contains("user"));
        assertTrue("Should contain 'company' collection", Defaults.COLLECTION_NAMES.contains("company"));
        assertTrue("Should contain 'settings' collection", Defaults.COLLECTION_NAMES.contains("settings"));
        assertTrue("Should contain 'label' collection", Defaults.COLLECTION_NAMES.contains("label"));
    }

    @Test
    public void testCollectionExistsMethod() {
        // Test the collectionExists method
        // Note: This test would require a running MongoDB instance
        // For now, we just verify the method doesn't throw exceptions
        try {
            // This will likely return false in a test environment without MongoDB
            boolean exists = BaseDao.collectionExists("test_collection");
            // The method should not throw an exception
            assertTrue("Method should execute without throwing exceptions", true);
        } catch (Exception e) {
            fail("collectionExists method should not throw exceptions: " + e.getMessage());
        }
    }

    @Test
    public void testInitializationHandlesNonExistentFiles() {
        // Test that initialization can handle cases where mongo folder or files don't exist
        // This is more of a documentation test since we can't easily test the full flow
        // without a complete application context

        // Verify that the collection names are properly configured
        for (String collectionName : Defaults.COLLECTION_NAMES) {
            assertNotNull("Collection name should not be null", collectionName);
            assertFalse("Collection name should not be empty", collectionName.trim().isEmpty());
        }

        // The actual initialization logic will be tested during application startup
        assertTrue("Initialization logic should be able to handle missing files", true);
    }
}
