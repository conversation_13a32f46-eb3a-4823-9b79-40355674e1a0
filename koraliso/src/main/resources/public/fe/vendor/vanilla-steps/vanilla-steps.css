/* <PERSON>ili per il wizard a step */

/* Stile per i pannelli di step */
.step-tab-panel {
  display: none;
}

.step-tab-panel.active {
  display: block;
}

/* <PERSON>ili per i pulsanti di navigazione */
.step-btn {
  transition: all 0.3s ease;
}

.step-btn[data-step-action="prev"] {
  opacity: 1;
  transform: translateX(0);
}

.step-btn[data-step-action="prev"].d-none {
  display: none !important;
}

.step-btn[data-step-action="next"],
.step-btn[data-step-action="finish"] {
  opacity: 1;
  transform: translateX(0);
}

.step-btn[data-step-action="next"].d-none,
.step-btn[data-step-action="finish"].d-none {
  display: none !important;
}

/* Stili per la sidebar di navigazione */
.step-steps .nat-item .nav-link {
  transition: all 0.3s ease;
  cursor: pointer;
}

.step-steps .nat-item .nav-link.active {
  font-weight: 500;
  
}

.step-steps .nat-item .nav-link.completed {
  color: var(--bs-success, #24c186);
}

/* Animazione per la barra di progresso */
.progress-bar {
  transition: width 0.4s ease;
}

/* Animazioni per i cambi di step */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.step-tab-panel.active {
  animation: fadeIn 0.4s ease forwards;
}