<svg id='Diving_Mask_Fish_32' width='32' height='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='32' height='32' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(1.17 0 0 1.17 16 16)" >
<g style="" >
<g transform="matrix(1 0 0 1 -8.36 -8.36)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.2820512820512822; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3.75, -3.75)" d="M 3.75 6.75 L 2.25 6.75 C 1.4215728752538097 6.75 0.75 6.07842712474619 0.75 5.25 L 0.75 3.75 C 0.75 2.0931457505076194 2.0931457505076194 0.75 3.75 0.75 L 6.75 0.75 L 6.75 3.75 C 6.75 5.406854249492381 5.406854249492381 6.75 3.75 6.75 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -2.36 -8.36)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.2820512820512822; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-9.75, -3.75)" d="M 11.25 6.75 L 9.75 6.75 C 8.09314575050762 6.75 6.75 5.406854249492381 6.75 3.75 L 6.75 0.75 L 9.75 0.75 C 11.40685424949238 0.75 12.75 2.0931457505076185 12.75 3.749999999999999 L 12.75 5.25 C 12.75 6.07842712474619 12.07842712474619 6.75 11.25 6.75 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.36 -0.11)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.2820512820512822; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.75, -12)" d="M 4.5 12 C 4.5 13.242640687119286 5.507359312880714 14.25 6.75 14.25 C 7.992640687119286 14.25 9 13.242640687119286 9 12 C 9 10.757359312880714 7.992640687119286 9.75 6.75 9.75 C 5.507359312880714 9.75 4.5 10.757359312880714 4.5 12 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.27 -3.11)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.2820512820512822; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12.38, -9)" d="M 9 12 L 12.75 12 C 14.40685424949238 12 15.75 10.65685424949238 15.75 9 L 15.75 6" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.64 -10.23)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.2820512820512822; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-15.75, -1.88)" d="M 15.75 0.75 L 15.75 3" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.64 7.38)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.2820512820512822; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-15.75, -19.49)" d="M 23.25 23.25 C 23.25 23.25 16.598 16.668 13.85 16.668 C 11.62885158123584 16.61186299787699 9.52690910183843 17.670716521873437 8.249999999999998 19.489000000000004 C 9.526909101838434 21.307283478126568 11.628851581235843 22.36613700212301 13.850000000000001 22.310000000000002 C 16.601 22.310000000000002 23.25 15.727000000000002 23.25 15.727000000000002 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -8.36 -0.11)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.2820512820512822; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3.75, -12)" d="M 4.5 12 L 3 12" stroke-linecap="round" />
</g>
</g>
</g>
</svg>