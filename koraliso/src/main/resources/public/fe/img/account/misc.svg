<svg id='Bathroom_Shower_Person_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 3.25 -0.75)" >
<circle style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="2.5" />
</g>
<g transform="matrix(1 0 0 1 2.25 6.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-14, -18.5)" d="M 12.5 19.5 L 14.5 19.5 C 15.052284749830793 19.5 15.5 19.052284749830793 15.5 18.5 C 15.5 17.947715250169207 15.052284749830793 17.5 14.5 17.5 L 12.5 17.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 4.76 4.7)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-16.51, -16.46)" d="M 17.5 23.5 L 17.5 17.36 C 20.338460238378545 16.13637777023905 22.508338292967544 13.744041629381261 23.449999999999996 10.8 C 23.59461622812934 10.450757825578219 23.52994893248798 10.04981891098821 23.282864255349192 9.763754036953607 C 23.035779578210406 9.477689162919004 22.64851009555689 9.355393536817894 22.281947598453627 9.447675092670796 C 21.915385101350367 9.539956648523699 21.632183732055744 9.831042300502347 21.55 10.200000000000001 C 20.78803796022594 12.572279471850823 19.04335833184636 14.502718309097538 16.759999999999998 15.5 L 11.5 15.5 C 10.756356403953614 15.501222369424939 10.074792300877533 15.914939339994103 9.730520654301928 16.574093590144958 C 9.386249007726324 17.23324784029581 9.436121572971084 18.02898905801344 9.86 18.64 C 10.23303456547751 19.177724472275745 10.845552725394679 19.498923019549384 11.5 19.5 L 12.5 19.5 L 12.5 23.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -9.21 -10.46)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-2.54, -1.29)" d="M 0.5 0.5 L 3 0.5 C 3.3147573033330526 0.5 3.611145618000168 0.6481941573335577 3.8 0.8999999999999999 L 4.59 2.09" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -6.18 -8.68)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.57, -3.07)" d="M 5 4.5 C 4.889163138354843 4.610746515314304 4.732821755080084 4.663133038492227 4.577635778358212 4.6415248645182965 C 4.42244980163634 4.619916690544365 4.286366574921739 4.5268128166084285 4.21 4.39 C 3.8290156572647644 3.621029226388814 3.981070932659586 2.6947143070308095 4.5878926198451975 2.087892619845198 C 5.194714307030809 1.4810709326595863 6.1210292263888135 1.329015657264765 6.889999999999999 1.71 C 7.0268128166084285 1.7863665749217386 7.119916690544365 1.9224498016363394 7.141524864518296 2.0776357783582116 C 7.163133038492227 2.2328217550800833 7.110746515314304 2.389163138354842 7 2.4999999999999996 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -4.46 -5.39)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-0.4500000000000002" y1="-0.6000000000000001" x2="0.4500000000000002" y2="0.6000000000000001" />
</g>
<g transform="matrix(1 0 0 1 -2.36 -2.59)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-0.4500000000000002" y1="-0.5999999999999996" x2="0.4500000000000002" y2="0.5999999999999996" />
</g>
<g transform="matrix(1 0 0 1 -6.5 -4.3)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-0.25" y1="-0.71" x2="0.25" y2="0.71" />
</g>
<g transform="matrix(1 0 0 1 -5.32 -1.01)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-0.25" y1="-0.7050000000000001" x2="0.25" y2="0.7050000000000001" />
</g>
<g transform="matrix(1 0 0 1 -4.15 2.29)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-0.25500000000000034" y1="-0.7050000000000001" x2="0.25500000000000034" y2="0.7050000000000001" />
</g>
<g transform="matrix(1 0 0 1 -2.89 -6.96)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-0.6000000000000005" y1="-0.4500000000000002" x2="0.6000000000000005" y2="0.4500000000000002" />
</g>
<g transform="matrix(1 0 0 1 -0.09 -4.87)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-0.5999999999999996" y1="-0.44499999999999984" x2="0.5999999999999996" y2="0.44499999999999984" />
</g>
<g transform="matrix(1 0 0 1 -1.8 -9)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-0.71" y1="-0.25" x2="0.71" y2="0.25" />
</g>
<g transform="matrix(1 0 0 1 1.49 -7.83)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-0.7050000000000001" y1="-0.2549999999999999" x2="0.7050000000000001" y2="0.2549999999999999" />
</g>
<g transform="matrix(1 0 0 1 4.79 -6.65)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-0.7050000000000001" y1="-0.25500000000000034" x2="0.7050000000000001" y2="0.25500000000000034" />
</g>
</g>
</g>
</svg>