<svg id='Diving_Scuba_Free_Diving_Buoy_Flag_Hold_32' width='32' height='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='32' height='32' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(1.17 0 0 1.17 16 16)" >
<g style="" >
<g transform="matrix(1 0 0 1 -7.43 -2.59)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.2820512820512822; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.68, -9.52)" d="M 0.75 10.2723 C 2.24336 10.2723 3.73673 10.2723 5.23009 8.77234 C 6.72345 10.2723 8.21681 10.2723 8.61504 10.2723" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 8.9 -2.59)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.2820512820512822; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-21.01, -9.52)" d="M 18.7699 8.77234 C 20.2633 10.2723 21.7566 10.2723 23.25 10.2723" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -4.37 8.33)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.2820512820512822; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7.74, -20.44)" d="M 3.93666 23.0709 L 5.0936 20.308 C 5.29965 19.8159 5.69375 19.4266 6.18828 19.2265 L 9.47095 17.8984 C 10.2389 17.5876 11.1133 17.9583 11.424 18.7263 C 11.7347 19.4942 11.3641 20.3687 10.5961 20.6794 L 7.69749 21.8427 L 7.17766 23.0845" stroke-linecap="round" />
</g>
<g transform="matrix(0.39 -0.92 0.92 0.39 -5.53 2.81)" >
<circle style="stroke: rgb(51,61,76); stroke-width: 1.2820512820512822; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="2" />
</g>
<g transform="matrix(1 0 0 1 1.67 -3.99)" >
<circle style="stroke: rgb(51,61,76); stroke-width: 1.2820512820512822; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="2.511" />
</g>
<g transform="matrix(1 0 0 1 3.7 -8.92)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.2820512820512822; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-15.81, -3.19)" d="M 13.8177 5.60906 L 13.8177 0.773895 L 17.8023 0.773895 L 17.8023 3.86607 L 13.8177 3.86607" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.63 4.73)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.2820512820512822; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13.73, -16.83)" d="M 13.7347 10.672 L 13.7347 22.9974" stroke-linecap="round" />
</g>
</g>
</g>
</svg>