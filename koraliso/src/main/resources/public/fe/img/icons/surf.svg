<svg id='Nautic_Sports_Surfing_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -4 -8.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7.75, -3)" d="M 5.251 3 C 5.25099998296425 3.893163986127576 5.727497287342336 4.71848155121594 6.500999986234897 5.1650635491975265 C 7.274502685127458 5.611645547179112 8.227497314872542 5.611645547179112 9.001000013765104 5.1650635491975265 C 9.774502712657664 4.71848155121594 10.25100001703575 3.893163986127576 10.251 3 C 10.25100001703575 2.106836013872424 9.774502712657664 1.2815184487840598 9.001000013765104 0.8349364508024739 C 8.227497314872542 0.388354452820888 7.274502685127458 0.38835445282088843 6.500999986234897 0.8349364508024739 C 5.727497287342336 1.2815184487840596 5.25099998296425 2.106836013872424 5.251 2.9999999999999996 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -4 7.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7.75, -19.5)" d="M 7.751 22 L 7.751 17" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -4.25 3.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7.5, -15.5)" d="M 13.252 8.6 L 11.602 7.7299999999999995 C 11.314401449862814 7.578302675939775 10.99415381590449 7.49901306881826 10.669 7.499 L 4.751 7.499 C 3.0941457505076198 7.499 1.7510000000000003 8.842145750507619 1.7510000000000003 10.498999999999999 L 1.7510000000000003 16 C 1.7510000000000003 16.82842712474619 2.42257287525381 17.5 3.2510000000000003 17.5 C 4.079427124746191 17.5 4.751 16.82842712474619 4.751 16 L 4.751 22 C 4.751 22.82842712474619 5.4225728752538105 23.5 6.251 23.5 C 7.079427124746191 23.5 7.751 22.82842712474619 7.751 22 C 7.751 22.82842712474619 8.42257287525381 23.5 9.251000000000001 23.5 C 10.079427124746191 23.5 10.751000000000001 22.82842712474619 10.751000000000001 22 L 10.751000000000001 11.809 C 10.751005817427709 11.63563594335736 10.840814761912378 11.474646972529763 10.988328627235113 11.383571423083268 C 11.135842492557847 11.292495873636774 11.320006715846516 11.284332902482765 11.475000000000001 11.362 L 12.757 12" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 6.73 0.44)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-18.49, -12.2)" d="M 20.643 22.684 C 20.554361874499403 23.157505555409777 20.140730174258113 23.500517209268406 19.659 23.5 L 17.32 23.5 C 16.838269825741886 23.500517209268406 16.424638125500596 23.157505555409777 16.336 22.684 L 15.075 15.955000000000002 C 14.1090942948702 10.807363850311678 15.182364023842332 5.48583936720226 18.06799999999999 1.115000000000009 C 18.163213369247007 0.9755641144317877 18.32115702008209 0.8921614131003675 18.49 0.8921614131003675 C 18.658842979917907 0.8921614131003675 18.81678663075299 0.9755641144317878 18.912 1.115000000000002 C 21.795506701753506 5.486704888907779 22.866940577433247 10.808016373207437 21.900000000000002 15.954999999999988 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 6.69 0.19)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-18.44, -11.94)" d="M 14.919 14.998 L 21.97 8.88" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 7.18 4.42)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-18.94, -16.17)" d="M 15.651 19.021 L 22.221 13.318" stroke-linecap="round" />
</g>
</g>
</g>
</svg>