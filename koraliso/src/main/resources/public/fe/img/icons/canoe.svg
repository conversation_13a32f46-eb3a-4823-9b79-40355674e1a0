<svg id='Canoe_2_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -6.25 -2.12)" id="Light" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.5, -9.63)" d="M 10.5 8.542 L 6.5 8.542 C 4.2170000000000005 8.542 2.224 7.6739999999999995 2.0620000000000003 6.157 C 2.0405850355651514 5.940734434149761 1.88192477530659 5.763067126295462 1.669448982252268 5.717422487615693 C 1.456973189197946 5.671777848935925 1.2393442650640567 5.768609671421277 1.1310000000000002 5.957 C 0.6962545040973825 6.74789285403491 0.47855166525789306 7.639749967188759 0.5 8.542000000000002 C 0.506604704035654 9.88105356576808 1.0457111742140839 11.162456395144371 1.9983744738361717 12.103488122380368 C 2.9510377734582596 13.044519849616364 4.238966335822295 13.567844811345427 5.578000000000003 13.558 L 6.5 13.542" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 5.75 -2.13)" id="Light" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-17.5, -9.62)" d="M 15.5 8.542 L 17.5 8.542 C 19.783 8.542 21.776 7.6739999999999995 21.938 6.157 C 21.959414964434846 5.9407344341497605 22.11807522469341 5.763067126295461 22.33055101774773 5.717422487615692 C 22.543026810802054 5.671777848935923 22.760655734935945 5.7686096714212765 22.869 5.957 C 23.30288788782151 6.746417537016938 23.520562892200736 7.636435779962111 23.5 8.536999999999995 C 23.5 11.298423749153965 21.261423749153966 13.536999999999999 18.5 13.536999999999999 L 11.5 13.536999999999999" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -7.13 7)" id="Light" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.62, -18.75)" d="M 3.789 16.7 C 4.240583705643918 16.146238335642344 4.95329258858589 15.875458495290198 5.658656110383529 15.98966062413412 C 6.364019632181168 16.10386275297804 6.954876294229809 16.585696830932545 7.208656110383529 17.253660624134117 C 7.462435926537251 17.921624417335693 7.340583705643919 18.674238335642347 6.888999999999999 19.227999999999998 L 5 21.542 L 1.9 19.014 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.54 -2.32)" id="Light" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="5.693999999999999" y1="-6.979500000000001" x2="-5.693999999999999" y2="6.979500000000001" />
</g>
</g>
</g>
</svg>