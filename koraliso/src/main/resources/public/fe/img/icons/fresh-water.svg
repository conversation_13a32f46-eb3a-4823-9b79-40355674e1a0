<svg id='Aquascaping_3_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -3.15 -5.58)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-8.86, -6.42)" d="M 9.04318 6.796 C 8.83608 6.796 8.66818 6.6281 8.66818 6.421 C 8.66818 6.21389 8.83608 6.046 9.04318 6.046" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -2.77 -5.58)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-9.23, -6.42)" d="M 9.04318 6.796 C 9.25029 6.796 9.41818 6.6281 9.41818 6.421 C 9.41818 6.21389 9.25029 6.046 9.04318 6.046" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -6.42 -9.36)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.58, -2.64)" d="M 5.582 0.75 C 5.20755 0.75 4.84152 0.861048 4.5302 1.0691 C 4.21887 1.27715 3.97624 1.57285 3.833 1.91881 C 3.68975 2.26477 3.65232 2.64544 3.72544 3.01267 C 3.79857 3.37991 3.97896 3.71721 4.2438 3.98191 C 4.50864 4.24661 4.84603 4.42682 5.2133 4.49975 C 5.58058 4.57268 5.96123 4.53505 6.30711 4.39162 C 6.65299 4.24819 6.94857 4.0054 7.15645 3.69397 C 7.36434 3.38253 7.47519 3.01644 7.475 2.642 C 7.475 2.39346 7.42602 2.14735 7.33088 1.91773 C 7.23574 1.68812 7.09628 1.4795 6.92049 1.3038 C 6.74469 1.1281 6.536 0.988756 6.30634 0.893733 C 6.07668 0.79871 5.83054 0.749869 5.582 0.75 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -0.01 -0.01)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -12)" d="M 10.6 0.837 C 11.0644 0.779305 11.532 0.75025 12 0.75 C 14.4791 0.750703 16.8885 1.57056 18.8535 3.08208 C 20.8185 4.5936 22.229 6.71207 22.8657 9.10804 C 23.5023 11.504 23.3294 14.0432 22.3738 16.3307 C 21.4183 18.6183 19.7336 20.526 17.5818 21.7572 C 15.43 22.9883 12.9317 23.474 10.4754 23.1386 C 8.01908 22.8032 5.74242 21.6656 3.99945 19.9026 C 2.25649 18.1397 1.1449 15.8502 0.837564 13.3902 C 0.530224 10.9302 1.04435 8.4376 2.3 6.3" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -0.01 5.89)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.99, -17.9)" d="M 3.173 18.975 C 9.336 14.092 15.534 18.987 20.817 18.987" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -4.1 1.56)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7.91, -13.57)" d="M 7.17301 10.31 C 6.92401 12.137 8.68801 13.499 8.66801 16.827" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.25 0.33)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-15.25, -12.34)" d="M 14.677 6.68 C 13.347 9.715 17.5 14.501 15.589 17.991" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 2.46 -3.01)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-14.47, -9)" d="M 11.929 8.6 C 12.905 11.317 16.739 10.656 17.006 7.676" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -4.35 1)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7.66, -13)" d="M 5.133 12.709 C 6.185 15.4 10 14.628 10.182 11.642" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.46 1.51)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-15.46, -13.52)" d="M 12.943 13.286 C 14.043 15.956 17.843 15.127 17.975 12.137" stroke-linecap="round" />
</g>
</g>
</g>
</svg>