<svg id='Camera_Flash_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.25 4.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -16.5)" d="M 22.5 22 C 22.5 22.82842712474619 21.82842712474619 23.5 21 23.5 L 3 23.5 C 2.1715728752538097 23.5 1.5 22.82842712474619 1.5 22 L 1.5 13 C 1.5 12.17157287525381 2.1715728752538097 11.5 3 11.5 L 8 11.5 C 8.63292711229546 11.465678248486698 9.235157326141506 11.216248394245834 9.707 10.793 L 11 9.5 L 17 9.5 L 18.293 10.793 C 18.764842673858492 11.216248394245834 19.367072887704538 11.465678248486698 20 11.5 L 21 11.5 C 21.82842712474619 11.5 22.5 12.17157287525381 22.5 13 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -6.75 -1.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5, -10.5)" d="M 3.5 11.5 L 3.5 10.5 C 3.5 9.947715250169207 3.9477152501692068 9.5 4.5 9.5 L 5.5 9.5 C 6.052284749830793 9.5 6.5 9.947715250169207 6.5 10.5 L 6.5 11.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -6.75 3.25)" >
<circle style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="1.5" />
</g>
<g transform="matrix(1 0 0 1 2.75 5.25)" >
<circle style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="2.5" />
</g>
<g transform="matrix(1 0 0 1 2.75 5.25)" >
<circle style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="4.5" />
</g>
<g transform="matrix(1 0 0 1 2.25 -8.25)" >
<rect style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x="-5.5" y="-3" rx="1" ry="1" width="11" height="6" />
</g>
<g transform="matrix(1 0 0 1 2.25 -8.25)" >
<rect style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x="-3.5" y="-1" rx="0" ry="0" width="7" height="2" />
</g>
<g transform="matrix(1 0 0 1 -1.25 -3.75)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-0.5" y1="-1.5" x2="0.5" y2="1.5" />
</g>
<g transform="matrix(1 0 0 1 5.75 -3.75)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0.5" y1="-1.5" x2="-0.5" y2="1.5" />
</g>
</g>
</g>
</svg>