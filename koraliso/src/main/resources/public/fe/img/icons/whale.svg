<svg id='Whale_Water_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.28 -0.99)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12.03, -10.76)" d="M 0.639999 15.25 C 1.13433 14.353 1.85737 13.6029 2.7356 13.076 C 3.61382 12.5491 4.61592 12.2641 5.64 12.25 C 10.49 12.25 11.97 16.14 15 15.75 C 15.6848 15.6137 16.327 15.3157 16.8732 14.8808 C 17.4194 14.446 17.8538 13.8868 18.14 13.25 C 18.6762 12.3295 19.0167 11.3082 19.14 10.25 C 18.6829 10.0791 18.2679 9.81217 17.9229 9.46714 C 17.5778 9.12211 17.3109 8.70705 17.14 8.25 C 16.9925 7.82662 16.961 7.37144 17.049 6.93179 C 17.1369 6.49214 17.341 6.08407 17.64 5.75 L 20.14 7.75 L 23.14 6.25 C 23.3286 6.63972 23.4265 7.06706 23.4265 7.5 C 23.4265 7.93294 23.3286 8.36028 23.14 8.75 C 22.9995 9.17422 22.7326 9.5453 22.3751 9.81342 C 22.0176 10.0816 21.5866 10.2339 21.14 10.25 C 21.14 10.25 21.14 14.25 20.64 15.25" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -8.61 -6.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3.14, -5.5)" d="M 0.639999 6.75 C 0.639999 6.4217 0.704664 6.09661 0.830301 5.79329 C 0.955937 5.48998 1.14009 5.21438 1.37223 4.98223 C 1.60438 4.75009 1.87998 4.56594 2.18329 4.4403 C 2.4866 4.31466 2.81169 4.25 3.14 4.25 C 3.4683 4.25 3.79339 4.31466 4.09671 4.4403 C 4.40002 4.56594 4.67562 4.75009 4.90777 4.98223 C 5.13991 5.21438 5.32406 5.48998 5.4497 5.79329 C 5.57534 6.09661 5.64 6.4217 5.64 6.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -3.61 -4.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-8.14, -7.25)" d="M 10.64 6.75 C 10.64 6.08696 10.3766 5.45107 9.90777 4.98223 C 9.43893 4.51339 8.80304 4.25 8.14 4.25 C 7.47696 4.25 6.84107 4.51339 6.37223 4.98223 C 5.90339 5.45107 5.64 6.08696 5.64 6.75 L 5.64 10.25" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.23 6.93)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.98, -18.69)" d="M 0.5 19.6401 C 3.01 20.1601 4.56 17.6401 4.56 17.6401 C 4.94565 18.2564 5.47831 18.7672 6.11014 19.1268 C 6.74198 19.4863 7.45322 19.6834 8.18 19.7001 C 8.86586 19.6856 9.53516 19.4867 10.1176 19.1242 C 10.7 18.7617 11.1741 18.2491 11.49 17.6401 C 11.9573 18.2471 12.5506 18.7456 13.229 19.1012 C 13.9075 19.4568 14.655 19.6612 15.42 19.7001 C 16.1342 19.6618 16.8297 19.4575 17.4512 19.1034 C 18.0726 18.7493 18.603 18.2551 19 17.6601 C 19 17.6601 21 20.2001 23.46 19.6601" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -6.99 2.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.76, -14.5)" d="M 4.89 14.75 C 4.75193 14.75 4.64 14.6381 4.64 14.5 C 4.64 14.3619 4.75193 14.25 4.89 14.25" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -6.74 2.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.01, -14.5)" d="M 4.89 14.75 C 5.02807 14.75 5.14 14.6381 5.14 14.5 C 5.14 14.3619 5.02807 14.25 4.89 14.25" stroke-linecap="round" />
</g>
</g>
</g>
</svg>