<svg id='Canoe_3_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.25 -3.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -8.5)" d="M 7.5 8.5 L 16.5 8.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 0.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -12.5)" d="M 7.5 12.5 L 16.5 12.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 4.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -16.5)" d="M 7.5 16.5 L 16.5 16.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 9.75 9.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-21.5, -21.01)" d="M 19.5 20.516 C 19.5 19.41143050033841 20.395430500338414 18.516 21.5 18.516 C 22.604569499661586 18.516 23.5 19.41143050033841 23.5 20.516 L 23.5 23.5 L 19.5 23.5 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 9.75 -2.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-21.5, -9.51)" d="M 21.5 0.5 L 21.5 18.516" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -9.25 -8.76)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-2.5, -2.99)" d="M 4.5 3.484 C 4.5 4.5885694996615864 3.604569499661587 5.484 2.5 5.484 C 1.395430500338413 5.484 0.5 4.5885694996615864 0.5 3.484 L 0.5 0.5 L 4.5 0.5 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -9.25 2.74)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-2.5, -14.49)" d="M 2.5 23.5 L 2.5 5.484" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 0.79)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -12.54)" d="M 5.5 7.814 C 5.5011883370635495 6.9782649764079565 5.850938692744393 6.180906652575981 6.465 5.614 L 9.965 2.383 C 11.114230955648585 1.3220172204705138 12.885769044351413 1.3220172204705138 14.035 2.3829999999999996 L 17.535 5.614 C 18.149061307255607 6.180906652575981 18.49881166293645 6.9782649764079565 18.5 7.814 L 18.5 17 C 18.5 20.589850873900158 15.589850873900158 23.5 12 23.5 C 8.410149126099842 23.5 5.5 20.589850873900158 5.5 17 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 0.78)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -12.53)" d="M 7.5 8.41 C 7.497433508906506 7.829458725271422 7.737839168185485 7.274313831823282 8.163 6.879000000000001 L 10.59 4.635 C 11.387438041328775 3.8979374820007493 12.617561958671224 3.8979374820007493 13.415 4.635 L 15.838 6.879 C 16.26330373091606 7.274127521157532 16.50343888360955 7.829485404501542 16.5 8.409999999999998 L 16.5 16.485 C 16.5 18.97028137423857 14.485281374238571 20.985 12 20.985 C 9.514718625761429 20.985 7.5 18.97028137423857 7.5 16.485 Z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>