<svg id='Marine_Mammal_Dolphin_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.25 10.24)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -21.99)" d="M 23.5 21.9941 C 23 22.4941 21.528 24.3601 19 20.9941 C 18.6245 21.5881 18.109 22.081 17.4989 22.4297 C 16.8887 22.7784 16.2024 22.9722 15.5 22.9941 C 14.7939 22.9899 14.1008 22.8036 13.4877 22.4532 C 12.8746 22.1029 12.3622 21.6003 12 20.9941 C 11.6378 21.6003 11.1254 22.1029 10.5123 22.4532 C 9.89923 22.8036 9.20613 22.9899 8.5 22.9941 C 7.79759 22.9722 7.11127 22.7784 6.50111 22.4297 C 5.89095 22.081 5.37555 21.5881 5 20.9941 C 2.472 24.3601 1 22.4941 0.5 21.9941" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.24 -2.14)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.99, -9.62)" d="M 20.712 14.026 C 20.712 7.87801 17.16 4.57401 13.887 3.34201 C 13.9752 2.78302 14.1481 2.24077 14.4 1.73401 C 14.4396 1.65687 14.4584 1.57078 14.4546 1.48418 C 14.4509 1.39758 14.4246 1.31344 14.3786 1.24002 C 14.3325 1.16661 14.2681 1.10643 14.1917 1.06541 C 14.1154 1.02438 14.0297 1.00391 13.943 1.00601 C 9.586 1.10601 8.293 1.93301 7.58 3.06001 C 3.664 4.02701 1.744 7.00801 2.611 9.32101 L 0.910999 11.548 C 0.410999 12.253 1.501 12.908 2.219 12.405 C 3.26824 11.6665 4.38684 11.0318 5.559 10.51 C 6.25909 11.7786 7.4191 12.7303 8.8 13.169 C 8.89073 13.1924 8.98619 13.1898 9.07551 13.1615 C 9.16483 13.1332 9.24441 13.0804 9.30518 13.0091 C 9.36595 12.9378 9.40547 12.8509 9.41923 12.7582 C 9.43299 12.6656 9.42043 12.5709 9.383 12.485 C 9.01032 11.5422 8.80263 10.5423 8.769 9.52901 C 12.345 8.93801 15.855 10.143 18.536 14.388 C 17.4951 15.2562 16.7861 16.4572 16.529 17.788 C 16.5126 17.8608 16.519 17.9369 16.5473 18.0059 C 16.5756 18.0749 16.6246 18.1335 16.6874 18.1737 C 16.7502 18.2139 16.824 18.2338 16.8985 18.2306 C 16.973 18.2275 17.0448 18.2014 17.104 18.156 L 19.627 16.2 L 22.648 18.012 C 22.707 18.0469 22.7746 18.0644 22.8431 18.0624 C 22.9116 18.0605 22.9782 18.0392 23.0351 18.001 C 23.092 17.9628 23.1369 17.9093 23.1647 17.8467 C 23.1925 17.784 23.2019 17.7148 23.192 17.647 C 23.0516 16.9047 22.7587 16.1996 22.3318 15.5764 C 21.905 14.9531 21.3534 14.4252 20.712 14.026 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.38 -4.85)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.38, -6.91)" d="M 6.5 7.15723 C 6.36193 7.15723 6.25 7.0453 6.25 6.90723 C 6.25 6.76916 6.36193 6.65723 6.5 6.65723" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.13 -4.85)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.63, -6.91)" d="M 6.5 7.15723 C 6.63807 7.15723 6.75 7.0453 6.75 6.90723 C 6.75 6.76916 6.63807 6.65723 6.5 6.65723" stroke-linecap="round" />
</g>
</g>
</g>
</svg>