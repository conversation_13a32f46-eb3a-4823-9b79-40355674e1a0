<svg id='Sea_Transport_Cruiser_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -3.5 -0.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-8.25, -11.5)" d="M 16 14.5 C 15.98027932209443 13.123231760832617 15.637765269706746 11.770301253901264 15 10.55 C 14.316765008562951 9.277235421278451 12.984504441404308 8.48789028755729 11.539999999999997 8.5 L 0.5 8.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 5.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -17.5)" d="M 20.5 20.5 C 22.245370946524233 19.20194880759663 23.34024891855912 17.20927089849313 23.499999999999996 15.040000000000003 C 23.51118787708613 14.900608779525166 23.46349117912197 14.762902128484292 23.368483111412864 14.660293415358458 C 23.27347504370376 14.557684702232624 23.139838764296883 14.499551737712782 23 14.5 L 12.9 14.5 C 12.41413576071704 14.491163619404626 11.992249124645193 14.832891794622821 11.9 15.31 C 11.649164006465844 17.036059294826618 10.238527617872153 18.359568141771874 8.5 18.5 L 0.5 18.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.56 3.75)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="5.69" y1="0" x2="-5.69" y2="0" />
</g>
<g transform="matrix(1 0 0 1 -9.75 5.25)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0" y1="-1.5" x2="0" y2="1.5" />
</g>
<g transform="matrix(1 0 0 1 -4.75 5.25)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0" y1="-1.5" x2="0" y2="1.5" />
</g>
<g transform="matrix(1 0 0 1 -7.25 -10.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.5, -1.25)" d="M 0.5 0.87 C 0.9311175499567164 1.3558649590652867 1.5504466268472121 1.6327414875575077 2.200000000000001 1.6299999999999994 C 2.6515391987762076 1.631312513261405 3.093389019744521 1.4991054802157677 3.4699999999999998 1.25 L 3.88 1 C 4.399314090459473 0.684810265886051 4.992645360529723 0.5123302455167922 5.599999999999998 0.5 C 6.760510636216719 0.4670902436252717 7.8562447806158655 1.0338492838317266 8.5 1.9999999999999987" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -2.75 0.25)" >
<circle style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="1.5" />
</g>
<g transform="matrix(1 0 0 1 -8.75 0.25)" >
<circle style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="1.5" />
</g>
<g transform="matrix(1 0 0 1 -2.75 -5.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-9, -6.5)" d="M 7.5 8.5 L 7.5 5 C 7.5 4.723857625084603 7.723857625084603 4.5 8 4.5 L 10 4.5 C 10.276142374915397 4.5 10.5 4.723857625084603 10.5 5 L 10.5 8.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -2.75 -5.25)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-1.5" y1="0" x2="1.5" y2="0" />
</g>
<g transform="matrix(1 0 0 1 -8.75 -5.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3, -6.5)" d="M 1.5 8.5 L 1.5 5 C 1.5 4.723857625084603 1.7238576250846034 4.5 2 4.5 L 4 4.5 C 4.276142374915397 4.5 4.5 4.723857625084603 4.5 5 L 4.5 8.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -8.75 -5.25)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-1.5" y1="0" x2="1.5" y2="0" />
</g>
<g transform="matrix(1 0 0 1 0.25 10.71)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -22.47)" d="M 0.5 23.43 C 2.95 23.95 5 21.43 5 21.43 C 5.758070557983144 22.635855603931766 7.06634766149496 23.38558460307894 8.489999999999998 23.43 C 9.929514380185484 23.42402487873325 11.260985269871728 22.665352007117157 12 21.43 C 12.731032874190396 22.663652310135955 14.05603430982115 23.422965453763897 15.489999999999998 23.43 C 16.918846900787383 23.385152391286887 18.23298205736649 22.63635743027315 19 21.429999999999996 C 19 21.43 21.05 23.95 23.5 23.43" stroke-linecap="round" />
</g>
</g>
</g>
</svg>