<svg id='Style_Three_Pin_Boat_Skating_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.25 0.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -12)" d="M 12 0.5 C 14.163511481483456 0.4972021969327435 16.28299278250621 1.1111926883910541 18.109999999999996 2.269999999999998 C 22.86 5.27 23.21 11.5 19.43 16.05 C 17.18286422713449 18.75213007174104 14.696079631950811 21.2456085689306 12.000000000000007 23.499999999999996 C 9.303920368049198 21.245608568930603 6.817135772865516 18.752130071741036 4.570000000000007 16.049999999999997 C 0.79 11.5 1.14 5.28 5.89 2.27 C 7.717007217493788 1.1111926883910548 9.836488518516544 0.4972021969327447 12 0.5000000000000004 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -2.25 -0.75)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-3.5" y1="1" x2="3.5" y2="-1" />
</g>
<g transform="matrix(1 0 0 1 -3.25 -6.25)" >
<circle style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="1.5" />
</g>
<g transform="matrix(1 0 0 1 4.25 -3.75)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-4" y1="0" x2="4" y2="0" />
</g>
<g transform="matrix(1 0 0 1 -3.14 -2.7)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-8.61, -9.05)" d="M 8.5 7 C 7.724720022079371 8.351141293140207 7.999870972947398 10.060412351562801 9.16 11.100000000000001" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.19 2)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.94, -13.75)" d="M 17.38 14.44 C 16.18443848497609 14.58800442254489 15.012330865018942 14.025392764965458 14.379999999999999 13 L 14.379999999999999 13 C 13.808344982856841 13.9122499307619 12.81629425035176 14.475915119685242 11.739999999999998 14.5 C 10.686213011628677 14.434886017180315 9.723847767319445 13.879675299309604 9.14 13 L 9.14 13 C 8.568344982856843 13.9122499307619 7.576294250351761 14.475915119685242 6.5 14.5" stroke-linecap="round" />
</g>
</g>
</g>
</svg>