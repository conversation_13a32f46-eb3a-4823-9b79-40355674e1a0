<svg id='Avatar_Diver_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 8.37 8.11)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-20.13, -19.87)" d="M 18.5 16.233 C 19.5238 17.1464 20.3427 18.2662 20.9028 19.5186 C 21.463 20.7711 21.7517 22.128 21.75 23.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.25 -7.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13, -4.5)" d="M 20 0.5 L 20 6.5 C 19.9984 7.02995 19.7872 7.53773 19.4125 7.91247 C 19.0377 8.2872 18.5299 8.49842 18 8.5 L 13 8.5 C 12.7348 8.5 12.4804 8.39464 12.2929 8.20711 C 12.1054 8.01957 12 7.76522 12 7.5 C 12 7.76522 11.8946 8.01957 11.7071 8.20711 C 11.5196 8.39464 11.2652 8.5 11 8.5 L 8 8.5 C 7.46957 8.5 6.96086 8.28929 6.58579 7.91421 C 6.21071 7.53914 6 7.03043 6 6.5 L 6 5.536 C 6 5.27078 6.10536 5.01643 6.29289 4.82889 C 6.48043 4.64136 6.73478 4.536 7 4.536 L 16.605 4.536 C 16.8702 4.536 17.1246 4.64136 17.3121 4.82889 C 17.4996 5.01643 17.605 5.27078 17.605 5.536 L 17.605 6.8" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 4.96)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -16.72)" d="M 15.5 15.685 C 15.1541 16.3102 14.647 16.8313 14.0315 17.1942 C 13.416 17.5571 12.7145 17.7485 12 17.7485 C 11.2855 17.7485 10.584 17.5571 9.96849 17.1942 C 9.35299 16.8313 8.84592 16.3102 8.5 15.685" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -4.75 6.62)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -18.38)" d="M 5.5 23.5 L 5.5 14.75 C 5.5 14.3522 5.65804 13.9706 5.93934 13.6893 C 6.22064 13.408 6.60218 13.25 7 13.25 C 7.39782 13.25 7.77936 13.408 8.06066 13.6893 C 8.34196 13.9706 8.5 14.3522 8.5 14.75 L 8.5 23.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 5.25 6.62)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-17, -18.38)" d="M 18.5 23.5 L 18.5 14.75 C 18.5 14.3522 18.342 13.9706 18.0607 13.6893 C 17.7794 13.408 17.3978 13.25 17 13.25 C 16.6022 13.25 16.2206 13.408 15.9393 13.6893 C 15.658 13.9706 15.5 14.3522 15.5 14.75 L 15.5 23.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -7.88 8.11)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3.87, -19.87)" d="M 2.25 23.5 C 2.24833 22.128 2.53705 20.7711 3.09719 19.5186 C 3.65734 18.2662 4.47621 17.1464 5.5 16.233" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 2.33)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -14.08)" d="M 8.461 14.412 C 10.7378 13.5294 13.2622 13.5294 15.539 14.412" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.24 -8.94)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -2.81)" d="M 16.992 4.62101 C 16.6691 3.6302 16.0588 2.75785 15.2386 2.11501 C 14.4184 1.47217 13.4255 1.08791 12.3862 1.01113 C 11.347 0.934355 10.3084 1.16854 9.40263 1.68388 C 8.49688 2.19921 7.76495 2.9724 7.3 3.90501 L 7 4.53601" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -3.06 -2)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-8.69, -9.75)" d="M 7.175 8.32199 C 7.45917 8.9818 7.8754 9.57645 8.39804 10.0693 C 8.92069 10.5622 9.53869 10.9429 10.214 11.188" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.24 -1.44)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -10.31)" d="M 11.996 12.297 C 13.0934 12.297 13.983 11.4074 13.983 10.31 C 13.983 9.21261 13.0934 8.323 11.996 8.323 C 10.8986 8.323 10.009 9.21261 10.009 10.31 C 10.009 11.4074 10.8986 12.297 11.996 12.297 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -2.33 -5.28)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-9.42, -6.47)" d="M 9.544 6.72198 C 9.40593 6.72198 9.294 6.61006 9.294 6.47198 C 9.294 6.33391 9.40593 6.22198 9.544 6.22198" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -2.08 -5.28)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-9.67, -6.47)" d="M 9.544 6.72198 C 9.68207 6.72198 9.794 6.61006 9.794 6.47198 C 9.794 6.33391 9.68207 6.22198 9.544 6.22198" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 2.61 -5.28)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-14.36, -6.47)" d="M 14.484 6.72198 C 14.3459 6.72198 14.234 6.61006 14.234 6.47198 C 14.234 6.33391 14.3459 6.22198 14.484 6.22198" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 2.86 -5.28)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-14.61, -6.47)" d="M 14.484 6.72198 C 14.6221 6.72198 14.734 6.61006 14.734 6.47198 C 14.734 6.33391 14.6221 6.22198 14.484 6.22198" stroke-linecap="round" />
</g>
</g>
</g>
</svg>