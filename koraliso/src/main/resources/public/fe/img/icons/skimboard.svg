<svg id='Paddle_Board_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'>
  <rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>
  <g transform="translate(2.5, 0)">
    <g transform="matrix(0.67 0 0 0.67 10 10)">
      <g>
        <g transform="matrix(1 0 0 1 -3.95 0)">
          <path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; fill: none;" transform=" translate(-8.05, -12)" d="M 9.75 23.25 C 10.15 23.25 10.45 23.05 10.65 22.75 C 12.05 20.05 12.85 16.85 12.85 13.35 C 12.85 8.55001 11.35 4.14999 8.85 1.14999 C 8.65 0.949994 8.35 0.75 8.05 0.75 C 7.75 0.75 7.45 0.849994 7.25 1.14999 C 4.85 4.14999 3.25 8.55001 3.25 13.35 C 3.25 16.85 4.05 20.05 5.45 22.75 C 5.65 23.05 5.95 23.25 6.35 23.25 L 9.75 23.25 Z" />
        </g>
        <g transform="matrix(1 0 0 1 -3.95 0.81)">
          <path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; fill: none;" transform=" translate(-8.05, -12.82)" d="M 3.30304 14.7891 L 8.04685 10.8438 L 12.797 14.7891" />
        </g>
        <g transform="matrix(1 0 0 1 -3.95 5.78)">
          <path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; fill: none;" transform=" translate(-8.05, -17.79)" d="M 4.11334 19.4219 L 8.04738 16.15 L 11.9867 19.4219" />
        </g>
      </g>
    </g>
  </g>
</svg>
