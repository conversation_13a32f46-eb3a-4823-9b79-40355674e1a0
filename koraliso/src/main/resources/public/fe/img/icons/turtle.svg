<svg id='Marine_Mammal_Turtle_1_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.25 2.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -14.25)" d="M 17.5 13.75 C 17.5 18.75 13 21.25 12 21.25 C 11 21.25 6.5 18.75 6.5 13.75 C 6.5 10.16 8.96 7.25 12 7.25 C 15.04 7.25 17.5 10.16 17.5 13.75 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 -0.72)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -11.03)" d="M 7.79 9.56 L 11.4 12.3 C 11.755555555555556 12.566666666666666 12.244444444444444 12.566666666666666 12.6 12.3 L 16.21 9.56" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -3.11 1.86)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="1.94" y1="-1.9400000000000004" x2="-1.94" y2="1.9400000000000004" />
</g>
<g transform="matrix(1 0 0 1 3.61 1.86)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-1.9400000000000004" y1="-1.9400000000000004" x2="1.9400000000000004" y2="1.9400000000000004" />
</g>
<g transform="matrix(1 0 0 1 -1.99 5.73)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="1.3650000000000002" y1="-1.3650000000000002" x2="-1.3650000000000002" y2="1.3650000000000002" />
</g>
<g transform="matrix(1 0 0 1 2.48 5.73)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-1.3650000000000002" y1="-1.3650000000000002" x2="1.3650000000000002" y2="1.3650000000000002" />
</g>
<g transform="matrix(1 0 0 1 0.25 3.48)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -15.24)" d="M 8.21 14 L 11.420000000000002 16.29 C 11.767164308452076 16.53717803443113 12.232835691547928 16.53717803443113 12.580000000000002 16.29 L 15.79 14" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 -7.7)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -4.05)" d="M 13 7.36 C 12.991260109451135 6.435630376759396 13.25186750536149 5.528716638991359 13.75 4.749999999999999 C 13.915033785372644 4.371469002239721 14.00014338814952 3.9629429089107298 14 3.5500000000000003 C 14.038982100333733 2.2730668428174634 13.220552586632259 1.1272655236354 12 0.7500000000000004 C 10.779447413367741 1.1272655236353997 9.961017899666267 2.273066842817464 10 3.5500000000000003 C 9.999856611850483 3.9629429089107298 10.084966214627356 4.371469002239721 10.25 4.75 C 10.724691220272781 5.498713511461053 10.984165966579448 6.363629332483278 11 7.25 L 11 7.36" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 7.51 -2.45)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-19.26, -9.31)" d="M 15 8.29 C 15.714906503243265 7.0253920967347945 17.06768457128646 6.256768194437523 18.52 6.289999999999999 C 22.02 6.289999999999999 23.52 12.29 23.52 12.29 C 21.62086617875096 12.518488862018641 19.792394241736062 11.490984720421773 19 9.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -7.01 -2.45)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.74, -9.31)" d="M 9 8.29 C 8.285093496756735 7.025392096734794 6.9323154287135385 6.256768194437523 5.4799999999999995 6.289999999999999 C 1.9800000000000004 6.289999999999999 0.4800000000000004 12.29 0.4800000000000004 12.29 C 2.37913382124904 12.518488862018641 4.207605758263936 11.490984720421773 5 9.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -6.21 8.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.54, -20)" d="M 7.09 16.75 C 7.09 16.75 4 17.25 4 19.75 C 3.9870926252328247 21.19187085117001 4.751219830264044 22.52909345997464 6.000000000000001 23.25 C 6.639703396495559 22.19347951310058 6.984982626587102 20.98500220778018 7 19.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 6.7 8.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-18.46, -20)" d="M 16.91 16.75 C 16.91 16.75 20 17.25 20 19.75 C 20.012907374767174 21.19187085117001 19.24878016973596 22.52909345997464 18 23.25 C 17.36029660350444 22.19347951310058 17.0150173734129 20.98500220778018 17 19.75" stroke-linecap="round" />
</g>
</g>
</g>
</svg>