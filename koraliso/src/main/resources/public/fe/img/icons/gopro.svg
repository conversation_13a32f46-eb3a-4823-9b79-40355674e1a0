<svg id='Go_Pro_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.25 -2.75)" >
<rect style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x="-11.5" y="-8" rx="2" ry="2" width="23" height="16" />
</g>
<g transform="matrix(1 0 0 1 -5.75 -6.25)" >
<rect style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x="-3.5" y="-2.5" rx="1" ry="1" width="7" height="5" />
</g>
<g transform="matrix(1 0 0 1 5.75 -4.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-17.5, -7)" d="M 11.5 1 L 21.5 1 C 22.604569499661586 1 23.5 1.8954305003384126 23.5 2.9999999999999996 L 23.5 13 L 13.5 13 C 12.395430500338414 13 11.5 12.104569499661586 11.5 11 L 11.5 1 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 5.75 -4.75)" >
<circle style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="4" />
</g>
<g transform="matrix(1 0 0 1 5.75 -4.75)" >
<circle style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="2" />
</g>
<g transform="matrix(1 0 0 1 0.25 8.25)" >
<rect style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x="-1.5" y="-3" rx="0" ry="0" width="3" height="6" />
</g>
<g transform="matrix(1 0 0 1 0.25 11.25)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-4.5" y1="0" x2="4.5" y2="0" />
</g>
<g transform="matrix(1 0 0 1 -7.25 -6.25)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0" y1="-0.5" x2="0" y2="0.5" />
</g>
<g transform="matrix(1 0 0 1 -5.75 1.75)" >
<circle style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="1.5" />
</g>
<g transform="matrix(1 0 0 1 -3.75 8.25)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="2.5" y1="0" x2="-2.5" y2="0" />
</g>
<g transform="matrix(1 0 0 1 -8.75 8.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3, -20)" d="M 1.5 19 C 0.9477152501692065 19 0.5 19.447715250169207 0.5 20 C 0.5 20.552284749830793 0.9477152501692065 21 1.5 21 L 5.5 21 L 5.5 19 Z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>