<svg id='Lighthouse_Bird_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.25 10.7)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -22.45)" d="M 23.5 23.388 C 21 23.909000000000002 19.44 21.409000000000002 19.44 21.409000000000002 L 19.44 21.409000000000002 C 18.65322815707497 22.65772071687455 17.293485658828573 23.429567921842686 15.818000000000001 23.465000000000003 C 14.353550535809868 23.394413000948205 13.010140010458825 22.630993421036166 12.2 21.409 L 12.2 21.409 C 11.414385815303438 22.65794347495805 10.055067050005011 23.42997645326014 8.579999999999998 23.465 C 7.114509554755051 23.39615442769181 5.769757554539665 22.632394728121966 4.96 21.409 L 4.96 21.409 C 4.96 21.409 2.96 23.951 0.5049999999999999 23.429" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.75 -10.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13.5, -1.5)" d="M 13.499 0.5 L 13.499 2.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.63 1.21)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13.38, -12.97)" d="M 8.157 20.434 L 10.499 9.5 L 10.499 5.5 L 16.499 5.5 L 16.499 9.5 L 18.612 19.359" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.75 -7.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13.5, -4)" d="M 10.5 5.5 C 10.5 3.8431457505076194 11.84314575050762 2.5 13.5 2.5 C 15.15685424949238 2.5 16.5 3.843145750507619 16.5 5.499999999999999" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.75 -2.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13.5, -9.5)" d="M 8.999 9.5 L 17.999 9.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.75 1.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13.5, -13.5)" d="M 9.642 13.5 L 17.356 13.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.75 6.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13.5, -18.5)" d="M 8.571 18.5 L 18.428 18.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.75 -0.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13.5, -11.5)" d="M 13.499 11 L 13.499 12" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.75 4.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13.5, -16)" d="M 13.499 15.5 L 13.499 16.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -4.75 -10.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -1.25)" d="M 5.499 0.5 L 6.999 2 L 8.499 0.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -8.75 -7)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3, -4.75)" d="M 1.499 4 L 2.999 5.5 L 4.499 4" stroke-linecap="round" />
</g>
</g>
</g>
</svg>