<svg id='Camera_Holder_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.25 -4.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -7.5)" d="M 17.5 11 C 17.5 11.552284749830793 17.052284749830793 12 16.5 12 L 6.5 12 L 6.5 4 C 6.5 3.4477152501692068 6.947715250169207 3 7.5 3 L 16.5 3 C 17.052284749830793 3 17.5 3.4477152501692063 17.5 4 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 7.25 -5.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-19, -6.5)" d="M 17.5 5 L 20.5 5 L 20.5 8 L 17.5 8 z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -7.2 -4.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.55, -7.5)" d="M 2.6 6 L 6.5 6 L 6.5 9 L 2.6 9" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 10.25 -5.26)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-22, -6.5)" d="M 23.5 8.127 C 23.49993323112401 8.495396486485665 23.297321980426315 8.833921057240266 22.972678017454964 9.008053974919038 C 22.648034054483613 9.182186892597812 22.253941047158747 9.163723266423247 21.947 8.96 L 20.5 8 L 20.5 5 L 21.944 4.033 C 22.250742071820476 3.827812039112728 22.645560628795806 3.808244352162973 22.97108837629064 3.9820962532993534 C 23.296616123785476 4.155948154435734 23.499932731061843 4.4949569220366214 23.5 4.864000000000001 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -9.92 -4.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-1.83, -7.5)" d="M 2.291 9.514 C 2.110489839853796 9.815245080518531 1.785187185712167 9.999722431642024 1.434 10 L 1 10 C 0.7238576250846033 10 0.5 9.776142374915397 0.5 9.5 L 0.5 5.5 C 0.5 5.223857625084603 0.7238576250846033 5 1 5 L 1.434 5 C 1.7851871857121668 5.000277568357976 2.110489839853796 5.184754919481468 2.2910000000000004 5.486 L 2.883 6.471 C 3.263025656120606 7.104368987131962 3.2630256561206057 7.895631012868038 2.883 8.529 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -1.75 4.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10, -16.5)" d="M 13.5 12 L 13.5 13 C 13.5 13.82842712474619 12.82842712474619 14.5 12 14.5 C 10.619288125423017 14.5 9.5 15.619288125423017 9.5 17 L 9.5 19.5 C 9.5 20.32842712474619 8.82842712474619 21 8 21 C 7.17157287525381 21 6.5 20.32842712474619 6.5 19.5 L 6.5 12" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -1.75 -5.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10, -6.5)" d="M 8.5 6.5 C 8.5 7.32842712474619 9.17157287525381 8 10 8 C 10.82842712474619 8 11.5 7.32842712474619 11.5 6.5 C 11.5 5.67157287525381 10.82842712474619 5 10 5 C 9.17157287525381 5 8.5 5.671572875253809 8.5 6.5 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 2.75 -1.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-14.5, -10)" d="M 15.5 10 L 13.5 10" stroke-linecap="round" />
</g>
</g>
</g>
</svg>