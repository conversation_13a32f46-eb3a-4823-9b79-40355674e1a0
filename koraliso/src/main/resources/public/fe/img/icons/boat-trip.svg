<svg id='Sailing_Boat_Water_1_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.25 10.23)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -21.98)" d="M 23.5 22.915 C 21 23.435 19.44 20.935 19.44 20.935 L 19.44 20.935 C 18.65442622605261 22.184616841784838 17.29461840780698 22.95708982773658 15.819 22.991999999999997 C 14.354007348009599 22.921497973596114 13.010124529973695 22.157649684925254 12.2 20.935 L 12.2 20.935 C 11.41462049294249 22.184325784094813 10.055260607589595 22.95675763055086 8.579999999999998 22.991999999999997 C 7.114648575740256 22.921810366390055 5.77032998581085 22.157925465979893 4.96 20.935 L 4.96 20.935 C 4.96 20.935 2.96 23.476999999999997 0.5049999999999999 22.956" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -4.14 -2.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7.62, -9.5)" d="M 12.5 18.027 C 12.613255707208493 12.260513104969611 11.467997482379927 6.538658320791793 9.144000000000013 1.2600000000000087 C 9.053492189777085 1.0668755882447583 8.85044422477135 0.9524233321803681 8.63836015607953 0.974985467147583 C 8.426276087387711 0.997547602114798 8.251851649060825 1.1521564968315945 8.204 1.36 L 8.114 1.766 C 7.153 6.1 5.826 10.051 2.859 13.186 C 2.72232616337936 13.330959656027474 2.6847542642071396 13.543225621963414 2.763366272444291 13.72629170744385 C 2.841978280681442 13.90935779292429 3.0217701669645987 14.028283675426248 3.221 14.029 L 12.338000000000001 14.051" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.31 7.27)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12.06, -19.03)" d="M 21.5 20.027 L 22.138 18.75 C 22.215192136176693 18.59509102457335 22.206781981635128 18.41127829152332 22.115766586595242 18.26406547135813 C 22.024751191555353 18.116852651192943 21.86407623929662 18.027178734988798 21.691000000000003 18.027 L 2.435 18.027 C 2.250724144736996 18.02709519281221 2.081437680389925 18.128542185313975 1.9944592815097293 18.290999435145494 C 1.9074808826295337 18.453456684977013 1.916911137975718 18.650587275152386 2.019 18.804000000000002 L 2.5010000000000003 19.527" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.21 -1.7)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-14.97, -10.05)" d="M 10.615 5.07 C 10.615 5.07 17.066 7.84 19.294 14.370000000000001 C 19.346306647941006 14.52273171103662 19.32165068847623 14.691321283326074 19.22779666841901 14.822677226831106 C 19.133942648361792 14.954033170336135 18.982440270348143 15.03199078496152 18.821 15.032000000000002 L 12.409 15.032000000000002" stroke-linecap="round" />
</g>
</g>
</g>
</svg>