<svg id='Standup_Paddle_Board_1_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.3 -8.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12.05, -3.25)" d="M 12.05 5.75 C 13.4308 5.75 14.55 4.63071 14.55 3.25 C 14.55 1.86929 13.4308 0.75 12.05 0.75 C 10.6693 0.75 9.55005 1.86929 9.55005 3.25 C 9.55005 4.63071 10.6693 5.75 12.05 5.75 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -0.02 1.71)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.73, -13.47)" d="M 9.75 14.05 L 11.05 12.15 C 9.80382 12.7251 8.02743 12.9887 6.75 12.35 C 5.95 12.05 5.55 11.25 5.75 10.45 C 6.05 9.64998 6.85 9.24998 7.65 9.44998 L 7.85 9.54995 C 8.85 9.94995 10.05 9.54996 10.65 8.64996 L 11.45 7.54995 C 11.85 7.04995 12.55 6.74998 13.15 6.94998 C 13.75 7.14998 14.15 7.74997 14.15 8.34997 L 14.15 10.95 C 14.15 11.75 14.25 12.55 14.45 13.35 L 14.65 14.05 C 14.85 15.05 15.45 15.85 16.25 16.55 L 17.25 17.45 C 17.85 17.95 17.95 18.95 17.45 19.55 C 17.15 19.95 16.75 20.05 16.25 20.05 C 15.95 20.05 15.55 19.95 15.25 19.75 L 14.25 18.85 C 13.25 18.05 12.55 17.05 12.05 15.85" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -4.47 2.97)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7.29, -14.72)" d="M 8.15002 17.25 L 6.42578 12.1875" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -2.12 8.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-9.64, -20.25)" d="M 11.25 22.35 C 11.45 22.25 11.65 22.15 11.75 21.95 C 11.85 21.75 11.85 21.55 11.75 21.25 C 10.95 19.55 9.65003 18.15 8.15003 17.25 C 7.45003 18.95 7.25003 20.75 7.65003 22.65 C 7.75003 22.85 7.85004 23.05 8.05004 23.15 C 8.25004 23.25 8.45003 23.25 8.65003 23.25 L 11.25 22.35 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 6.75 9.95)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-18.5, -21.7)" d="M 13.75 20.1499 L 23.25 20.1499 C 23.25 20.1499 23.25 23.2499 20.35 23.2499 C 18.45 23.2499 15.55 23.2499 13.85 23.2499" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -8.95 9.95)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-2.8, -21.7)" d="M 4.45 20.1499 L 0.75 20.1499 C 0.75 20.1499 0.750001 23.2499 3.65 23.2499 C 4.05 23.2499 4.45 23.2499 4.85 23.2499" stroke-linecap="round" />
</g>
</g>
</g>
</svg>