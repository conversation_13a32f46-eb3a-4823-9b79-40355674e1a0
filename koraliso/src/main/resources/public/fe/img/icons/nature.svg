<svg id='Style_Three_Pin_Fish_Trekking_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.25 0.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -12)" d="M 12 0.5 C 14.164191454542598 0.4968217927894343 16.284418651356113 1.1108273782540807 18.111999999999995 2.2699999999999942 C 22.862000000000002 5.281000000000001 23.212000000000003 11.5 19.427000000000003 16.047 C 17.184015325710455 18.752824063165953 14.697969177549624 21.247573213865127 12.000000000000004 23.50000000000001 C 9.302030822450378 21.247573213865117 6.815984674289542 18.752824063165953 4.572999999999993 16.046999999999997 C 0.789 11.5 1.138 5.281 5.888 2.27 C 7.7155813486438785 1.1108273782540845 9.835808545457397 0.4968217927894356 11.999999999999995 0.49999999999999867 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -0.81 0.94)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10.94, -12.69)" d="M 10.94 13.729 L 12.078999999999999 16.891 C 12.263703239185663 17.35774859228222 12.785771432891368 17.593361291621164 13.257971510812768 17.423077348237946 C 13.730171588734166 17.252793404854728 13.981704051226767 16.73820622732178 13.825999999999999 16.261 L 12.153999999999998 11.616999999999999 C 12.006964893096272 11.21055577000263 12.006964893096272 10.765444229997367 12.153999999999998 10.358999999999998 L 12.587999999999997 9.152999999999999 C 12.761693553821782 8.6705792710228 12.511420728977196 8.138693553821783 12.028999999999998 7.964999999999999 C 11.5465792710228 7.791306446178215 11.014693553821783 8.041579271022801 10.840999999999998 8.524 L 8.055 16.261 C 7.899295948773231 16.73820622732178 8.150828411265833 17.252793404854728 8.62302848918723 17.423077348237946 C 9.09522856710863 17.593361291621164 9.617296760814336 17.35774859228222 9.802 16.891 L 11.219 12.955999999999998 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.25 -7.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13, -4.5)" d="M 11 4.505 C 11 5.609569499661587 11.895430500338414 6.505 13 6.505 C 14.104569499661586 6.505 15 5.609569499661587 15 4.505 C 15 3.400430500338413 14.104569499661586 2.505 13 2.505 C 11.895430500338414 2.505 11 3.4004305003384125 11 4.505 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -3.72 -3.04)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-8.03, -8.71)" d="M 9.579 7.294 C 9.815327671496306 7.412730092694455 9.918317534966839 7.694966144077534 9.814 7.938 L 8.726 10.479 C 8.668973571689538 10.61163111054143 8.557401528218822 10.71313923061781 8.419986025723537 10.757411170752274 C 8.282570523228252 10.801683110886739 8.132725893301462 10.784397098976083 8.009 10.709999999999999 L 6.449 9.774 C 6.333625647089378 9.703511146710104 6.2517156195767 9.589303040380056 6.221947198884871 9.4574176322517 C 6.192178778193042 9.325532224123343 6.217089798058856 9.187213081385007 6.2909999999999995 9.074 L 7.754999999999999 6.874 C 7.894023616251495 6.659803884197998 8.172111913040043 6.584784715762018 8.4 6.7 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 2.62 -1.02)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-14.37, -10.73)" d="M 12.231 11.832 C 12.357419378550201 11.929755749574447 12.491144224203287 12.017679835591352 12.631 12.095 C 13.67669963061922 12.605774206110745 14.903895160963437 12.584205314971362 15.931000000000001 12.037 C 16.248309800701293 11.906633075924491 16.47017901905691 11.614702168109588 16.510829436308995 11.274072474613508 C 16.551479853561077 10.933442781117426 16.40454063551257 10.597498632201777 16.126820854117167 10.39612145490581 C 15.849101072721764 10.194744277609841 15.48412566081203 10.159494402536506 15.173000000000002 10.304 C 14.676934748155244 10.59479028082378 14.075310050229934 10.643391541610892 13.539000000000001 10.436 L 13.534 10.436 C 13.060878196675096 10.089495098096362 12.737038185193079 9.57621225429043 12.628 9" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 4.25 3)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-16, -14.75)" d="M 16 17.505 L 16 12.004" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 4.25 -2.58)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-16, -9.17)" d="M 16 10.337 L 16 8.005" stroke-linecap="round" />
</g>
</g>
</g>
</svg>