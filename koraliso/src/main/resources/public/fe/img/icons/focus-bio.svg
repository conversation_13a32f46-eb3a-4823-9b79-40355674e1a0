<svg id='Seafood_Crab_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0 1.5)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0" y1="-0.75" x2="0" y2="0.75" />
</g>
<g transform="matrix(1 0 0 1 -3.19 2.03)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-0.5250000000000004" y1="-0.5300000000000002" x2="0.5250000000000004" y2="0.5300000000000002" />
</g>
<g transform="matrix(1 0 0 1 3.18 2.03)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0.5250000000000004" y1="-0.5300000000000002" x2="-0.5250000000000004" y2="0.5300000000000002" />
</g>
<g transform="matrix(1 0 0 1 0.02 3.97)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12.03, -15.97)" d="M 18.63 11.42 C 14.362191232985113 9.779652171282939 9.637808767014892 9.779652171282937 5.3700000000000045 11.419999999999996 C 4.966658117205316 11.55984604420967 4.640084259658851 11.861845065882607 4.469174908270273 12.25303758128313 C 4.298265556881694 12.644230096683653 4.298564675313763 13.089037700094782 4.469999999999999 13.48 C 6.199999999999999 17.39 7.3199999999999985 18.990000000000002 10.09 21.380000000000003 C 10.365280420335234 21.6241378528112 10.722107888229724 21.75616401593216 11.09 21.750000000000004 L 12.969999999999999 21.750000000000004 C 13.337892111770275 21.75616401593216 13.694719579664763 21.6241378528112 13.969999999999999 21.380000000000003 C 16.74 18.990000000000002 17.86 17.380000000000003 19.59 13.480000000000002 C 19.75913153445759 13.08110144820659 19.751205384372593 12.629196983722762 19.56818926936293 12.236474903597859 C 19.385173154353264 11.843752823472958 19.044229557330098 11.547040955620508 18.63 11.42 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 8.45 5.21)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-20.45, -17.21)" d="M 18.5 15.67 C 20.10504881217254 16.236742692699313 21.47670414186849 17.31999869666432 22.4 18.750000000000004" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 7.04 7.46)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-19.04, -19.46)" d="M 17.17 17.92 C 18.721176076249098 18.507919385250997 20.035548448514138 19.59034369182221 20.909999999999997 20.999999999999993" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 5.34 9.61)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-17.34, -21.61)" d="M 15.38 20 C 16.99312232065388 20.633798941598737 18.36498154718514 21.760683306249412 19.3 23.22" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -8.45 5.21)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3.55, -17.21)" d="M 5.5 15.67 C 3.894951187827458 16.236742692699313 2.5232958581315117 17.319998696664317 1.5999999999999996 18.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -7.04 7.46)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.96, -19.46)" d="M 6.83 17.92 C 5.278823923750904 18.507919385251 3.9644515514858543 19.590343691822216 3.089999999999998 21.000000000000004" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.34 9.62)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.66, -21.63)" d="M 8.62 20 C 7.002622926720732 20.64170150160975 5.630246635350614 21.779513477873238 4.699999999999999 23.250000000000004" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -8.99 -3.33)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3.01, -8.67)" d="M 4.52 12.14 C 2.2832588625122123 10.589143364085498 1.1482759547523562 7.882345639049965 1.6099999999999999 5.200000000000001" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -6.24 -7.62)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.76, -4.38)" d="M 5.32 4.5 L 9.92 2.9299999999999997 C 9.880879444294106 2.613027234453301 9.778887214763303 2.3070505458608945 9.62 2.0299999999999994 C 8.7 0.5 6.24 0.32 4.12 1.61 C 2 2.9000000000000004 1 5.2 2 6.73 C 3 8.260000000000002 5.38 8.450000000000001 7.5 7.15 C 7.861892179041645 6.909670316045593 8.197121243256456 6.6314301927473 8.5 6.32 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 8.98 -3.33)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-20.99, -8.67)" d="M 19.48 12.14 C 21.716741137487787 10.5891433640855 22.851724045247643 7.882345639049967 22.39 5.200000000000003" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 6.24 -7.62)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-18.25, -4.38)" d="M 18.68 4.5 L 14.08 2.9299999999999997 C 14.119120555705894 2.6130272344533005 14.221112785236697 2.3070505458608954 14.38 2.0300000000000002 C 15.3 0.5 17.76 0.32 19.88 1.61 C 21.999999999999996 2.9000000000000004 23 5.2 22.05 6.73 C 21.1 8.260000000000002 18.67 8.450000000000001 16.55 7.15 C 16.188107820958358 6.9096703160455935 15.852878756743545 6.6314301927473 15.55 6.320000000000001 Z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>