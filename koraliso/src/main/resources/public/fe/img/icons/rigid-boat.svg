<svg id='Sea_Transport_Small_Boat_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.75 4.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12.5, -16.25)" d="M 20.77 18.645 L 23.5 13.25 L 3.86 13.25 C 3.644880351065016 13.250163337317659 3.4539777666487157 13.387903176706637 3.386 13.592 L 1.5 19.25" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.07 -2.01)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.83, -9.74)" d="M 21.5 13.25 L 16.6 7.013 C 16.22132964660018 6.531280130844208 15.642735462753064 6.249722196793766 15.030000000000001 6.2490000000000006 L 4.385 6.233 C 4.2390048089031 6.2327401759619665 4.10018878413722 6.296303303091606 4.005 6.407 L 2.272 8.424 C 2.1446062664851646 8.57214775872724 2.11518009387729 8.780929331687783 2.1966639289759557 8.95851679287907 C 2.278147764074622 9.136104254070359 2.455610850033407 9.249957220497288 2.651 9.25 L 5.859 9.25 C 6.012894943702439 9.250042511911117 6.158196606888001 9.320951758876468 6.252922163848056 9.442239533870682 C 6.3476477208081095 9.563527308864895 6.381242583668693 9.72167938700193 6.344 9.871 L 5.5 13.25" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.31 -2)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-15.07, -9.75)" d="M 17.571 8.25 L 10.707 8.25 C 10.504491119497605 8.249637676202275 10.321761521427716 8.371462873583322 10.244215690322608 8.55853671472389 C 10.166669859217503 8.74561055586446 10.209623158990171 8.960985806917948 10.353000000000002 9.104 L 11.914000000000001 10.664 C 12.288985462714917 11.039098749621903 12.797610373042373 11.24988672518901 13.328000000000001 11.25 L 19.928 11.25" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -0.76 -7.51)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11, -4.25)" d="M 12.995 6.246 L 8.999 2.25" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 8.96)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -20.71)" d="M 0.5 21.679 C 2.951 22.2 5 19.679 5 19.679 C 5.76379264894959 20.877432838420646 7.072406035291541 21.618843056448338 8.492999999999999 21.657999999999998 C 9.927015847066526 21.654636684777344 11.25580553522973 20.90480070163649 12 19.679000000000002 C 12.736500866214026 20.905797557656243 14.062103633300028 21.656833308032194 15.493000000000002 21.657999999999998 C 16.9168632049026 21.617169578006823 18.228981537044803 20.876741240593248 19 19.679 C 19 19.679 21.049 22.2 23.5 21.679" stroke-linecap="round" />
</g>
</g>
</g>
</svg>