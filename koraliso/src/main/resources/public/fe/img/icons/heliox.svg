<svg id='Helium_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0 7)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -19)" d="M 7 19 L 17 19" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.6 0)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.4, -12)" d="M 6.40283 16.5 L 6.40283 7.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -1.1 0)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10.9, -12)" d="M 10.9028 7.5 L 10.9028 16.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -3.35 -0.38)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-8.65, -11.63)" d="M 6.40283 11.625 L 10.9028 11.625" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.65 2)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-15.65, -14)" d="M 17.4098 15.3894 C 17.0977 16.0463 16.4281 16.5005 15.6525 16.5005 C 14.5786 16.5005 13.708 15.6299 13.708 14.556 L 13.708 13.4449 C 13.708 12.371 14.5786 11.5005 15.6525 11.5005 C 16.7263 11.5005 17.5969 12.371 17.5969 13.4449 L 17.5969 13.7227" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.65 1.72)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-15.65, -13.72)" d="M 17.5969 13.7227 L 13.708 13.7227" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0 0)" >
<rect style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x="-11.25" y="-11.25" rx="1" ry="1" width="22.5" height="22.5" />
</g>
</g>
</g>
</svg>