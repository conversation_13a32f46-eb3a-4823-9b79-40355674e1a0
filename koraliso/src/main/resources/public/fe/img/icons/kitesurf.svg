<svg id='Sport_Kitesurfing_2_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.53 -2.91)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="2.053" y1="-0.8215000000000003" x2="-2.053" y2="0.8215000000000003" />
</g>
<g transform="matrix(1 0 0 1 -4.48 0.88)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7.52, -12.89)" d="M 12.55 17.353 L 10.689 14.873000000000001 C 10.405718427000252 14.495291236000337 9.96113595499958 14.273000000000001 9.489 14.273000000000001 L 6.4 14.273000000000001 L 6.067 13.273000000000001 L 7.489 13.273000000000001 C 7.9611359549995795 13.273000000000001 8.405718427000252 13.050708763999666 8.689 12.673000000000002 L 10.189 10.673000000000002 C 10.686056274847715 10.01025830020305 10.551741699796953 9.070056274847715 9.889 8.573 C 9.226258300203048 8.075943725152287 8.286056274847715 8.210258300203048 7.789 8.873000000000001 L 6.74 10.273000000000001 L 3.989 10.273000000000001 C 3.506922797607326 10.273044602547813 3.054244564620044 10.504784226190305 2.772340213763395 10.89584479442012 C 2.490435862906746 11.286905362649934 2.4136636013285475 11.78962482282491 2.5660000000000003 12.247000000000003 L 4.334 17.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -9.26 -4.23)" >
<circle style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="2" />
</g>
<g transform="matrix(1 0 0 1 7.55 -7.26)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-19.55, -4.74)" d="M 21.821 8.754 C 23.61404786184032 7.0823340144466105 23.759985604288293 4.290500777858544 22.151 2.4410000000000007 C 20.59584635557266 0.541160770431466 17.831823118504744 0.17732371831623706 15.838000000000001 1.6100000000000003 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 6.2 -6.82)" >
<polyline style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" points="-2.37,-3.57 -3.62,3.09 3.62,3.57 " />
</g>
<g transform="matrix(1 0 0 1 -3.29 5.49)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0.8804999999999996" y1="0.8804999999999996" x2="-0.8804999999999996" y2="-0.8804999999999996" />
</g>
<g transform="matrix(1 0 0 1 -0.01 10.32)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.99, -22.32)" d="M 0.739 23.273 C 2.219621650657827 23.270374317252788 3.6111607705390454 22.565327829846304 4.488999999999999 21.373 C 5.364671549030014 22.56803017281386 6.757481260423567 23.27414611014341 8.239 23.27414611014341 C 9.720518739576434 23.27414611014341 11.113328450969988 22.56803017281386 11.989 21.373 C 12.864671549030016 22.56803017281386 14.257481260423567 23.27414611014341 15.739 23.27414611014341 C 17.220518739576434 23.27414611014341 18.613328450969988 22.56803017281386 19.489 21.373 C 20.36652005449525 22.565704740279354 21.758263210151796 23.270854605812005 23.239 23.273" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 2.89 6.26)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-14.89, -18.26)" d="M 21.821 19.756 C 22.163363872094205 19.31113418381617 22.315792869889613 18.74887250326896 22.245 18.192 C 21.779 16.952 16.222 15.857 10.66 17.947 C 9.582604677296905 18.349999891695383 8.533277516995417 18.82438530004563 7.518999999999988 19.36700000000001" stroke-linecap="round" />
</g>
</g>
</g>
</svg>