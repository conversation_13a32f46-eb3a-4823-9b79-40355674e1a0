<svg id='Travel_Places_Beach_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(1.14 0 0 1.14 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -0.06 5.42)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -12.48)" d="M 13.5 13.48 L 13 13.48 C 11.895430500338414 13.48 11 12.584569499661587 11 11.48 C 11 12.584569499661587 10.104569499661586 13.48 9 13.48 C 7.8954305003384135 13.48 7 12.584569499661587 7 11.48 C 7 12.584569499661587 6.1045694996615865 13.48 5 13.48 C 3.895430500338413 13.48 3 12.584569499661587 3 11.48 C 3 12.584569499661587 2.104569499661587 13.48 1 13.48 L 0.5 13.48" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -1.3 1.42)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.76, -8.48)" d="M 10 9.48 C 8.957155231509454 8.212131836399793 7.401650051454043 7.477477598494362 5.76 7.477477598494362 C 4.118349948545957 7.477477598494362 2.562844768490544 8.212131836399791 1.5199999999999996 9.48" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.56 -2.06)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7.63, -5)" d="M 6.5 7.53 C 6.56 5.2700000000000005 7.25 3.21 8.75 2.4700000000000006" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.2 -5.57)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7.26, -1.5)" d="M 5.76 0.57 C 7.106118085217019 0.304938409786665 8.424244698581465 1.1397519315841471 8.76 2.4699999999999984" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.52 -4.67)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10.58, -2.39)" d="M 12.41 2.84 C 11.457686844507151 1.811711363814739 9.888847321409731 1.6531128327912286 8.75 2.4699999999999998" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.83 -3.21)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7.89, -3.85)" d="M 5.08 3.54 C 5.888293538721964 2.34917529557047 7.428378749205673 1.9001586265738568 8.75 2.4699999999999998 C 10.08591245669062 2.7967191705858343 10.922069330440234 4.123952303521729 10.64 5.470000000000001" stroke-linecap="round" />
</g>
</g>
</g>
</svg>