<svg id='Ice_Water_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.25 -3.2)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -8.55)" d="M 11.999 3.551 L 11.999 13.551" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 -6.2)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -5.55)" d="M 9.999 4.551 L 11.999 6.551 L 13.999 4.551" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 -0.2)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -11.55)" d="M 9.999 12.551 L 11.999 10.551 L 13.999 12.551" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 -3.2)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -8.55)" d="M 6.999 8.551 L 16.999 8.551" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -2.75 -3.2)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-9, -8.55)" d="M 7.999 10.551 L 9.999 8.551 L 7.999 6.551" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.25 -3.2)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-15, -8.55)" d="M 15.999 10.551 L 13.999 8.551 L 15.999 6.551" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 7.76)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -19.51)" d="M 0.5 19.051 L 1.366 20.09 C 1.555995174438358 20.317834069775 1.8373410549804676 20.449550158091984 2.1340000000000003 20.449550158091984 C 2.4306589450195326 20.449550158091984 2.7120048255616425 20.317834069775 2.902 20.09 L 3.866 18.933 C 4.055995174438358 18.705165930225 4.337341054980468 18.573449841908015 4.634 18.573449841908015 C 4.930658945019532 18.573449841908015 5.2120048255616425 18.705165930225 5.402 18.933 L 6.366 20.09 C 6.555995174438357 20.317834069775 6.837341054980467 20.449550158091984 7.1339999999999995 20.449550158091984 C 7.430658945019532 20.449550158091984 7.712004825561642 20.317834069775 7.901999999999999 20.09 L 8.866 18.933 C 9.055995174438358 18.705165930225 9.337341054980467 18.573449841908015 9.634 18.573449841908015 C 9.930658945019532 18.573449841908015 10.21200482556164 18.705165930225 10.402 18.933 L 11.366 20.09 C 11.555995174438358 20.317834069775 11.837341054980467 20.449550158091984 12.134 20.449550158091984 C 12.430658945019532 20.449550158091984 12.712004825561642 20.317834069775 12.902 20.09 L 13.866 18.933 C 14.055995174438358 18.705165930225 14.337341054980467 18.573449841908015 14.634 18.573449841908015 C 14.930658945019532 18.573449841908015 15.21200482556164 18.705165930225 15.402 18.933 L 16.366 20.09 C 16.555995174438358 20.317834069775003 16.837341054980467 20.449550158091988 17.134 20.449550158091988 C 17.430658945019534 20.449550158091988 17.712004825561642 20.317834069775003 17.902 20.09 L 18.866 18.933 C 19.055995174438358 18.705165930224997 19.337341054980467 18.573449841908012 19.634 18.573449841908012 C 19.930658945019534 18.573449841908012 20.212004825561642 18.705165930224997 20.402 18.933 L 21.366 20.09 C 21.555995174438358 20.317834069775003 21.837341054980467 20.449550158091988 22.134 20.449550158091988 C 22.430658945019534 20.449550158091988 22.712004825561642 20.317834069775003 22.902 20.09 L 23.502000000000002 19.37" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.24 4.26)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -16.01)" d="M 0.5 15.551 L 1.366 16.59 C 1.555995174438358 16.817834069775 1.8373410549804676 16.949550158091984 2.1340000000000003 16.949550158091984 C 2.4306589450195326 16.949550158091984 2.7120048255616425 16.817834069775 2.902 16.59 L 3.866 15.433 C 4.055995174438358 15.205165930224998 4.337341054980468 15.073449841908014 4.634 15.073449841908014 C 4.930658945019532 15.073449841908014 5.2120048255616425 15.205165930224998 5.402 15.433 L 6.366 16.59 C 6.555995174438357 16.817834069775 6.837341054980467 16.949550158091984 7.1339999999999995 16.949550158091984 C 7.430658945019532 16.949550158091984 7.712004825561642 16.817834069775 7.901999999999999 16.59 L 8.866 15.433 C 9.055995174438358 15.205165930224998 9.337341054980467 15.073449841908014 9.634 15.073449841908014 C 9.930658945019532 15.073449841908014 10.21200482556164 15.205165930224998 10.402 15.433 L 11.366 16.59 C 11.555995174438358 16.817834069775 11.837341054980467 16.949550158091984 12.134 16.949550158091984 C 12.430658945019532 16.949550158091984 12.712004825561642 16.817834069775 12.902 16.59 L 13.866 15.433 C 14.055995174438358 15.205165930224998 14.337341054980467 15.073449841908014 14.634 15.073449841908014 C 14.930658945019532 15.073449841908014 15.21200482556164 15.205165930224998 15.402 15.433 L 16.366 16.59 C 16.555995174438358 16.817834069775003 16.837341054980467 16.949550158091988 17.134 16.949550158091988 C 17.430658945019534 16.949550158091988 17.712004825561642 16.817834069775003 17.902 16.59 L 18.866 15.433 C 19.055995174438358 15.205165930224998 19.337341054980467 15.073449841908014 19.634 15.073449841908014 C 19.930658945019534 15.073449841908014 20.212004825561642 15.205165930224998 20.402 15.433 L 21.366 16.59 C 21.555995174438358 16.817834069775003 21.837341054980467 16.949550158091988 22.134 16.949550158091988 C 22.430658945019534 16.949550158091988 22.712004825561642 16.817834069775003 22.902 16.59 L 23.495 15.878" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 8.43 0.76)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-20.18, -12.51)" d="M 16.865 13.449 C 17.16156886942022 13.449136135097065 17.4428993659187 13.31762878061405 17.633 13.09 L 18.596999999999998 11.933 C 18.786995174438356 11.705165930224998 19.068341054980465 11.573449841908014 19.365 11.573449841908014 C 19.661658945019532 11.573449841908014 19.94300482556164 11.705165930224998 20.133 11.933 L 21.1 13.09 C 21.28999517443836 13.317834069775001 21.57134105498047 13.449550158091986 21.868000000000002 13.449550158091986 C 22.164658945019536 13.449550158091986 22.446004825561644 13.317834069775001 22.636000000000003 13.09 L 23.502000000000002 12.051" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -7.94 0.73)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3.82, -12.49)" d="M 0.5 12.028 L 1.366 13.067 C 1.555995174438358 13.294834069775002 1.8373410549804676 13.426550158091986 2.1340000000000003 13.426550158091986 C 2.4306589450195326 13.426550158091986 2.7120048255616425 13.294834069775002 2.902 13.067 L 3.866 11.91 C 4.055995174438358 11.682165930224999 4.337341054980468 11.550449841908014 4.634 11.550449841908014 C 4.930658945019532 11.550449841908014 5.2120048255616425 11.682165930224999 5.402 11.91 L 6.366 13.067 C 6.556100634081298 13.294628780614051 6.83743113057978 13.426136135097064 7.1339999999999995 13.426" stroke-linecap="round" />
</g>
</g>
</g>
</svg>