<svg id='Arrow<PERSON>_Shrink_1_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(1.14 0 0 1.14 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -4 3.88)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3.06, -10.94)" d="M 5.10547 8.89453 L 1.01965 12.9804" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.76 -3.95)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10.83, -3.11)" d="M 12.887 1.05133 L 8.76409 5.1742" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -3.95 -3.95)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3.11, -3.11)" d="M 5.22864 5.22853 L 0.993408 0.993301" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.78 3.78)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10.84, -10.84)" d="M 12.9204 12.9204 L 8.76562 8.76562" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -3.92 3.8)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3.14, -10.86)" d="M 5.10669 12.954 C 5.26765 11.3333 5.26774 10.4432 5.10669 8.89338 C 3.55685 8.73234 2.66672 8.73243 1.04611 8.89338" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -3.8 -3.8)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3.26, -3.26)" d="M 1.16626 5.22697 C 2.78688 5.38793 3.677 5.38802 5.22684 5.22698 C 5.38789 3.67713 5.3878 2.78701 5.22684 1.16639" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.68 -3.8)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10.74, -3.26)" d="M 8.76831 1.1664 C 8.60735 2.78701 8.60726 3.67713 8.76831 5.22698 C 10.3182 5.38803 11.2083 5.38793 12.8289 5.22698" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.68 3.66)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10.74, -10.72)" d="M 12.8289 8.75038 C 11.2082 8.58943 10.3181 8.58933 8.76828 8.75038 C 8.60723 10.3002 8.60732 11.1903 8.76828 12.811" stroke-linecap="round" />
</g>
</g>
</g>
</svg>