<svg id='Whale_Body_1_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.24 3.73)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.99, -15.48)" d="M 20.4512 14.2389 C 20.4512 16.4821 19.5601 18.6334 17.9739 20.2196 C 16.3877 21.8058 14.2364 22.6969 11.9932 22.6969 C 9.74997 22.6969 7.59865 21.8058 6.01246 20.2196 C 4.42628 18.6334 3.53517 16.4821 3.53517 14.2389 C 3.53517 10.7562 8.01294 8.26855 11.9932 8.26855 C 15.9734 8.26855 20.4512 10.7562 20.4512 14.2389 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.24 4.35)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.99, -16.11)" d="M 20.073 16.7264 C 17.4721 17.6059 14.7382 18.0267 11.9932 17.9702 C 9.24682 18.0274 6.51145 17.6065 3.90932 16.7264 C 3.90932 16.7264 5.52529 14.2388 11.9932 14.2388 C 18.461 14.2388 20.073 16.7264 20.073 16.7264 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -8.9 7.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-2.85, -19.25)" d="M 4.06056 17.1919 L 0.549999 20.7064 C 0.987642 20.9879 1.47807 21.1772 1.99133 21.2627 C 2.50459 21.3483 3.0299 21.3283 3.53517 21.204 C 3.98787 21.0994 4.39281 20.847 4.68601 20.4866 C 4.9792 20.1262 5.14392 19.6783 5.15413 19.2139" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 9.38 7.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-21.13, -19.25)" d="M 19.9168 17.1919 L 23.4363 20.7064 C 22.9987 20.9879 22.5083 21.1772 21.995 21.2627 C 21.4817 21.3483 20.9564 21.3283 20.4512 21.204 C 19.9987 21.0992 19.5939 20.8467 19.3009 20.4863 C 19.0079 20.126 18.8434 19.6782 18.8332 19.2139" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 7.7 -3.98)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-19.45, -7.77)" d="M 15.455 8.85972 C 15.455 8.85972 17.9635 9.26372 18.9586 6.27854 C 18.3363 5.59734 17.9827 4.71331 17.9635 3.7909 C 17.8821 3.08911 18.0589 2.38168 18.461 1.80078 L 20.6382 3.66652 L 23.4363 2.79584 C 23.502 3.65501 23.3299 4.51567 22.9388 5.28349 C 21.9438 6.77607 20.9487 6.77607 20.9487 6.77607 C 20.9562 9.10816 20.7795 11.4372 20.4203 13.7415" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -1.01 -9.21)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10.75, -2.55)" d="M 9.50055 1.30323 C 9.82765 1.30257 10.1517 1.36644 10.4541 1.49116 C 10.7564 1.61588 11.0313 1.79902 11.2628 2.03008 C 11.4943 2.26115 11.678 2.5356 11.8033 2.83774 C 11.9287 3.13988 11.9932 3.46377 11.9932 3.79087" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.48 -7.96)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13.24, -3.79)" d="M 14.4808 1.30322 C 13.8211 1.30322 13.1883 1.56531 12.7218 2.03184 C 12.2553 2.49836 11.9932 3.1311 11.9932 3.79087 L 11.9932 6.27851" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.11 0.74)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.64, -12.5)" d="M 6.76911 12.7461 C 6.63172 12.7461 6.52035 12.6347 6.52035 12.4973 C 6.52035 12.3599 6.63172 12.2485 6.76911 12.2485" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -4.86 0.74)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.89, -12.5)" d="M 6.76911 12.7461 C 6.9065 12.7461 7.01788 12.6347 7.01788 12.4973 C 7.01788 12.3599 6.9065 12.2485 6.76911 12.2485" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 5.33 0.74)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-17.09, -12.5)" d="M 17.2123 12.7461 C 17.0749 12.7461 16.9635 12.6347 16.9635 12.4973 C 16.9635 12.3599 17.0749 12.2485 17.2123 12.2485" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 5.58 0.74)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-17.34, -12.5)" d="M 17.2122 12.7461 C 17.3496 12.7461 17.461 12.6347 17.461 12.4973 C 17.461 12.3599 17.3496 12.2485 17.2122 12.2485" stroke-linecap="round" />
</g>
</g>
</g>
</svg>