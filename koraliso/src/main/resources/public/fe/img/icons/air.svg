<svg id='Interface_Weather_Wind_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(1.14 0 0 1.14 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -2.19 -4.81)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.88, -2.25)" d="M 7.5 0.5 C 8.466498312203889 0.5 9.25 1.283501687796111 9.25 2.2499999999999996 C 9.25 3.216498312203888 8.466498312203889 3.9999999999999996 7.500000000000001 4 L 0.5 4" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.69 1.69)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7.75, -8.75)" d="M 11.75 10.5 C 12.716498312203889 10.5 13.5 9.716498312203889 13.5 8.75 C 13.5 7.783501687796111 12.716498312203889 7 11.75 7 L 2 7" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -1.81 4.69)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.25, -11.75)" d="M 7.25 13.5 C 8.216498312203889 13.5 9 12.716498312203889 9 11.75 C 9 10.783501687796111 8.216498312203889 10 7.25 10 L 1.5 10" stroke-linecap="round" />
</g>
</g>
</g>
</svg>