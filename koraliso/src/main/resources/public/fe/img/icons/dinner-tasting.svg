<svg id='Food_Kitchenware_Fork_Spoon_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(1.14 0 0 1.14 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 3.54 -3.56)" >
<ellipse style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" rx="2.4" ry="3" />
</g>
<g transform="matrix(1 0 0 1 3.54 2.94)" >
<line style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0" y1="-3.5" x2="0" y2="3.5" />
</g>
<g transform="matrix(1 0 0 1 -3.56 -0.06)" >
<line style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0" y1="-6.5" x2="0" y2="6.5" />
</g>
<g transform="matrix(1 0 0 1 -3.56 -4.06)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3.5, -3)" d="M 6 0.5 L 6 3 C 6 4.380711874576983 4.880711874576983 5.5 3.5 5.5 L 3.5 5.5 C 2.1192881254230165 5.5 1 4.380711874576983 1 3 L 1 0.5" stroke-linecap="round" />
</g>
</g>
</g>
</svg>