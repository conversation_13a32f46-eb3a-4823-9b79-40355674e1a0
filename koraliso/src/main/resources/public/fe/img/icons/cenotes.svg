<svg id='Farming_Grow_Crops_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -7.21 -1.21)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.54, -10.55)" d="M 8.576 11.556 C 7.112303347918392 11.485590463217195 5.7697314098380215 10.722014279346313 4.961 9.5 L 4.961 9.5 C 4.961 9.5 2.9610000000000003 12.042 0.5060000000000002 11.52" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 7.02 -1.22)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-18.77, -10.53)" d="M 23.5 11.479 C 21 12 19.44 9.5 19.44 9.5 L 19.44 9.5 C 18.307092838966778 11.322345000090396 16.017056111868154 12.050067782257292 14.04 11.216" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.5 9.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.25, -21)" d="M 10 21 C 10 21.449 8.211 23 6.3260000000000005 23 C 4.995764839446466 22.931401859736614 3.693675519848147 22.591075325602183 2.4999999999999973 22 L 2.5 20 C 3.6947556784888325 19.411819469821992 4.996239282596106 19.07165125128899 6.325999999999997 19 C 8.191 19 10 20.551 10 21 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -10.25 9.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-1.5, -21)" d="M 2.5 22 L 0.5 23 L 0.5 19 L 2.5 20" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 8.75 5.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-20.5, -17.5)" d="M 23.5 17.5 C 23.5 17.837 22.087 19 20.6 19 C 19.549475781133285 18.96722036794618 18.50755816916423 18.799169140209237 17.5 18.5 L 17.5 16.5 C 18.507944052599434 16.202525735396033 19.549642337464196 16.03450988299849 20.599999999999998 16 C 22.072 16 23.5 17.163 23.5 17.5 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 4.75 5.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-16.5, -17.5)" d="M 17.5 18.5 L 15.5 19 L 15.5 16 L 17.5 16.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.77 -7.15)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-15.52, -4.6)" d="M 13.5 3 C 12.3 3.8 11.676 5.269 13.5 6.5 C 14.5 9 16.36 8.327 17.461 7.167 C 18.714343046276667 5.366063556805551 19.034838701976398 3.075800858427529 18.324 1 C 16.566 2.325 15 2 13.5 3 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 2 -4.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13.75, -7.25)" d="M 16 4.5 C 13 6 11.5 10 11.5 10" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -4.28 -7.15)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7.48, -4.6)" d="M 9.5 3 C 10.7 3.8 11.324 5.269 9.5 6.5 C 8.5 9 6.640000000000001 8.327 5.539 7.167 C 4.285656953723334 5.3660635568055515 3.965161298023604 3.0758008584275327 4.675999999999999 1.0000000000000053 C 6.434 2.325 8 2 9.5 3 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -2.5 -4.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-9.25, -7.25)" d="M 7 4.5 C 10 6 11.5 10 11.5 10" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -1.75 1.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10, -13)" d="M 11.5 10 L 11.5 10.69 C 11.500173235932817 12.470931698320667 10.709175958750537 14.159906894156846 9.341000000000003 15.299999999999997 L 8.5 16" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1 2)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12.75, -13.75)" d="M 11.5 10.5 C 11.689409905454959 12.862340550215665 12.557581210605555 15.119585943607214 14 17" stroke-linecap="round" />
</g>
</g>
</g>
</svg>