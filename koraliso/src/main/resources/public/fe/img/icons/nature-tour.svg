<svg id='Ecology_Globe_Nature_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -0.25 6.08)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.5, -17.83)" d="M 22.5 12.33 C 22.5 18.405132248138727 17.57513224813873 23.33 11.5 23.33 C 5.424867751861271 23.33 0.5 18.405132248138727 0.5 12.33 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5 4.08)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.75, -15.83)" d="M 3 19.33 L 8.5 19.33 C 9.604569499661586 19.33 10.5 18.434569499661585 10.5 17.33 C 10.5 16.225430500338412 9.604569499661586 15.329999999999998 8.5 15.329999999999998 C 7.3954305003384135 15.329999999999998 6.5 14.434569499661585 6.5 13.329999999999998 L 6.5 12.329999999999998" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 6.65 8.2)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-18.4, -19.95)" d="M 21.3 17.33 L 17.5 17.33 C 16.395430500338414 17.33 15.5 18.225430500338412 15.5 19.33 L 15.5 22.58" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 7.74 -6.73)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-19.5, -5.02)" d="M 23.28 8 L 21.28 5.27 C 21.94 5.789999999999999 22.89 5.02 22.23 4.069999999999999 L 19.9 0.87 C 19.805572809000083 0.7440970786667785 19.657378651666527 0.6699999999999995 19.5 0.6699999999999995 C 19.34262134833347 0.6699999999999995 19.194427190999914 0.7440970786667787 19.099999999999998 0.8700000000000001 L 16.78 4 C 16.12 5 17.07 5.72 17.73 5.2 L 15.72 8 C 15.520268179062311 8.215533639807427 15.44938700083196 8.52061037805076 15.533635507340046 8.802123680285096 C 15.617884013848132 9.083636982519431 15.844703067688025 9.299620950581158 16.13 9.370000000000001 L 22.869999999999997 9.370000000000001 C 23.151519405705073 9.294590953638094 23.373832838922777 9.078647304431042 23.457391709668986 8.799438394864444 C 23.540950580415196 8.520229485297843 23.473812145616563 8.217661554421667 23.28 8 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 7.75 -0.92)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0" y1="-1.5" x2="0" y2="1.5" />
</g>
<g transform="matrix(1 0 0 1 -0.97 -3.79)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10.78, -7.96)" d="M 15 10.83 L 10.13 5.27 C 10.036948325900005 5.155698529232087 9.897388738621784 5.08935416596516 9.75 5.08935416596516 C 9.602611261378218 5.08935416596516 9.463051674099997 5.155698529232087 9.370000000000001 5.27 L 6.56 8.51" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -6.71 -2.79)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.04, -8.96)" d="M 8.51 10.83 L 5.51 7.27 C 5.415940034376544 7.162620428144143 5.282541201292683 7.097723698535778 5.14 7.09 C 4.9943617295838 7.087368701749471 4.855102461045024 7.149668900832608 4.76 7.26 L 1.58 10.83" stroke-linecap="round" />
</g>
</g>
</g>
</svg>