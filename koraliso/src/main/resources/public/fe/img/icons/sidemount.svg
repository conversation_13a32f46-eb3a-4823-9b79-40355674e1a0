<svg id='Diving_Weight_Belt_3_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -9.23 -6.09)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-2.77, -5.91)" d="M 1 5 C 1.56758 5.4865 2.76631 6.20968 4.53862 6.8241" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 9.23 -6.09)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-21.23, -5.91)" d="M 23 5 C 22.4324 5.4865 21.2337 6.20968 19.4614 6.8241" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0 -4.08)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -7.92)" d="M 9.18066 7.84698 C 10.0586 7.94436 10.9992 8.00001 12 8.00001 C 13.0009 8.00001 13.9414 7.94436 14.8194 7.84698" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -10.01 0.6)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-1.99, -12.6)" d="M 1 12 C 1.3825 12.3279 2.05165 12.7632 2.98982 13.2003" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0 2.96)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -14.96)" d="M 14.0108 14.9231 C 13.3706 14.9728 12.7 15 12 15 C 11.2999 15 10.6294 14.9728 9.98914 14.9231" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 10 0.6)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-22.01, -12.6)" d="M 23 12 C 22.6175 12.3279 21.9484 12.7632 21.0102 13.2003" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.5 0.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.5, -12.5)" d="M 3 11.6136 C 3 11.6136 3 6 6.5 6 C 10 6 10 11.6136 10 11.6136 L 10 17.2273 C 10 17.2273 9.41667 19 6.5 19 C 3.58333 19 3 17.2273 3 17.2273 L 3 11.6136 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 5.5 0.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-17.5, -12.5)" d="M 14 11.6136 C 14 11.6136 14 6 17.5 6 C 21 6 21 11.6136 21 11.6136 L 21 17.2273 C 21 17.2273 20.4167 19 17.5 19 C 14.5833 19 14 17.2273 14 17.2273 L 14 11.6136 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.5 0.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.5, -12.5)" d="M 3 12 C 3.38889 12.3333 4.63333 13 6.5 13 C 8.36667 13 9.61111 12.3333 10 12" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 5.5 0.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-17.5, -12.5)" d="M 14 12 C 14.3889 12.3333 15.6333 13 17.5 13 C 19.3667 13 20.6111 12.3333 21 12" stroke-linecap="round" />
</g>
</g>
</g>
</svg>