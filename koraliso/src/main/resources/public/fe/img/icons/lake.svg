<svg id='Water_Protection_Fish_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.25 -8.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -3.25)" d="M 23.5 4.18402 C 21 4.70502 19.44 2.20502 19.44 2.20502 C 19.0525 2.82056 18.5185 3.33046 17.8858 3.68908 C 17.253 4.0477 16.5412 4.24386 15.814 4.26002 C 15.128 4.24648 14.4583 4.04853 13.8752 3.68695 C 13.2921 3.32536 12.8171 2.81348 12.5 2.20502 C 12.0336 2.81134 11.4411 3.30911 10.7634 3.66393 C 10.0857 4.01874 9.33902 4.22213 8.575 4.26002 C 7.8534 4.22541 7.15009 4.02133 6.52204 3.66431 C 5.89399 3.30729 5.35886 2.80736 4.96 2.20502 C 4.96 2.20502 2.952 4.74602 0.5 4.22502" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.75 5.21)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13.51, -16.96)" d="M 19.515 16.956 C 19.515 17.646 17.09 20.546 13.546 20.546 C 11.312 20.4764 9.17267 19.6274 7.49899 18.146 L 7.49899 15.763 C 9.18055 14.2962 11.3159 13.4533 13.546 13.376 C 17.052 13.376 19.514 16.266 19.515 16.956 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.75 5.2)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6, -16.96)" d="M 7.5 18.149 L 5.185 19.071 C 5.10911 19.1012 5.02697 19.1124 4.94576 19.1035 C 4.86456 19.0947 4.78676 19.0661 4.71918 19.0202 C 4.65159 18.9743 4.59627 18.9126 4.55807 18.8404 C 4.51986 18.7682 4.49992 18.6877 4.5 18.606 L 4.5 15.306 C 4.50008 15.2244 4.52014 15.1441 4.55842 15.072 C 4.59669 14.9999 4.65203 14.9383 4.71959 14.8926 C 4.78715 14.8468 4.86489 14.8183 4.94601 14.8094 C 5.02714 14.8006 5.10919 14.8118 5.185 14.842 L 7.499 15.763" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.19 9.37)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.95, -21.13)" d="M 14.59 20.457 C 14.59 20.457 13.238 21.8 9.3 21.8" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -0.66 0.98)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.1, -12.73)" d="M 8.77299 12.481 C 9.53912 12.1024 10.4082 11.9854 11.2472 12.1479 C 12.0862 12.3105 12.8487 12.7436 13.418 13.381" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 9.25 -2.96)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-21, -8.79)" d="M 20.999 10.295 C 21.8274 10.295 22.499 9.62341 22.499 8.79498 C 22.499 7.96656 21.8274 7.29498 20.999 7.29498 C 20.1706 7.29498 19.499 7.96656 19.499 8.79498 C 19.499 9.62341 20.1706 10.295 20.999 10.295 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 3.87 4.58)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-15.62, -16.33)" d="M 15.749 16.579 C 15.6109 16.579 15.499 16.4671 15.499 16.329 C 15.499 16.1909 15.6109 16.079 15.749 16.079" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 4.12 4.58)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-15.87, -16.33)" d="M 15.749 16.579 C 15.8871 16.579 15.999 16.4671 15.999 16.329 C 15.999 16.1909 15.8871 16.079 15.749 16.079" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 7.89 0.79)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-19.64, -12.54)" d="M 19.765 12.795 C 19.6269 12.795 19.515 12.6831 19.515 12.545 C 19.515 12.4069 19.6269 12.295 19.765 12.295" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 8.14 0.79)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-19.89, -12.54)" d="M 19.765 12.795 C 19.9031 12.795 20.015 12.6831 20.015 12.545 C 20.015 12.4069 19.9031 12.295 19.765 12.295" stroke-linecap="round" />
</g>
</g>
</g>
</svg>