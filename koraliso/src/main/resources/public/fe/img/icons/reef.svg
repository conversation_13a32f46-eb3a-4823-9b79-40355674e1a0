<svg id='Seahorse_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>
	

<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -0.73 0.2)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.03, -11.95)" d="M 11.5256 18.4262 C 10.8457 17.7967 10.3144 17.0237 9.9703 16.1635 C 9.62618 15.3032 9.47784 14.3771 9.53602 13.4524 C 9.53602 9.97068 11.0282 8.55811 11.0282 7.56335 C 11.0282 6.56858 10.5308 5.57382 9.53602 5.57382 L 5.05958 5.57382 L 5.05958 3.58429 C 6.06946 3.52743 7.07014 3.36065 8.04387 3.08691 C 10.0334 2.58952 9.53602 1.09738 12.5203 0.599994 C 13.3318 0.429424 14.174 0.477719 14.9606 0.739926 C 15.7472 1.00213 16.4499 1.46882 16.9968 2.09214 C 15.6805 3.60749 14.9718 5.55646 15.0072 7.56335 C 15.0072 11.045 15.5046 12.4576 15.5046 16.934 C 15.5046 21.4105 13.5151 23.4 10.0334 23.4 C 5.05958 23.4 6.05434 18.9236 8.04387 18.9236" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 5.27 -0.49)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-17.02, -11.26)" d="M 15.1007 10.094 C 15.1007 10.094 16.9968 8.47852 17.9915 8.47852 C 18.9863 8.47852 19.2758 13.1629 18.4889 13.9497 C 17.9915 14.4471 15.3474 12.9221 15.3474 12.9221" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 4.35 -0.29)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-16.11, -11.46)" d="M 15.2181 11.4629 L 16.9968 11.4629" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.39 -8.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12.15, -3.26)" d="M 12.2716 3.50471 C 12.1343 3.50471 12.0229 3.39336 12.0229 3.25602 C 12.0229 3.11867 12.1343 3.00732 12.2716 3.00732" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.64 -8.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12.4, -3.26)" d="M 12.2716 3.50471 C 12.409 3.50471 12.5203 3.39336 12.5203 3.25602 C 12.5203 3.11867 12.409 3.00732 12.2716 3.00732" stroke-linecap="round" />
</g>
</g>
</g>
</svg>