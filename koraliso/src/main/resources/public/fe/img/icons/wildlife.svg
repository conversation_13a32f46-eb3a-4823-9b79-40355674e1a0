<svg id='Squid_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 4.22 -4.57)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-16.22, -7.43)" d="M 11.4261 9.51169 C 12.7842 3.9767 21.0213 1.74854 21.0213 1.74854 C 21.0213 1.74854 21.153 10.28 16.2088 13.1139" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 6.75 -7.49)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-18.76, -4.52)" d="M 15.0543 4.62703 L 14.4556 1.75227 C 14.4556 1.75227 18.0288 -0.502837 21.0173 1.75227 C 24.0058 4.00738 22.8254 8.05958 22.8254 8.05958 L 19.8978 8.2831" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -1.44 0.28)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10.56, -12.28)" d="M 10.7467 12.6541 C 10.5401 12.6541 10.3726 12.4866 10.3726 12.2799 C 10.3726 12.0733 10.5401 11.9058 10.7467 11.9058" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -1.07 0.28)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10.93, -12.28)" d="M 10.7468 12.6541 C 10.9534 12.6541 11.121 12.4866 11.121 12.2799 C 11.121 12.0733 10.9534 11.9058 10.7468 11.9058" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.55 2.53)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13.55, -14.53)" d="M 13.7362 14.9051 C 13.5296 14.9051 13.3621 14.7376 13.3621 14.5309 C 13.3621 14.3243 13.5296 14.1567 13.7362 14.1567" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1.92 2.53)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13.92, -14.53)" d="M 13.7363 14.9051 C 13.9429 14.9051 14.1105 14.7376 14.1105 14.5309 C 14.1105 14.3243 13.9429 14.1567 13.7363 14.1567" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -3.14 7.96)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-8.86, -19.96)" d="M 12.2294 16.9683 C 12.206 17.6369 11.9782 18.2823 11.5768 18.8175 C 11.1754 19.3527 10.6196 19.7521 9.98427 19.9618 C 8.39672 20.4906 5.49402 21.4585 5.49402 22.9553" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 5.36 8.11)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-17.36, -20.11)" d="M 15.2229 16.9683 C 15.9712 18.465 14.4745 20.7101 15.2229 22.2069 C 15.9712 23.7036 18.9648 23.7036 19.7131 21.4585" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -6.51 5.34)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.49, -17.35)" d="M 9.23596 14.7266 C 9.23596 14.7266 7.73921 14.7266 6.24245 16.9717 C 4.7457 19.2168 3.99733 19.9652 1.7522 19.9652" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -7.66 -0.78)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.34, -11.23)" d="M 7.73914 11.7329 C 7.10339 11.5281 6.4258 11.4893 5.77083 11.6203 C 5.11586 11.7513 4.50531 12.0477 3.99726 12.4813 C 2.50051 13.978 -0.492997 11.7329 1.75213 9.48779" stroke-linecap="round" />
</g>
</g>
</g>
</svg>