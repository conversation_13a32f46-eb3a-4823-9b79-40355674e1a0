<svg id='Wave_1_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0 1.8)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -13.8)" d="M 1 14.5776 C 2.6186 13.1479 4.70715 12.3647 6.86667 12.3776 C 10.5333 12.3776 17.1333 18.5698 23 12.7032" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0 -3.58)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -8.43)" d="M 1 9.20015 C 2.6186 7.77054 4.70715 6.98734 6.86667 7.00015 C 10.5333 7.00015 17.1333 13.1924 23 7.32576" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0 7.18)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -19.18)" d="M 1 19.9556 C 2.6186 18.5259 4.70715 17.7427 6.86667 17.7556 C 10.5333 17.7556 17.1333 23.9478 23 18.0812" stroke-linecap="round" />
</g>
</g>
</g>
</svg>