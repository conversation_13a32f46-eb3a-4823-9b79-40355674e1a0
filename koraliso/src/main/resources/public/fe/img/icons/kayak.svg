<svg id='Canoe_1_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.55 0.25)" id="Light" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12.3, -12)" d="M 16.3 13 C 16.3 19.352 13.075000000000001 23.5 12.3 23.5 C 11.525 23.5 8.3 19.352 8.3 13 C 8.3 6.648 11.477 0.5 12.3 0.5 C 13.123000000000001 0.5 16.3 6.648 16.3 13 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.55 3.25)" id="Light" >
<ellipse style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" rx="2" ry="3" />
</g>
<g transform="matrix(1 0 0 1 -4.33 4.82)" id="Light" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="1.0810000000000004" y1="-1.0809999999999995" x2="-1.0810000000000004" y2="1.0809999999999995" />
</g>
<g transform="matrix(1 0 0 1 4.97 -4.48)" id="Light" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0.9299999999999997" y1="-0.9299999999999997" x2="-0.9299999999999997" y2="0.9299999999999997" />
</g>
<g transform="matrix(1 0 0 1 8.32 -7.83)" id="Light" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-20.08, -3.92)" d="M 22.96 3.868 C 23.155190969166433 3.672750074257093 23.155190969166433 3.3562499257429073 22.96 3.1610000000000005 L 20.839 1.04 C 20.643750074257092 0.8448090308335681 20.327249925742905 0.8448090308335681 20.131999999999998 1.04 L 17.657 3.515 C 17.13708683109378 4.017148940523057 16.92857480560329 4.760755784284379 17.111605686834128 5.460014647786589 C 17.294636568064966 6.159273511288799 17.8407264887112 6.705363431935032 18.53998535221341 6.88839431316587 C 19.239244215715622 7.071425194396709 19.982851059476943 6.862913168906223 20.485 6.343 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -7.84 8.33)" id="Light" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3.91, -20.09)" d="M 1.04 20.132 C 0.8448090308335683 20.32724992574291 0.8448090308335681 20.643750074257095 1.04 20.839000000000002 L 3.161 22.96 C 3.3562499257429073 23.155190969166433 3.6727500742570927 23.155190969166433 3.868 22.96 L 6.343 20.485 C 7.124206778635657 19.703793221364343 7.124206778635657 18.437206778635655 6.343 17.656 C 5.561793221364343 16.874793221364342 4.295206778635658 16.874793221364342 3.514 17.656 Z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>