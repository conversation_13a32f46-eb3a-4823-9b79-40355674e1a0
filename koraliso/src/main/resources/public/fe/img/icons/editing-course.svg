<svg id='Photo_Video_Editing_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -8 9.75)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-3.25" y1="0" x2="3.25" y2="0" />
</g>
<g transform="matrix(1 0 0 1 0 9.75)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-2.75" y1="0" x2="2.75" y2="0" />
</g>
<g transform="matrix(1 0 0 1 8 9.75)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-3.25" y1="0" x2="3.25" y2="0" />
</g>
<g transform="matrix(1 0 0 1 -11.25 9.75)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0" y1="-2" x2="0" y2="2" />
</g>
<g transform="matrix(1 0 0 1 -4.75 9.75)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0" y1="-2" x2="0" y2="2" />
</g>
<g transform="matrix(1 0 0 1 4.75 9.75)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0" y1="-2" x2="0" y2="2" />
</g>
<g transform="matrix(1 0 0 1 11.75 9.75)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0" y1="-2" x2="0" y2="2" />
</g>
<g transform="matrix(1 0 0 1 0.25 -2.9)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -8.85)" d="M 20 0.5 L 4 0.5 C 3.4477152501692068 0.5 3 0.9477152501692065 3 1.5 L 3 14 C 3 14.552284749830793 3.4477152501692068 15 4 15 L 9.464 15 C 9.798278534019305 14.999949976750564 10.110475415284865 15.166930279831302 10.296000000000001 15.445 L 11.167000000000002 16.752 C 11.352399279073405 17.030588308135265 11.66485938962731 17.197974955439737 11.999500000000001 17.197974955439737 C 12.334140610372692 17.197974955439737 12.646600720926598 17.030588308135265 12.832 16.752 L 13.703000000000001 15.444999999999999 C 13.888524584715135 15.166930279831302 14.200721465980696 14.999949976750564 14.535 15 L 20 15 C 20.552284749830793 15 21 14.552284749830793 21 14 L 21 1.5 C 21 0.9477152501692065 20.552284749830793 0.5 20 0.5 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 1 -4.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12.75, -7.5)" d="M 9.5 4.991 L 9.5 10.008 C 9.501641860276374 10.368816356756255 9.697531824156613 10.700767777915596 10.01259294924266 10.876630299413328 C 10.327654074328704 11.052492820911063 10.713021881360168 11.044991003633402 11.021 10.857 L 15.512 8.2 C 15.822143753373783 8.019687778570166 16.0092779858019 7.684598780684778 16.000114307141256 7.325965720375019 C 15.990950628480611 6.96733266006526 15.786947588671984 6.642237878195215 15.468 6.478 L 10.977 4.118 C 10.668056860976591 3.9503219118325505 10.293762118717826 3.9571655830572774 9.99115513930205 4.136025361682851 C 9.688548159886276 4.314885140308424 9.502071774399031 4.6394925147995165 9.5 4.991 Z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>