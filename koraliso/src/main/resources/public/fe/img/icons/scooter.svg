<svg id='Nautic_Sports_Scooter_1_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 10.25 3.25)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="1" y1="-0.75" x2="-1" y2="0.75" />
</g>
<g transform="matrix(1 0 0 1 -8.75 3.25)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0" y1="0.75" x2="0" y2="-0.75" />
</g>
<g transform="matrix(1 0 0 1 1.25 -0.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-13, -11.25)" d="M 23 14.25 C 22.99754314938075 12.709865038995568 22.148366866849212 11.295852043648933 20.79 10.569999999999999 L 16.5 8.29 C 16.242084988499386 8.175755579657748 15.947915011500614 8.175755579657748 15.69 8.29 C 15.43196432417617 8.400727436487616 15.232369627210108 8.614838111414844 15.139999999999999 8.879999999999999 C 14.46022233351284 10.920968232933646 12.551196489349842 12.298366626823405 10.399999999999999 12.299999999999999 L 4 12.299999999999999 C 3.4477152501692068 12.299999999999999 3 12.747715250169206 3 13.299999999999999 L 3 14.299999999999999 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.16 -4.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.91, -7.25)" d="M 13.33 9.25 L 12.23 5.93 C 12.087006077804412 5.504629061779411 11.678152358145752 5.226608532411522 11.23 5.25 L 10.5 5.25" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.5 -1)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12.25, -10.75)" d="M 9.5 12.25 L 9.5 10.25 C 9.5 9.697715250169207 9.947715250169207 9.25 10.5 9.25 L 15 9.25" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 5.94)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -17.7)" d="M 23.5 18.64 C 20.990000000000002 19.16 19.44 16.64 19.44 16.64 L 19.44 16.64 C 18.648482532780257 17.88687957762285 17.286501093914506 18.656042649158604 15.810000000000002 18.69 C 14.349203509162287 18.6193933728045 13.009029358651858 17.858352650215476 12.200000000000003 16.64 L 12.200000000000003 16.64 C 11.40848253278026 17.88687957762285 10.046501093914507 18.656042649158604 8.570000000000004 18.69 C 7.127222057207542 18.61157489714328 5.8051338272995086 17.8597992369995 4.999999999999999 16.66 L 5 16.66 C 5 16.66 3 19.2 0.5 18.68" stroke-linecap="round" />
</g>
</g>
</g>
</svg>