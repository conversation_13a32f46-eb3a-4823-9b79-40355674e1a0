<svg id='Diving_Boat_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -1.75 -5.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10, -6.5)" d="M 3.501 6.5 L 16.501 6.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 -8.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -3.5)" d="M 3.501 3.5 L 20.501 3.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 -11.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -0.5)" d="M 3.501 0.5 L 20.501 0.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 2.58 5.02)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-14.34, -16.77)" d="M 7.674 19.85 L 19.984 13.750000000000002 C 20.115293894025495 13.684811270251332 20.268580782051192 13.680521189867463 20.40331520778007 13.738264515179841 C 20.53804963350895 13.796007840492218 20.64065811876594 13.909967366568667 20.684 14.050000000000002 C 21.207839062677795 15.703378531991039 21.07092946418547 17.49538898346132 20.302 19.050000000000004" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 10.7)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -22.45)" d="M 23.5 23.388 C 21 23.909000000000002 19.44 21.409000000000002 19.44 21.409000000000002 L 19.44 21.409000000000002 C 18.65419150617243 22.65823453127144 17.29442482413928 23.430308626344665 15.819 23.465000000000003 C 14.419674163241968 23.439046701652092 13.146445483107088 22.65032776029707 12.5 21.409 L 12.5 21.409 C 11.556919876739249 22.635714019729697 10.123372563696531 23.387211540284472 8.578 23.465 C 7.113227015319219 23.395528537521177 5.769347220861184 22.631842286165252 4.960000000000001 21.409 L 4.96 21.409 C 4.96 21.409 2.96 23.951 0.5049999999999999 23.429" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.31 1.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12.06, -13.26)" d="M 13.882 16.774 L 10.248 9.739" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -0.12 0.66)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.63, -12.41)" d="M 9.337 13.598 L 13.922 11.229" stroke-linecap="round" />
</g>
</g>
</g>
</svg>