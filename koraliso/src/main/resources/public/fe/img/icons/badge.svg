<svg id='Check_Badge_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.41 0.61)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12.17, -12.37)" d="M 16 9.5 L 11.93 14.926 C 11.796717252339075 15.103675758019062 11.592957934498706 15.21484979628659 11.371418963432237 15.230769912822101 C 11.149879992365769 15.246690029357614 10.93231814693912 15.165792750008842 10.775 15.009 L 8.333 12.566" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 0.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -12)" d="M 10.7 1.121 C 11.015974942845112 0.7261453878692626 11.494282362155632 0.49630043737374296 12 0.49630043737374296 C 12.505717637844366 0.49630043737374296 12.984025057154888 0.7261453878692627 13.299999999999999 1.121 L 14.844999999999999 3.044 C 15.20117960021945 3.4871309458615984 15.758846375966872 3.717889611688117 16.323999999999998 3.6559999999999997 L 18.775999999999996 3.3890000000000002 C 19.277432054537638 3.3355536823642598 19.776107350996128 3.5119616419938144 20.13239023982061 3.8688273272558487 C 20.488673128645093 4.225693012517883 20.664265802314304 4.724655969532055 20.61 5.226 L 20.343 7.677 C 20.28153694305684 8.24230787145706 20.512650583272798 8.799921368813346 20.956 9.155999999999999 L 22.878 10.7 C 23.27251288346103 11.016186959318702 23.50209981284194 11.494416566272248 23.50209981284194 11.999999999999998 C 23.50209981284194 12.50558343372775 23.27251288346103 12.983813040681294 22.878 13.299999999999997 L 20.956 14.844999999999999 C 20.512660749048756 15.20064668313746 20.281508965585793 15.757975126788775 20.343 16.323 L 20.61 18.775 C 20.664824016860052 19.27674928937869 20.489085854079754 19.77628192831765 20.132183891198704 20.1331838911987 C 19.775281928317657 20.49008585407975 19.27574928937869 20.66582401686005 18.774 20.610999999999997 L 16.322000000000003 20.343999999999998 C 15.756746864388239 20.28291852383758 15.199309880081083 20.513959004824926 14.843000000000004 20.956999999999997 L 13.3 22.877 C 12.984025057154888 23.271854612130735 12.505717637844368 23.501699562626257 12 23.501699562626257 C 11.494282362155634 23.501699562626257 11.015974942845112 23.271854612130735 10.700000000000001 22.877 L 9.157 20.955 C 8.800690119918919 20.511959004824927 8.243253135611763 20.28091852383758 7.677999999999999 20.342 L 5.226 20.608999999999998 C 4.724250710621311 20.66382401686005 4.224718071682345 20.488085854079753 3.8678161088012954 20.131183891198702 C 3.5109141459202466 19.774281928317656 3.335175983139947 19.27474928937869 3.3900000000000006 18.773 L 3.657 16.320999999999998 C 3.7184910344142064 15.755975126788774 3.487339250951245 15.198646683137458 3.0439999999999996 14.842999999999998 L 1.122 13.3 C 0.7274871165389697 12.983813040681298 0.49790018715805995 12.505583433727752 0.49790018715805995 12 C 0.49790018715805995 11.49441656627225 0.7274871165389689 11.016186959318704 1.1219999999999992 10.700000000000001 L 3.044 9.156 C 3.4873494167272026 8.799921368813347 3.718463056943162 8.242307871457061 3.657 7.677000000000001 L 3.39 5.226 C 3.3356260295561384 4.724270481964763 3.5114718150406437 4.224918461308254 3.8682282839292768 3.867967680617394 C 4.22498475281791 3.511016899926534 4.724240948957933 3.3348992363541443 5.226 3.389 L 7.678 3.6559999999999997 C 8.243153624033127 3.717889611688117 8.800820399780548 3.487130945861598 9.157 3.044 Z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>