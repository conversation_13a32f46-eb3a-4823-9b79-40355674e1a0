<svg id='Outdoors_Water_Sun_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0 4.4)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -16.4)" d="M 23.251 17 C 20.883616690257348 17.959464447509834 18.1680185671523 17.302490857134938 16.501 15.367 C 15.37479325810563 16.68030628205809 13.731062142231192 17.436090309426767 12.001000000000001 17.436090309426767 C 10.27093785776881 17.436090309426767 8.627206741894373 16.68030628205809 7.501000000000001 15.367 C 5.832669769789211 17.301361119461546 3.1163562304219017 17.95599630366398 0.7499999999999991 16.994" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0 8.71)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -20.72)" d="M 0.75 21.313 C 3.1171258443531022 22.273343336428102 5.8330702567713555 21.616688331825642 7.499999999999998 19.681 C 8.626206741894372 20.99430628205809 10.26993785776881 21.750090309426767 12 21.750090309426767 C 13.73006214223119 21.750090309426767 15.373793258105628 20.99430628205809 16.5 19.681 C 18.167018567152297 21.616490857134938 20.88261669025735 22.273464447509834 23.25 21.314" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0 -2.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -9.75)" d="M 6 12.75 C 6 9.436291501015239 8.686291501015239 6.75 12 6.75 C 15.313708498984761 6.75 18 9.436291501015237 18 12.749999999999998" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -10.13 0.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-1.88, -12.75)" d="M 3.001 12.75 L 0.751 12.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 10.12 0.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-22.13, -12.75)" d="M 23.251 12.75 L 21.001 12.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0 -8.63)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -3.38)" d="M 12.001 2.25 L 12.001 4.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 6.75 -6)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-18.75, -6)" d="M 18.001 6.75 L 19.501 5.25" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -6.75 -6)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.25, -6)" d="M 6.001 6.75 L 4.501 5.25" stroke-linecap="round" />
</g>
</g>
</g>
</svg>