<svg id='Human_Tube_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 0.25 10.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -22.25)" d="M 21.5 21 L 2.5 21 C 1.94772 21 1.5 21.4477 1.5 22 L 1.5 22.5 C 1.5 23.0523 1.94772 23.5 2.5 23.5 L 21.5 23.5 C 22.0523 23.5 22.5 23.0523 22.5 22.5 L 22.5 22 C 22.5 21.4477 22.0523 21 21.5 21 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 -10)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -1.75)" d="M 21.5 0.5 L 2.5 0.5 C 1.94772 0.5 1.5 0.947715 1.5 1.5 L 1.5 2 C 1.5 2.55228 1.94772 3 2.5 3 L 21.5 3 C 22.0523 3 22.5 2.55228 22.5 2 L 22.5 1.5 C 22.5 0.947715 22.0523 0.5 21.5 0.5 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -8.25 0.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-3.5, -12)" d="M 3.5 21 L 3.5 3" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 8.75 0.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-20.5, -12)" d="M 20.5 3 L 20.5 21" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 -4)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -7.75)" d="M 12 10 C 13.2426 10 14.25 8.99264 14.25 7.75 C 14.25 6.50736 13.2426 5.5 12 5.5 C 10.7574 5.5 9.75 6.50736 9.75 7.75 C 9.75 8.99264 10.7574 10 12 10 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 4.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -16)" d="M 13.5 21 L 14 16.5 L 15.5 16.5 L 15.5 14.5 C 15.5 13.5717 15.1313 12.6815 14.4749 12.0251 C 13.8185 11.3687 12.9283 11 12 11 C 11.0717 11 10.1815 11.3687 9.52513 12.0251 C 8.86875 12.6815 8.5 13.5717 8.5 14.5 L 8.5 16.5 L 10 16.5 L 10.5 21" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -6.38 6.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.38, -18)" d="M 5.5 18.25 C 5.36193 18.25 5.25 18.1381 5.25 18 C 5.25 17.8619 5.36193 17.75 5.5 17.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -6.13 6.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.63, -18)" d="M 5.5 18.25 C 5.63807 18.25 5.75 18.1381 5.75 18 C 5.75 17.8619 5.63807 17.75 5.5 17.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.38 1)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.38, -12.75)" d="M 6.5 13 C 6.36193 13 6.25 12.8881 6.25 12.75 C 6.25 12.6119 6.36193 12.5 6.5 12.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.13 1)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.63, -12.75)" d="M 6.5 13 C 6.63807 13 6.75 12.8881 6.75 12.75 C 6.75 12.6119 6.63807 12.5 6.5 12.5" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.88 -4.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.88, -7.5)" d="M 6 7.75 C 5.86193 7.75 5.75 7.63807 5.75 7.5 C 5.75 7.36193 5.86193 7.25 6 7.25" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -5.63 -4.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.13, -7.5)" d="M 6 7.75 C 6.13807 7.75 6.25 7.63807 6.25 7.5 C 6.25 7.36193 6.13807 7.25 6 7.25" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 4.87 -5.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-16.63, -6)" d="M 16.75 6.25 C 16.6119 6.25 16.5 6.13807 16.5 6 C 16.5 5.86193 16.6119 5.75 16.75 5.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 5.12 -5.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-16.88, -6)" d="M 16.75 6.25 C 16.8881 6.25 17 6.13807 17 6 C 17 5.86193 16.8881 5.75 16.75 5.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 6.12 -0.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-17.88, -11)" d="M 18 11.25 C 17.8619 11.25 17.75 11.1381 17.75 11 C 17.75 10.8619 17.8619 10.75 18 10.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 6.37 -0.75)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-18.13, -11)" d="M 18 11.25 C 18.1381 11.25 18.25 11.1381 18.25 11 C 18.25 10.8619 18.1381 10.75 18 10.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 6.12 6.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-17.88, -18)" d="M 18 18.25 C 17.8619 18.25 17.75 18.1381 17.75 18 C 17.75 17.8619 17.8619 17.75 18 17.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 6.37 6.25)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-18.13, -18)" d="M 18 18.25 C 18.1381 18.25 18.25 18.1381 18.25 18 C 18.25 17.8619 18.1381 17.75 18 17.75" stroke-linecap="round" />
</g>
</g>
</g>
</svg>