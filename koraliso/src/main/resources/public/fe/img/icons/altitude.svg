<svg id='Rocky_Mountain_Height_Altitude_1_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 2.33 11)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-14.33, -23)" d="M 23 23 L 5.66577 23" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 5 1.15)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-17, -13.16)" d="M 23 23 L 20.4688 8.03125 L 16.0625 3.3125 L 11 12" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 2.72 -2.62)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-14.73, -9.38)" d="M 16.0625 3.3125 L 16.8438 10.75 L 12.6094 15.4531" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -1.12 5.5)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-10.88, -17.5)" d="M 5.66577 22.9997 L 7.5 15 L 11 12 L 16.0938 22.9997" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -10 0)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-2, -12)" d="M 3 1 L 1 1 L 1 23 L 3 23" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -10 0)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-2, -12)" d="M 1 12 L 3 12" stroke-linecap="round" />
</g>
</g>
</g>
</svg>