<svg id='Sport_Para_Sailing_1_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 2.52 0.25)" >
<circle style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="0.75" />
</g>
<g transform="matrix(1 0 0 1 2.52 2.75)" >
<polyline style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" points="-3,-0.5 -2,0.5 2,0.5 3,-0.5 " />
</g>
<g transform="matrix(1 0 0 1 2.52 6.5)" >
<polyline style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" points="-2.5,0.75 -2,-0.75 2,-0.75 2.5,0.75 " />
</g>
<g transform="matrix(1 0 0 1 2.52 -7.97)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-14.52, -4.03)" d="M 23.231 6.432 C 22.492 3.418 18.877 0.75 14.519 0.75 C 10.161000000000001 0.75 6.546 3.418 5.807 6.432 L 10.807 7.3180000000000005 C 11.718402129094331 6.218664468357985 13.092426296350116 5.6101257477306925 14.519 5.674 C 15.970253500543185 5.594070824680761 17.3741836015286 6.203379453513925 18.307 7.318 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -4.2 -2.66)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="2" y1="2.909" x2="-2" y2="-2.909" />
</g>
<g transform="matrix(1 0 0 1 9.27 -2.66)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-1.9620000000000015" y1="2.909" x2="1.9620000000000015" y2="-2.909" />
</g>
<g transform="matrix(-1 0 0 -1 2.52 4.5)" >
<rect style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x="-0.212" y="-1.25" rx="0" ry="0" width="0.424" height="2.5" />
</g>
<g transform="matrix(1 0 0 1 6.3 -3.81)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0" y1="-0.875" x2="0" y2="0.875" />
</g>
<g transform="matrix(1 0 0 1 -1.2 -3.81)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="0" y1="-0.875" x2="0" y2="0.875" />
</g>
<g transform="matrix(1 0 0 1 -6.86 9.12)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-5.14, -21.13)" d="M 0.769 19 L 9.519 20 L 9.519 22.75 C 8.14233815771472 23.09707394502281 6.726658170620992 23.26512712487724 5.306999999999999 23.25 C 4.135226446521387 23.31245020154295 2.9886646757885487 22.894316887415286 2.132187185217769 22.092194953760416 C 1.2757096946469897 21.29007302010554 0.7834040362956616 20.173348120569102 0.7690000000000001 19 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -7.95 6.2)" >
<line style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" x1="-0.9184999999999999" y1="1.0690000000000008" x2="0.9184999999999999" y2="-1.0690000000000008" />
</g>
</g>
</g>
</svg>