<svg id='Nautic_Sports_Sailing_Person_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.67 0 0 0.67 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -7.75 -4.76)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4, -6.99)" d="M 1.499 6.99 C 1.499 8.370711874576983 2.6182881254230166 9.49 3.999 9.49 C 5.379711874576984 9.49 6.4990000000000006 8.370711874576983 6.4990000000000006 6.99 C 6.499 5.609288125423017 5.379711874576984 4.49 3.9989999999999997 4.49 C 2.618288125423016 4.49 1.4989999999999997 5.609288125423016 1.4989999999999997 6.99 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 4.94 -1.74)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-16.69, -10.01)" d="M 10.038 1.175 L 16.060000000000002 19.175 C 16.128658506415004 19.367674701882002 16.3077487006779 19.499101292516396 16.512145627164607 19.50681022832139 C 16.716542553651312 19.514519164126387 16.905023536219602 19.396955710549204 16.988000000000003 19.21 C 17.958514520617303 16.76897303734727 20.27340561118884 15.126614603715048 22.898 15.017000000000001 C 23.046523414337372 15.009503157443703 23.18400214012641 14.936282378101303 23.273105889565816 14.817219667353637 C 23.362209639005222 14.69815695660597 23.393690383999505 14.545609766311653 23.359 14.401000000000002 C 22.677 11.758 19.382 1.07 10.534 0.512 C 10.368292547153555 0.504201098604266 10.209506026193072 0.5790976521250408 10.110131854013256 0.7119304669541096 C 10.01075768183344 0.8447632817831785 9.98373615106346 1.018235152248886 10.038 1.175 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 5.83 -1.26)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-17.59, -10.49)" d="M 13.154 10.49 L 22.023 10.49" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 4.58 -4.26)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-16.33, -7.49)" d="M 12.15 7.49 L 20.517 7.49" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 -3.7 2.88)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-8.05, -14.63)" d="M 10.11 20.222 C 9.641823719453013 19.751439658393167 9.00478565005036 19.487837698640348 8.341 19.490000000000002 L 6 19.490000000000002 C 5.386517958463886 19.49010247300368 4.834806326043431 19.116617857517326 4.607 18.547 L 2.607 13.547 C 2.422592447837563 13.084936719582812 2.479279951207028 12.561504806959064 2.7583342450075166 12.149634291486125 C 3.0373885388080053 11.737763776013184 3.5024976201424356 11.491049401378346 3.9999999999999982 11.491 L 6.549 11.491 C 7.133479242535034 11.490175084073194 7.699346979967409 11.285401696589853 8.149000000000001 10.911999999999999 L 10.037 9.338999999999999 C 10.675446024338243 8.859305194407026 11.577785830922302 8.963918402619047 12.089544603584205 9.57696276570362 C 12.60130337624611 10.19000712878819 12.543034108064028 11.096520086370209 11.957 11.639 L 10.07 13.216 C 9.081163917416308 14.03888716450317 7.835444690902936 14.489624538708803 6.5489999999999995 14.489999999999998 L 6.213 14.49 L 7.013 16.490000000000002 L 8.34 16.490000000000002 C 9.79966483275033 16.48552920962449 11.200436083061373 17.0652828633522 12.23 18.1 L 13.6 19.662" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0.25 10.69)" >
<path style="stroke: rgb(51,61,76); stroke-width: 1.4925373134328357; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -22.44)" d="M 23.5 23.378 C 21 23.899 19.44 21.399 19.44 21.399 L 19.44 21.399 C 18.65419150617243 22.648234531271438 17.29442482413928 23.420308626344664 15.819 23.455000000000002 C 14.419890098633545 23.42924336493235 13.146713771820362 22.64094045694769 12.5 21.4 L 12.5 21.4 C 11.556919876739249 22.626714019729697 10.123372563696531 23.37821154028447 8.578 23.456 C 7.113227015319219 23.386528537521176 5.769347220861184 22.622842286165252 4.960000000000001 21.4 L 4.96 21.4 C 4.96 21.4 2.96 23.942 0.5049999999999999 23.419999999999998" stroke-linecap="round" />
</g>
</g>
</g>
</svg>