<svg id='Arrow<PERSON>_Curvy_Right_20' width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='20' height='20' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(1.14 0 0 1.14 10 10)" >
<g style="" >
<g transform="matrix(1 0 0 1 -0.1 -1.05)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.96, -6.01)" d="M 1.17743 1.23111 C 4.59247 0.987673 6.76044 0.950062 9.72223 1.10642 C 10.9326 1.17032 12.0701 1.96938 12.1674 3.17754 C 12.1861 3.41002 12.1892 3.63979 12.1765 3.87353 C 12.106 5.16745 10.8452 5.99055 9.5494 5.99055 L 3.83928 5.99055 C 2.74022 5.99055 1.61578 6.56534 1.40873 7.64472 C 1.31274 8.14514 1.32934 8.62713 1.45451 9.15585 C 1.6983 10.1856 2.70247 10.8149 3.75825 10.8869 C 6.92869 11.1031 9.16238 11.0124 12.7458 10.75" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 4.52 3.76)" >
<path style="stroke: rgb(51,61,76); stroke-width: 0.8771929824561404; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-11.58, -10.82)" d="M 10.3342 8.33439 C 11.426 9.22889 11.9715 9.7743 12.8226 10.8228 C 11.9715 11.8712 11.426 12.4166 10.3342 13.3111" stroke-linecap="round" />
</g>
</g>
</g>
</svg>