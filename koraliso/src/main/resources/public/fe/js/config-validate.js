/**
 * Configurazione globale per jQuery Validate
 * Disabilita i messaggi automatici e usa solo classi CSS + label personalizzate
 * 
 * Includere questo file prima di qualsiasi script che usa jQuery Validate
 */

$(document).ready(function() {
    configureJQueryValidate();
});

/**
 * Configura jQuery Validate per disabilitare i messaggi automatici
 */
function configureJQueryValidate() {
    // Configurazione personalizzata per jQuery Validate
    $.validator.setDefaults({
        errorElement: 'span',
        errorClass: 'sr-only', // Nasconde visivamente i messaggi di errore
        validClass: 'is-valid',
        
        // Non inserire i messaggi di errore nel DOM
        errorPlacement: function(error, element) {
            // Non inserire nulla - i messaggi sono già nel HTML
            return false;
        },
        
        highlight: function(element, errorClass, validClass) {
            const $element = $(element);
            
            // Gestione speciale per Choice.js
            if ($element.hasClass('choice-input')) {
                const choiceContainer = $element.closest('.choices');
                if (choiceContainer.length) {
                    choiceContainer.addClass('is-invalid').removeClass('is-valid');
                }
            }
            
            $element.addClass('is-invalid').removeClass('is-valid');
            
            // Gestione speciale per checkbox e radio in form-check
            if ($element.is(':checkbox, :radio') && $element.closest('.form-check').length) {
                const formCheck = $element.closest('.form-check');
                const customError = formCheck.find('.invalid-tooltip, .invalid-feedback');
                if (customError.length) {
                    customError.show();
                }
            } else {
                // Mostra la label di errore personalizzata se esiste
                const customError = $element.siblings('.invalid-tooltip, .invalid-feedback');
                const parentError = $element.closest('.form-group, .mb-3, .col').find('.invalid-feedback, .invalid-tooltip');
                
                if (customError.length) {
                    customError.show();
                } else if (parentError.length) {
                    parentError.show();
                }
            }
        },
        
        unhighlight: function(element, errorClass, validClass) {
            const $element = $(element);
            
            // Gestione speciale per Choice.js
            if ($element.hasClass('choice-input')) {
                const choiceContainer = $element.closest('.choices');
                if (choiceContainer.length) {
                    choiceContainer.removeClass('is-invalid').addClass('is-valid');
                }
            }
            
            $element.removeClass('is-invalid').addClass('is-valid');
            
            // Gestione speciale per checkbox e radio in form-check
            if ($element.is(':checkbox, :radio') && $element.closest('.form-check').length) {
                const formCheck = $element.closest('.form-check');
                const customError = formCheck.find('.invalid-tooltip, .invalid-feedback');
                if (customError.length) {
                    customError.hide();
                }
            } else {
                // Nascondi la label di errore personalizzata se esiste
                const customError = $element.siblings('.invalid-tooltip, .invalid-feedback');
                const parentError = $element.closest('.form-group, .mb-3, .col').find('.invalid-feedback, .invalid-tooltip');
                
                if (customError.length) {
                    customError.hide();
                } else if (parentError.length) {
                    parentError.hide();
                }
            }
        },
        
        // Non validare durante la digitazione per evitare interferenze
        onkeyup: false,
        onfocusout: false,
        onclick: false
    });
    
    // Aggiungi metodi di validazione personalizzati
    addCustomValidationMethods();
    
    // Setup per rimuovere errori durante la digitazione
    setupErrorRemovalListeners();
}

/**
 * Aggiunge metodi di validazione personalizzati
 */
function addCustomValidationMethods() {
    // Validazione per codice fiscale italiano
    $.validator.addMethod("codicefiscale", function(value, element) {
        if (!value) return true; // Non obbligatorio se non specificato
        const cf = value.toUpperCase().replace(/\s/g, '');
        const cfRegex = /^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$/;
        return cfRegex.test(cf);
    }, "Inserisci un codice fiscale valido");
    
    // Validazione per partita IVA italiana
    $.validator.addMethod("partitaiva", function(value, element) {
        if (!value) return true; // Non obbligatorio se non specificato
        const piva = value.replace(/\s/g, '');
        const pivaRegex = /^[0-9]{11}$/;
        return pivaRegex.test(piva);
    }, "Inserisci una partita IVA valida (11 cifre)");
    
    // Validazione per telefono
    $.validator.addMethod("telefono", function(value, element) {
        if (!value) return true; // Non obbligatorio se non specificato
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
        return phoneRegex.test(value);
    }, "Inserisci un numero di telefono valido");
}

/**
 * Setup listener per rimuovere errori quando l'utente modifica i campi
 */
function setupErrorRemovalListeners() {
    // Listener globale per tutti i form con jQuery Validate
    $(document).on('input change', 'input, textarea, select', function() {
        const $this = $(this);
        const form = $this.closest('form');
        
        // Controlla se il form ha jQuery Validate attivo
        if (form.length && form.data('validator')) {
            if ($this.hasClass('is-invalid')) {
                $this.removeClass('is-invalid');
                
                // Gestione speciale per checkbox e radio in form-check
                if ($this.is(':checkbox, :radio') && $this.closest('.form-check').length) {
                    const formCheck = $this.closest('.form-check');
                    const customError = formCheck.find('.invalid-tooltip, .invalid-feedback');
                    if (customError.length) {
                        customError.hide();
                    }
                } else {
                    // Nascondi le label di errore personalizzate
                    const customError = $this.siblings('.invalid-tooltip, .invalid-feedback');
                    const parentError = $this.closest('.form-group, .mb-3, .col').find('.invalid-feedback, .invalid-tooltip');
                    
                    if (customError.length) {
                        customError.hide();
                    } else if (parentError.length) {
                        parentError.hide();
                    }
                }
            }
        }
    });
    
    // Setup per Choice.js se presente
    if (window.Choices) {
        document.addEventListener('choice', function(event) {
            const element = event.detail.choice ? event.target : null;
            if (element) {
                const $element = $(element);
                const form = $element.closest('form');
                
                if (form.length && form.data('validator') && $element.hasClass('is-invalid')) {
                    $element.removeClass('is-invalid');
                    const choiceContainer = $element.closest('.choices');
                    if (choiceContainer.length) {
                        choiceContainer.removeClass('is-invalid');
                    }
                }
            }
        });
    }
    
    // Aggiungi CSS personalizzato per gestire checkbox/radio con invalid-tooltip
    addCheckboxTooltipStyles();
}

/**
 * Aggiunge stili CSS personalizzati per gestire checkbox/radio con tooltip
 */
function addCheckboxTooltipStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* Gestione invalid-tooltip per checkbox e radio */
        .form-check .form-check-input.is-invalid ~ .invalid-tooltip {
            display: block !important;
        }
        
        .form-check .form-check-input:not(.is-invalid) ~ .invalid-tooltip {
            display: none !important;
        }
        
        /* Posizionamento del tooltip per checkbox */
        .form-check {
            position: relative;
        }
        
        .form-check .invalid-tooltip {
            position: absolute;
            top: 100%;
            left: 0;
            margin-top: 0.25rem;
            z-index: 5;
        }
        
        /* Stile alternativo per invalid-feedback in form-check */
        .form-check .form-check-input.is-invalid ~ .invalid-feedback {
            display: block !important;
        }
        
        .form-check .form-check-input:not(.is-invalid) ~ .invalid-feedback {
            display: none !important;
        }
        
        /* Gestione per radio button groups */
        .form-check-inline .invalid-tooltip,
        .form-check-inline .invalid-feedback {
            position: static;
            margin-top: 0.25rem;
        }
    `;
    document.head.appendChild(style);
}

/**
 * Utility function per inizializzare un form con jQuery Validate
 * @param {string} formSelector - Selettore del form
 * @param {object} options - Opzioni aggiuntive per il validator
 * @returns {object} - Istanza del validator
 */
function initFormValidation(formSelector, options = {}) {
    const defaultOptions = {
        ignore: ':hidden:not(.choice-input)'
    };
    
    const finalOptions = $.extend({}, defaultOptions, options);
    
    return $(formSelector).validate(finalOptions);
}