$(function () {

    bindRegister();
});

function bindRegister() {
    // Form submission handling
    $('#register-form').submit(function (e) {
        e.preventDefault();
        if ($("#register-form").valid()) {
            const formData = new FormData(this);
            $.blockUI();
            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    $.unblockUI();
                    window.location.href = response;
                },
                error: function (error) {
                    // Handle errors
                    $.unblockUI();
                    Swal.fire({
                        text: error.responseText,
                        icon: 'error',
                        timer: 3000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    });
                    console.error('Error during account registration', error);
                }
            });
        }
    });
}
