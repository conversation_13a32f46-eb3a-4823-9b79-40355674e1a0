var pond;

$(function () {
    bindSubmit();
    bindFilePond();
});

function bindSubmit() {
    // Form submission handling
    $('#form-account-edit').submit(function (e) {
        e.preventDefault();
        if ($("#form-account-edit").valid()) {
            const formData = new FormData(this);
            $.blockUI();
            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    // todo: handle success response
                    $.unblockUI();
                    window.location.href = response;
                },
                error: function (error) {
                    // Handle errors
                    $.unblockUI();
                    Swal.fire({
                        text: error.responseText,
                        icon: 'error',
                        timer: 3000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    });
                    console.error('Error during account info save', error);
                }
            });
        }
    });

    // Form submission handling
    $('#form-account-password-edit').submit(function (e) {
        e.preventDefault();
        if ($("#form-account-password-edit").valid()) {
            const formData = new FormData(this);
            $.blockUI();
            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    // todo: handle success response
                    $.unblockUI();
                    window.location.href = response;
                },
                error: function (error) {
                    // Handle errors
                    $.unblockUI();
                    Swal.fire({
                        text: error.responseText,
                        icon: 'error',
                        timer: 3000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    });
                    console.error('Error during password save', error);
                }
            });
        }
    });

    // Form submission handling
    $('#form-account-delete').submit(function (e) {
        e.preventDefault();
        if ($("#form-account-delete").valid()) {
            const formData = new FormData(this);
            $.blockUI();
            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    // todo: handle success response
                    $.unblockUI();
                    window.location.href = response;
                },
                error: function (error) {
                    // Handle errors
                    $.unblockUI();
                    Swal.fire({
                        text: error.responseText,
                        icon: 'error',
                        timer: 3000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    });
                    console.error('Error during password save', error);
                }
            });
        }
    });

    // Form submission handling
    $('#form-account-image').submit(function (e) {
        e.preventDefault();
        if ($("#form-account-image").valid()) {
            const formData = new FormData(this);
            const pondFiles = pond.getFiles();
            if (pondFiles.length > 0) {
                formData.append('file', pondFiles[0].file);
            }

            $.blockUI();
            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    // todo: handle success response
                    $.unblockUI();
                    window.location.href = response;
                },
                error: function (error) {
                    // Handle errors
                    $.unblockUI();
                    Swal.fire({
                        text: error.responseText,
                        icon: 'error',
                        timer: 3000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    });
                    console.error('Error during password save', error);
                }
            });
        }
    });
}

function bindFilePond() {
    // We register the plugins required to do
    // image previews, cropping, resizing, etc.
    FilePond.registerPlugin(
        FilePondPluginFileValidateType,
        FilePondPluginImageExifOrientation,
        FilePondPluginImagePreview,
        FilePondPluginImageCrop,
        FilePondPluginImageResize,
        FilePondPluginImageTransform,
        FilePondPluginImageEdit
    );

    // Select the file input and use
    // create() to turn it into a pond
    pond = FilePond.create(
        document.querySelector('input[type="file"]'),
        {
            labelIdle: `Trascina un'immagine o <span class="filepond--label-action">Sfoglia</span>`,
            imagePreviewHeight: 170,
            imageCropAspectRatio: '1:1',
            imageResizeTargetWidth: 200,
            imageResizeTargetHeight: 200,
            stylePanelLayout: 'compact circle',
            styleLoadIndicatorPosition: 'center bottom',
            styleProgressIndicatorPosition: 'right bottom',
            styleButtonRemoveItemPosition: 'center bottom',
            styleButtonProcessItemPosition: 'right bottom',
        }
    );

    // Load initial image if present
    var imageId = pageVariables.get("imageId");
    if (typeof imageId !== "undefined" && imageId) {
        var images = imageId.replace("[", "").replace("]", "");
        if (images.includes(",")) {
            var imagesToLoad = images.split(", ");
            imagesToLoad.forEach(function (element) {
                var image = appRoutes.get("BE_IMAGE") + "?oid=" + element;
                pond.addFile(image);
            });
        } else {
            var image = appRoutes.get("BE_IMAGE") + "?oid=" + imageId.replace("[", "").replace("]", "");
            pond.addFile(image);
        }
    }
}