// Map to store all selected filters
const filtersMap = new Map();

// Map to store all Choices instances
const choicesMap = new Map();

// Debounced AJAX update function (will be initialized after the function is defined)
let debouncedUpdateResults;

$(function () {
    
    // Initialize Choices instances
    initializeChoicesInstances();
    
    // Initialize filter tracking
    initializeFilterTracking();
    
    // Initialize selected filters display
    initializeSelectedFiltersDisplay();
    
    /**
     * Initialize Choices instances for all select elements with data-select attribute
     */
    function initializeChoicesInstances() {
        const selectElements = document.querySelectorAll('select');
        
        selectElements.forEach(selectElement => {
            const selectId = selectElement.id || selectElement.name;
            if (!selectId) {
                console.warn('Select element without id or name found, skipping Choices initialization');
                return;
            }
            
            try {
                // Parse data-select attribute for configuration
                let config = {
                    classNames: {
                        containerInner: ['form-select', 'form-select-lg']
                    },
                    searchEnabled: true
                };
                
                // Try to parse the data-select attribute if it contains JSON
                const dataSelectAttr = selectElement.getAttribute('data-select');
                if (dataSelectAttr && dataSelectAttr.trim() !== '') {
                    try {
                        // Decode HTML entities and parse JSON
                        const decodedConfig = dataSelectAttr.replace(/&quot;/g, '"');
                        const parsedConfig = JSON.parse(decodedConfig);
                        config = { ...config, ...parsedConfig };
                    } catch (parseError) {
                        console.warn(`Failed to parse data-select config for ${selectId}:`, parseError);
                    }
                }
                
                // Create Choices instance
                const choicesInstance = new Choices(`#${selectId}`, config);
                
                // Store in the map
                choicesMap.set(selectId, choicesInstance);
                
                console.log(`Choices instance created for: ${selectId}`);
                
            } catch (error) {
                console.error(`Failed to initialize Choices for ${selectId}:`, error);
            }
        });
        
        console.log(`Initialized ${choicesMap.size} Choices instances`);
    }
    
    /**
     * Initialize filter tracking for all elements in filterSidebar
     */
    function initializeFilterTracking() {
        const filterSidebar = document.getElementById('filterSidebar');
        if (!filterSidebar) return;
        
        // Track all input elements in the sidebar
        trackFilterChanges(filterSidebar);
        
        // Log initial state
        console.log('Filter tracking initialized');
        logCurrentFilters();
    }
    
    /**
     * Set up event listeners for all filter elements
     * @param {HTMLElement} container - The container element to search for filters
     */
    function trackFilterChanges(container) {
        // Track checkboxes
        const checkboxes = container.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', handleFilterChange);
            // Set initial value
            updateFilterValue(checkbox.name || checkbox.id, getCheckboxValue(checkbox));
        });
        
        // Track regular select elements
        const selects = container.querySelectorAll('select:not([multiple])');
        selects.forEach(select => {
            select.addEventListener('change', handleFilterChange);
            // Set initial value
            updateFilterValue(select.name || select.id, getSelectValue(select));
        });
        
        // Track multi-select elements
        const multiSelects = container.querySelectorAll('select[multiple]');
        multiSelects.forEach(multiSelect => {
            multiSelect.addEventListener('change', handleFilterChange);
            // Set initial value
            updateFilterValue(multiSelect.name || multiSelect.id, getMultiSelectValue(multiSelect));
        });
        
        // Track radio buttons
        const radios = container.querySelectorAll('input[type="radio"]');
        radios.forEach(radio => {
            radio.addEventListener('change', handleFilterChange);
            // Set initial value for checked radios
            if (radio.checked) {
                updateFilterValue(radio.name, getRadioValue(radio.name));
            }
        });
        
        // Track text inputs and search inputs
        const textInputs = container.querySelectorAll('input[type="text"], input[type="search"]');
        textInputs.forEach(input => {
            input.addEventListener('input', debounce(handleFilterChange, 300));
            // Set initial value
            updateFilterValue(input.name || input.id, getTextInputValue(input));
        });
    }
    
    /**
     * Handle filter change events
     * @param {Event} event - The change event
     */
    function handleFilterChange(event) {
        const element = event.target;
        const filterName = element.name || element.id;
        
        if (!filterName) return;
        
        let value;
        
        // Determine the type of input and get appropriate value
        if (element.type === 'checkbox') {
            value = getCheckboxValue(element);
        } else if (element.type === 'radio') {
            value = getRadioValue(element.name);
        } else if (element.tagName === 'SELECT') {
            if (element.multiple) {
                value = getMultiSelectValue(element);
            } else {
                value = getSelectValue(element);
            }
        } else if (element.type === 'text' || element.type === 'search') {
            value = getTextInputValue(element);
        }
        
        // Update the filters map
        updateFilterValue(filterName, value);
        
        // Log the change
        console.log(`Filter changed: ${filterName} = ${value}`);
        logCurrentFilters();
        
        // Trigger custom event for other parts of the application
        triggerFilterChangeEvent(filterName, value);
    }
    
    /**
     * Get checkbox value
     * @param {HTMLInputElement} checkbox - The checkbox element
     * @returns {string} "true" or "false"
     */
    function getCheckboxValue(checkbox) {
        return checkbox.checked ? "true" : "false";
    }
    
    /**
     * Get radio button value for a group
     * @param {string} radioName - The name of the radio group
     * @returns {string} The value of the selected radio or empty string
     */
    function getRadioValue(radioName) {
        const selectedRadio = document.querySelector(`input[type="radio"][name="${radioName}"]:checked`);
        return selectedRadio ? selectedRadio.value : "";
    }
    
    /**
     * Get select value
     * @param {HTMLSelectElement} select - The select element
     * @returns {string} The selected value or empty string
     */
    function getSelectValue(select) {
        return select.value || "";
    }
    
    /**
     * Get multi-select values
     * @param {HTMLSelectElement} multiSelect - The multi-select element
     * @returns {string} Selected values joined with "|" or empty string
     */
    function getMultiSelectValue(multiSelect) {
        const selectedOptions = Array.from(multiSelect.selectedOptions);
        const values = selectedOptions.map(option => option.value).filter(value => value !== "");
        return values.length > 0 ? values.join("|") : "";
    }
    
    /**
     * Get text input value
     * @param {HTMLInputElement} input - The text input element
     * @returns {string} The input value or empty string
     */
    function getTextInputValue(input) {
        return input.value.trim() || "";
    }
    
    /**
     * Update filter value in the map
     * @param {string} filterName - The name of the filter
     * @param {string} value - The filter value
     */
    function updateFilterValue(filterName, value) {
        if (value === "" || value === "false") {
            // Remove empty or false values from the map
            filtersMap.delete(filterName);
        } else {
            // Add or update the filter value
            filtersMap.set(filterName, value);
        }
        
        // Update the visual display
        updateSelectedFiltersDisplay();
    }
    
    /**
     * Initialize selected filters display
     */
    function initializeSelectedFiltersDisplay() {
        const container = document.getElementById('selected-filters-container');
        if (!container) {
            console.warn('Selected filters container not found');
            return;
        }
        
        // Clear any existing static content
        container.innerHTML = '';
        
        // Set up event delegation for remove buttons
        container.addEventListener('click', handleFilterRemove);
        
        console.log('Selected filters display initialized');
    }
    
    /**
     * Update the visual display of selected filters
     */
    function updateSelectedFiltersDisplay() {
        const container = document.getElementById('selected-filters-container');
        if (!container) return;

        // Clear existing content
        container.innerHTML = '';

        // If no filters, show message and return
        if (filtersMap.size === 0) {
            container.style.display = 'flex';
            container.innerHTML = 'No filters selected';
            return;
        }

        container.style.display = 'flex';

        // Create filter buttons for each active filter
        filtersMap.forEach((value, filterName) => {
            const filterButtons = createFilterButtons(filterName, value);
            filterButtons.forEach(button => {
                container.appendChild(button);
            });
        });
    }
    
    /**
     * Create filter button(s) for a given filter
     * @param {string} filterName - The name of the filter
     * @param {string} value - The filter value
     * @returns {HTMLElement[]} Array of button elements
     */
    function createFilterButtons(filterName, value) {
        const buttons = [];
        
        // Handle multi-value filters (separated by |)
        if (value.includes('|')) {
            const values = value.split('|');
            values.forEach(singleValue => {
                const button = createSingleFilterButton(filterName, singleValue, singleValue);
                buttons.push(button);
            });
        } else {
            const displayText = getFilterDisplayText(filterName, value);
            const button = createSingleFilterButton(filterName, value, displayText);
            buttons.push(button);
        }
        
        return buttons;
    }
    
    /**
     * Create a single filter button
     * @param {string} filterName - The name of the filter
     * @param {string} value - The filter value
     * @param {string} displayText - The text to display
     * @returns {HTMLElement} Button element
     */
    function createSingleFilterButton(filterName, value, displayText) {
        const button = document.createElement('button');
        button.type = 'button';
        button.className = 'btn btn-sm btn-secondary rounded-pill';
        button.setAttribute('data-filter-name', filterName);
        button.setAttribute('data-filter-value', value);
        
        // Create close icon
        const closeIcon = document.createElement('i');
        closeIcon.className = 'fi-close fs-sm me-1 ms-n1';
        
        // Add content
        button.appendChild(closeIcon);
        button.appendChild(document.createTextNode(displayText));
        
        return button;
    }
    
    /**
     * Get display text for a filter
     * @param {string} filterName - The name of the filter
     * @param {string} value - The filter value
     * @returns {string} Display text
     */
    function getFilterDisplayText(filterName, value) {
        // Try to get the display text from the associated form element
        const element = document.querySelector(`[name="${filterName}"], #${filterName}`);
        
        if (element) {
            if (element.type === 'checkbox') {
                // For checkboxes, get the label text
                const label = document.querySelector(`label[for="${element.id}"]`);
                if (label) {
                    return label.textContent.trim();
                }
            } else if (element.tagName === 'SELECT') {
                // For selects, get the option text
                const option = element.querySelector(`option[value="${value}"]`);
                if (option) {
                    return option.textContent.trim();
                }
            }
        }
        
        // Fallback: use the value itself, formatted nicely
        return formatFilterValue(filterName, value);
    }
    
    /**
     * Format filter value for display
     * @param {string} filterName - The name of the filter
     * @param {string} value - The filter value
     * @returns {string} Formatted value
     */
    function formatFilterValue(filterName, value) {
        // Handle special cases
        if (value === 'true') {
            return filterName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        }
        
        // Convert camelCase or snake_case to readable format
        let formatted = value.replace(/([A-Z])/g, ' $1')
                            .replace(/_/g, ' ')
                            .replace(/-/g, ' ')
                            .toLowerCase();
        
        // Capitalize first letter of each word
        formatted = formatted.replace(/\b\w/g, l => l.toUpperCase());
        
        return formatted;
    }
    
    /**
     * Handle filter removal from visual display
     * @param {Event} event - The click event
     */
    function handleFilterRemove(event) {
        const button = event.target.closest('button[data-filter-name]');
        if (!button) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        const filterName = button.getAttribute('data-filter-name');
        const filterValue = button.getAttribute('data-filter-value');
        
        removeFilterValue(filterName, filterValue);
    }
    
    /**
     * Remove a specific filter value
     * @param {string} filterName - The name of the filter
     * @param {string} valueToRemove - The specific value to remove
     */
    function removeFilterValue(filterName, valueToRemove) {
        const currentValue = filtersMap.get(filterName);
        
        if (!currentValue) return;
        
        // Handle multi-value filters
        if (currentValue.includes('|')) {
            const values = currentValue.split('|');
            const newValues = values.filter(v => v !== valueToRemove);
            
            if (newValues.length > 0) {
                filtersMap.set(filterName, newValues.join('|'));
            } else {
                filtersMap.delete(filterName);
            }
        } else {
            // Single value filter
            filtersMap.delete(filterName);
        }
        
        // Update the corresponding form element
        updateFormElement(filterName, filtersMap.get(filterName) || '');
        
        // Update visual display
        updateSelectedFiltersDisplay();
        
        // Trigger change event
        triggerFilterChangeEvent(filterName, filtersMap.get(filterName) || '');
        
        console.log(`Filter removed: ${filterName} = ${valueToRemove}`);
        logCurrentFilters();
    }
    
    /**
     * Update form element to match filter state
     * @param {string} filterName - The name of the filter
     * @param {string} value - The new value
     */
    function updateFormElement(filterName, value) {
        const element = document.querySelector(`[name="${filterName}"], #${filterName}`);
        if (!element) return;
        
        if (element.type === 'checkbox') {
            element.checked = value === 'true';
        } else if (element.type === 'radio') {
            // Uncheck all radios in the group
            const radios = document.querySelectorAll(`input[type="radio"][name="${filterName}"]`);
            radios.forEach(radio => radio.checked = false);
            
            // Check the specific radio if value exists
            if (value) {
                const radioToCheck = document.querySelector(`input[type="radio"][name="${filterName}"][value="${value}"]`);
                if (radioToCheck) radioToCheck.checked = true;
            }
        } else if (element.tagName === 'SELECT') {
            // Check if this select has a Choices instance
            const choicesInstance = choicesMap.get(filterName);
            
            if (choicesInstance) {
                // Use Choices API to update the value
                if (element.multiple) {
                    // Handle multi-select with Choices
                    const values = value ? value.split('|') : [];
                    choicesInstance.removeActiveItems();
                    values.forEach(val => {
                        if (val) {
                            choicesInstance.setChoiceByValue(val);
                        }
                    });
                } else {
                    // Handle single select with Choices
                    choicesInstance.setChoiceByValue(value);
                }
            } else {
                // Fallback to native select handling
                if (element.multiple) {
                    // Handle multi-select
                    const values = value ? value.split('|') : [];
                    Array.from(element.options).forEach(option => {
                        option.selected = values.includes(option.value);
                    });
                } else {
                    element.value = value;
                }
            }
        } else {
            element.value = value;
        }
    }

    /**
     * Get all current filters
     * @returns {Map} The current filters map
     */
    function getCurrentFilters() {
        return new Map(filtersMap);
    }
    
    /**
     * Get filters as a plain object
     * @returns {Object} Filters as key-value pairs
     */
    function getFiltersAsObject() {
        return Object.fromEntries(filtersMap);
    }
    
    /**
     * Get filters as URL search parameters
     * @returns {URLSearchParams} Filters as URL search parameters
     */
    function getFiltersAsURLParams() {
        const params = new URLSearchParams();
        filtersMap.forEach((value, key) => {
            params.set(key, value);
        });
        return params;
    }
    
    /**
     * Clear all filters
     */
    function clearAllFilters() {
        // Clear the map
        filtersMap.clear();
        
        // Reset all form elements
        const filterSidebar = document.getElementById('filterSidebar');
        if (filterSidebar) {
            // Reset checkboxes
            const checkboxes = filterSidebar.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            
            // Reset selects (using Choices instances where available)
            const selects = filterSidebar.querySelectorAll('select');
            selects.forEach(select => {
                const selectId = select.id || select.name;
                const choicesInstance = choicesMap.get(selectId);
                
                if (choicesInstance) {
                    // Use Choices API to clear selections
                    choicesInstance.removeActiveItems();
                } else {
                    // Fallback to native select handling
                    select.selectedIndex = 0;
                    // For multi-selects, clear all selections
                    if (select.multiple) {
                        Array.from(select.options).forEach(option => {
                            option.selected = false;
                        });
                    }
                }
            });
            
            // Reset radio buttons
            const radios = filterSidebar.querySelectorAll('input[type="radio"]');
            radios.forEach(radio => {
                radio.checked = false;
            });
            
            // Reset text inputs
            const textInputs = filterSidebar.querySelectorAll('input[type="text"], input[type="search"]');
            textInputs.forEach(input => {
                input.value = '';
            });
        }
        
        // Update visual display
        updateSelectedFiltersDisplay();
        
        console.log('All filters cleared');
        triggerFilterChangeEvent('all', 'cleared');
    }
    
    /**
     * Set filters from an object
     * @param {Object} filters - Object with filter key-value pairs
     */
    function setFilters(filters) {
        Object.entries(filters).forEach(([key, value]) => {
            const element = document.querySelector(`[name="${key}"], #${key}`);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = value === "true";
                } else if (element.type === 'radio') {
                    const radioToCheck = document.querySelector(`input[type="radio"][name="${key}"][value="${value}"]`);
                    if (radioToCheck) radioToCheck.checked = true;
                } else if (element.tagName === 'SELECT') {
                    // Check if this select has a Choices instance
                    const choicesInstance = choicesMap.get(key);
                    
                    if (choicesInstance) {
                        // Use Choices API to set the value
                        if (element.multiple) {
                            // Handle multi-select with Choices
                            const values = value.split('|');
                            choicesInstance.removeActiveItems();
                            values.forEach(val => {
                                if (val) {
                                    choicesInstance.setChoiceByValue(val);
                                }
                            });
                        } else {
                            // Handle single select with Choices
                            choicesInstance.setChoiceByValue(value);
                        }
                    } else {
                        // Fallback to native select handling
                        if (element.multiple) {
                            const values = value.split('|');
                            Array.from(element.options).forEach(option => {
                                option.selected = values.includes(option.value);
                            });
                        } else {
                            element.value = value;
                        }
                    }
                } else {
                    element.value = value;
                }
                updateFilterValue(key, value);
            }
        });
        
        console.log('Filters set from object:', filters);
        logCurrentFilters();
    }
    
    /**
     * Trigger custom filter change event
     * @param {string} filterName - The name of the changed filter
     * @param {string} value - The new value
     */
    function triggerFilterChangeEvent(filterName, value) {
        const event = new CustomEvent('filterChanged', {
            detail: {
                filterName: filterName,
                value: value,
                allFilters: getFiltersAsObject()
            }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * Log current filters to console (for debugging)
     */
    function logCurrentFilters() {
        console.log('Current filters:', getFiltersAsObject());
        console.log('Filter count:', filtersMap.size);
    }
    
    /**
     * Debounce function to limit the rate of function calls
     * @param {Function} func - The function to debounce
     * @param {number} wait - The number of milliseconds to delay
     * @returns {Function} The debounced function
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Update results via AJAX call with current filters
     */
    function updateResultsWithAjax() {
        const resultsContainer = document.getElementById('results-data-container');
        if (!resultsContainer) {
            console.error('Results container not found');
            return;
        }

        // Get the RESULTS_DATA route URL
        const resultsDataUrl = appRoutes.get('RESULTS_DATA');
        if (!resultsDataUrl) {
            console.error('RESULTS_DATA route not found');
            return;
        }

        // Get current filters as an object
        const filtersObject = getFiltersAsObject();

        console.log('Making AJAX POST call to:', resultsDataUrl);
        console.log('Current filters:', filtersObject);

        // Block the UI during the AJAX call
        $.blockUI({
            message: '<div class="d-flex align-items-center"><div class="spinner-border spinner-border-sm me-2" role="status"></div>Caricamento risultati...</div>',
            css: {
                border: 'none',
                padding: '15px',
                backgroundColor: '#000',
                '-webkit-border-radius': '10px',
                '-moz-border-radius': '10px',
                opacity: 0.5,
                color: '#fff'
            }
        });

        // Prepare form data for POST request
        const formData = new FormData();

        // Add each filter as a form parameter
        Object.entries(filtersObject).forEach(([key, value]) => {
            formData.append(key, value);
        });

        // Make the AJAX call
        $.ajax({
            url: resultsDataUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                // Update the results container with the new HTML
                resultsContainer.innerHTML = response;
                $("#totalResults").html($("#totalCount").val());
                console.log('Results updated successfully');
            },
            error: function(xhr, status, error) {
                console.error('Error updating results:', error);
                console.error('Status:', status);
                console.error('Response:', xhr.responseText);

                // Show error message to user
                resultsContainer.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-danger" role="alert">
                            <h4 class="alert-heading">Errore nel caricamento</h4>
                            <p>Si è verificato un errore durante il caricamento dei risultati. Riprova più tardi.</p>
                        </div>
                    </div>
                `;
            },
            complete: function() {
                // Always unblock the UI when the request completes
                $.unblockUI();
            }
        });
    }

    // Initialize the debounced update function
    debouncedUpdateResults = debounce(updateResultsWithAjax, 500);

    // Bind clear filters functionality to the "Togli filtri" link
    $(document).on('click', '.nav-link[href="#!"]', function(e) {
        e.preventDefault();
        clearAllFilters();
    });
    
    // Listen for custom filter change events and trigger AJAX call
    document.addEventListener('filterChanged', function(event) {
        console.log('Filter change detected:', event.detail);

        // Use debounced version to avoid too many AJAX calls
        // For clearing all filters, use immediate call for better UX
        if (event.detail.filterName === 'all' && event.detail.value === 'cleared') {
            console.log('All filters cleared, triggering immediate AJAX call');
            updateResultsWithAjax();
        } else {
            // Use debounced version for individual filter changes
            debouncedUpdateResults();
        }
    });
    
    // Expose public methods for external use
    window.ResultsFilters = {
        getCurrentFilters: getCurrentFilters,
        getFiltersAsObject: getFiltersAsObject,
        getFiltersAsURLParams: getFiltersAsURLParams,
        clearAllFilters: clearAllFilters,
        setFilters: setFilters,
        logCurrentFilters: logCurrentFilters,
        updateResults: updateResultsWithAjax,
        updateResultsDebounced: debouncedUpdateResults,
        choicesMap: choicesMap
    };

    updateSelectedFiltersDisplay();

    // Load initial results on page load
    // Small delay to ensure all initialization is complete
    setTimeout(function() {
        updateResultsWithAjax();
    }, 100);
});