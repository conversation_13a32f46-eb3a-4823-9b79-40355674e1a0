$(function () {
    bindLogin();
});

function bindLogin() {
    // Inizializza il validator sul form usando la configurazione globale
    // La configurazione globale gestisce automaticamente tutto:
    // - Nessun messaggio di errore automatico
    // - Solo classi CSS (is-invalid/is-valid)
    // - Mostra/nasconde le tue label personalizzate
    $('#login-form').validate();
    
    // Form submission handling
    $('#login-form').submit(function (e) {
        e.preventDefault();
        if ($("#login-form").valid()) {
            const formData = new FormData(this);
            $.blockUI();
            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    // todo: handle success response
                    $.unblockUI();
                    window.location.href = response;
                },
                error: function (error) {
                    // Handle errors
                    $.unblockUI();
                    Swal.fire({
                        text: error.responseText,
                        icon: 'error',
                        timer: 3000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-center'
                    });
                    console.error('Error during account registration', error);
                }
            });
        }
    });
}