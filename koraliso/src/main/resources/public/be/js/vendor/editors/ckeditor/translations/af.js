(function(e){const n=e["af"]=e["af"]||{};n.dictionary=Object.assign(n.dictionary||{},{"%0 of %1":"%0 van %1","Advanced options":"","Align center":"<PERSON><PERSON> in die middel","Align left":"Belyn links","Align right":"Belyn regs","Block quote":"Verwysingsaanhaling",Bold:"Vet",Cancel:"Kanselleer",Clear:"",Code:"Bronkode",Find:"Soek","Find and replace":"Soek en vervang","Find in text…":"Soek in teks …","Insert code block":"Voeg bronkodeblok in",Italic:"Kursief",Justify:"Belyn beide kante","Match case":"Hooflettersensitief","Next result":"Volgende resultaat","Plain text":"Gewone skrif","Previous result":"Vorige resultaat","Remove color":"Verwyder kleur",Replace:"Vervang","Replace all":"Vervang alles","Replace with…":"Vervang met ...","Restore default":"Herstel verstek",Save:"Stoor","Saving changes":"Veranderinge word gestoor","Show more items":"Wys meer items",Strikethrough:"Deurstreep",Subscript:"Onderskrif",Superscript:"Boskrif","Text alignment":"Teksbelyning","Text alignment toolbar":"Teksbelyning nutsbank","Text to find must not be empty.":"Soekteks mag nie leeg wees nie.","Tip: Find some text first in order to replace it.":"Wenk: Soek eers 'n bietjie teks om dit te vervang.",Underline:"Onderstreep","Whole words only":"Slegs hele woorde"});n.getPluralForm=function(e){return e!=1}})(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));