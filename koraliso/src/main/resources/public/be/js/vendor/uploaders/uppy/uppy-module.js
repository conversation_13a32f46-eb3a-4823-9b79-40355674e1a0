    import {
        Uppy,
        Dashboard,
        Webcam,
        <PERSON>s,
        ImageEditor,
//        ScreenCapture,
//            RemoteSources,
//        Audio,
        Compressor
//        GoldenRetriever
    } 
    from "http://releases.transloadit.com/uppy/v3.15.0/uppy.min.mjs";

    import it_IT from "http://cdn.jsdelivr.net/npm/@uppy/locales@3.5.0/lib/it_IT.min.js";

    const uppy = new Uppy({ debug: true, autoProceed: false, locale: it_IT })
        .use(Dashboard, {
            inline: true,
            target: '.uppy'
        })
        .use(Webcam, {
          target: Dashboard,
          showVideoSourceDropdown: true,
          showRecordingLength: true
        })
//        .use(Audio, {
//          target: Dashboard,
//          showRecordingLength: true
//        })
//        .use(ScreenCapture, { target: Dashboard })
//            .use(RemoteSources, {
//              companionUrl: COMPANION_URL,
//              sources: [
//                "Box",
//                "Dropbox",
//                "Facebook",
//                "GoogleDrive",
//                "Instagram",
//                "OneDrive",
//                "Unsplash",
//                "Url"
//              ],
//              companionAllowedHosts
//            })
//        .use(GoldenRetriever)
        .use(Compressor)
        .use(ImageEditor, { target: Dashboard });