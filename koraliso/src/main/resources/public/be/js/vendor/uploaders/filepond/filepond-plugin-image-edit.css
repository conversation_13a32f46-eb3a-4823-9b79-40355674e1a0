/*!
 * FilePondPluginImageEdit 1.6.3
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */

/* eslint-disable */
.filepond--action-edit-item.filepond--action-edit-item {
  width: 2em;
  height: 2em;
  padding: 0.1875em;
}

.filepond--action-edit-item.filepond--action-edit-item[data-align*='center'] {
  margin-left: -0.1875em;
}

.filepond--action-edit-item.filepond--action-edit-item[data-align*='bottom'] {
  margin-bottom: -0.1875em;
}

.filepond--action-edit-item-alt {
  border: none;
  line-height: inherit;
  background: transparent;
  font-family: inherit;
  color: inherit;
  outline: none;
  padding: 0;
  margin: 0 0 0 0.25em;
  pointer-events: all;
  position: absolute;
}

.filepond--action-edit-item-alt svg {
  width: 1.3125em;
  height: 1.3125em;
}

.filepond--action-edit-item-alt span {
  font-size: 0;
  opacity: 0;
}
.filepond--root[data-style-panel-layout~='circle'] .filepond--action-edit-item {
  opacity: 1 !important;
  visibility: visible !important;
}
