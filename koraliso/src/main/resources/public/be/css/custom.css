/* Misc */
.img-flag {
    border-radius: 50%;
    height: 22px;
    width: 22px;
}
/* Radio Card */
label.radio-card {
    cursor: pointer;
}
label.radio-card .card-content-wrapper {
    background: #fff;
    border-radius: 5px;
    padding: 13px;
    display: flex;
    border: var(--border-width) solid var(--gray-400);
    transition: 200ms linear;
}
label.radio-card .check-icon {
    width: 20px;
    height: 20px;
    display: inline-block;
    border: solid 2px #e3e3e3;
    transition: 200ms linear;
    position: relative;
    border-radius: 0.1875em;
}
label.radio-card .check-icon:before {
    content: "";
    position: absolute;
    inset: 0;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center center;
    transform: scale(1.6);
    transition: 200ms linear;
    opacity: 0;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2.5' d='M5 10l4 4l6-8'/%3e%3c/svg%3e");
}
label.radio-card input[type=radio] {
    position: absolute;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}
label.radio-card input[type=radio]:checked + .card-content-wrapper {    
    border: var(--border-width) solid var(--component-active-bg);    
}
label.radio-card input[type=radio]:checked:focus + .card-content-wrapper {
    box-shadow: 0 0 0 0 transparent, var(--focus-ring-box-shadow);
    border: var(--border-width) solid var(--component-active-bg);    
}
label.radio-card input[type=radio]:checked + .card-content-wrapper .check-icon {
    background: rgba(var(--primary-rgb));
    border-color: rgba(var(--primary-rgb));
    transform: scale(1);
}
label.radio-card input[type=radio]:checked + .card-content-wrapper .check-icon:before {
    transform: scale(1);
    opacity: 1;
}
label.radio-card input[type=radio]:focus + .card-content-wrapper .check-icon {
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.25);
    border-color: rgba(var(--primary-rgb));
}
label.radio-card .card-content img {
    margin-bottom: 10px;
}
label.radio-card .card-content h4 {
    font-size: 16px;
    letter-spacing: -0.24px;
    margin-bottom: 10px;
}
label.radio-card .card-content h5 {
    font-size: 14px;
    line-height: 1.4;
    text-align: center;
    color: #686d73;
}
/* Block UI */
.blockui-loading {
    -webkit-animation: loading 1s linear infinite;
    animation: loading 1s linear infinite;
    background: transparent;
    border: 4px solid #888;
    border-bottom-color: #fff;
    border-radius: 50%;
    height: 50px;
    left: 50%;
    margin: -25px 0 0 -25px;
    opacity: .7;
    padding: 0;
    position: absolute;
    top: 50%;
    width: 50px;
    z-index: 99999
}
@-webkit-keyframes loading {
    to {
        transform: rotate(1turn)
    }
}

@keyframes loading {
    to {
        transform: rotate(1turn)
    }
}
/* CKEditor 5 Fix */
.ck.ck-button.ck-insert-table-dropdown-grid-box {
    --ck-spacing-tiny: 0;    
}
.ck-source-editing-area {
    overflow: hidden;
    position: relative;
    border: 1px solid var(--ck-color-base-border);
    border-radius: var(--ck-border-radius);
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.ck-focused+ .ck-source-editing-area {
    box-shadow: var(--ck-inner-shadow),0 0;
    border: var(--border-width) solid var(--component-active-bg);
    border-radius: var(--ck-border-radius);
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.ck-source-editing-area textarea {
    border-color: transparent!important;
}
.ck.ck-labeled-field-view>.ck.ck-labeled-field-view__input-wrapper>.ck.ck-label {
    background: #fff!important;
}
.ck.ck-powered-by {
    display: none!important;
}
/* DateRangePicker Fix */
.daterangepicker.auto-apply .drp-buttons, .daterangepicker.single .drp-selected  {
    display: none;
}
/* FilePond Fix */
.filepond--credits {
    display: none;
}
.filepond--root {
    margin-bottom: 0;
}
/*.filepond--root .filepond--drop-label {
    height: 100%;
}*/
/* Fix Maxlength */
.input-group:not(.has-validation)>:not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating).maxlength {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}
/* Fix DataTable buttons mobile */
@media (max-width: 575.98px) {
    .dt-buttons {
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }
}