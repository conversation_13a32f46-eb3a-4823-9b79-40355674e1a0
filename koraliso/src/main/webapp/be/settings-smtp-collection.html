{% extends "be/include/base.html" %}

{% set area = 'SETTINGS' %}
{% set page = 'SETTINGS_SMTP' %}

{% block extrahead %}
<title>Smtp</title>

<!-- Page script -->
{% include "be/include/snippets/plugins/datatable.html" %}
<script src="{{ contextPath }}/be/js/pages/settings-smtp-collection.js?{{ buildNumber }}"></script>        
<!-- /page script -->
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_SETTINGS_SMTP_DATA', '{{ routes("BE_SETTINGS_SMTP_DATA") }}');
</script>
<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
            <h5 class="py-sm-3 mb-sm-0">Lista di Smtp</h5>
            <div class="ms-sm-auto my-sm-auto">
                <a href="{{ routes('BE_SETTINGS_SMTP') }}" class="btn btn-primary w-100">
                    <i class="ph-plus me-2"></i>
                    NUOVO SMTP
                </a>
            </div>
        </div>

        <table class="table datatable">
            <thead>
                <tr>
                    <th>Hostname</th>
                    <th>Port</th>
                    <th>Authentication</th>
                    <th>Username</th>
                    <th>Encryption</th>
                    <th>startTls</th>
                    <th>Sender</th>
                    <th></th>
                </tr>
            </thead>
        </table>

    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}