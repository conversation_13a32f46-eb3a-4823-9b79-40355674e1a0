{% extends "be/include/base.html" %}

{% set area = 'SETTINGS' %}
{% set page = 'SETTINGS_USER' %}

{% block extrahead %}
<title>User</title>

<!-- Page script -->
{% include "be/include/snippets/plugins/datatable.html" %}
<script src="{{ contextPath }}/be/js/pages/settings-user-collection.js?{{ buildNumber }}"></script>        
<!-- /page script -->
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_SETTINGS_USER_DATA', '{{ routes("BE_SETTINGS_USER_DATA") }}');
</script>
<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
            <h5 class="py-sm-3 mb-sm-0"><PERSON><PERSON> <PERSON>ti</h5>
            {% if user.profileType equals "system" %}
            <div class="ms-sm-auto my-sm-auto">
                <a href="{{ routes('BE_SETTINGS_USER') }}" class="btn btn-primary w-100">
                    <i class="ph-plus me-2"></i>
                    NUOVO UTENTE
                </a>
            </div>
            {% endif %}
        </div>

        <table class="table datatable">
            <thead>
                <tr>
                    <th>Nome</th>
                    <th>Email</th>
                    <th>Profilo</th>
                    <th></th>
                </tr>
            </thead>
        </table>

    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}