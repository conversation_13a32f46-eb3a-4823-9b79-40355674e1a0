{% extends "be/include/base.html" %}

{% set area = 'MAIL' %}
{% set page = 'MAILTEMPLATE' %}

{% set title = 'Template mail' %}

{% block extrahead %}
<title>{{ title }}</title>

<!-- Page script -->
{% include "be/include/snippets/plugins/datatable.html" %}
<script src="{{ contextPath }}/be/js/pages/mailtemplate-collection.js?{{ buildNumber }}"></script>        
<!-- /page script -->
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_MAILTEMPLATE_DATA', '{{ routes("BE_MAILTEMPLATE_DATA") }}');
</script>
<div class="row">
    <div class="col-xl-12">

        <!-- Checkbox selection -->
        <div class="card">
            <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                <h5 class="py-sm-3 mb-sm-0">{{ title }}</h5>
                {% if  user.profileType equals "system" %}
                <div class="ms-sm-auto my-sm-auto">
                    <a href="{{ routes('BE_MAILTEMPLATE') }}" class="btn btn-primary w-100">
                        <i class="ph-plus me-2"></i>
                        NUOVO TEMPLATE
                    </a>
                </div>
                {% endif %}
            </div>

            <table class="table datatable">
                <thead>
                    <tr>
                        <th>Titolo</th>
                        <th>Lingue disponibili</th>
                        <th>Ultima modifica</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
            </table>
        </div>
        <!-- /checkbox selection -->
    </div>
</div>

{% endblock %}

{% block sidebar %}
    {% include "be/include/snippets/sidebar/sidebar-mailtemplate-collection.html" %}
{% endblock %}