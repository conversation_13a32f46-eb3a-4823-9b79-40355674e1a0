<!-- Right sidebar -->
<div class="sidebar sidebar-end sidebar-expand-lg align-self-start">

    <!-- Sidebar content -->
    <div class="sidebar-content">

        <!-- Header -->
        <div class="sidebar-section sidebar-section-body d-flex align-items-center d-lg-none pb-2">
            <h5 class="mb-0">Sidebar</h5>
            <div class="ms-auto">
                <button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-mobile-end-toggle">
                    <i class="ph-x"></i>
                </button>
            </div>
        </div>
        <!-- /header -->      

        <!-- Sub navigation -->
        <div class="sidebar-section">
            <div class="sidebar-section-header border-bottom">
                <span class="fw-semibold">Lingue</span>
                <div class="ms-auto">
                    <a href="#sidebar-navigation" class="text-reset" data-bs-toggle="collapse">
                        <i class="ph-caret-down collapsible-indicator"></i>
                    </a>
                </div>
            </div>

            <div class="collapse show" id="sidebar-navigation">                
                <ul class="nav nav-sidebar" data-nav-type="accordion">                    
                    {% for language in availableLanguages %}
                        <li class="nav-item nav-item-submenu">
                            {% set exists = false %}
                            {% if category is not empty %}
                                {% if category.availableLanguages is not empty %}
                                    {% for availableLanguage in category.availableLanguages %}
                                        {% if availableLanguage == language %}
                                            {% set exists = true %}
                                        {% endif %}
                                    {% endfor %}
                                {% endif %}
                            {% endif %}
                            <a href="#" class="nav-link">
                                <img class="img-flag me-2" src="{{ contextPath }}/be/images/lang/{{ language }}.svg" height="22" alt="">
                                {{ language }}
                                {% if exists == true %}
                                    <span class="badge bg-success ms-auto">Sì</span>
                                {% else %}
                                    <span class="badge bg-warning ms-auto">No</span>
                                {% endif %}
                            </a>
                            {# todo #}
                            <div class="nav-group-sub collapse" style="">                            
                                <div class="sidebar-section-body">
                                    <div class="d-sm-flex flex-sm-wrap mb-3">
                                        {% if exists == true %}
                                            <a onclick="changeCategoryLanguage('{{ category.parentId }}', '{{ language}}')" class="btn btn-flat-primary border-transparent w-100">
                                                <i class="ph-link me-2"></i>
                                                Apri
                                            </a>
                                        {% else %}
                                            <a onclick="createCategoryLanguage('{{ category.parentId }}', '{{ category.language}}', '{{ language}}')" class="btn btn-flat-primary border-transparent w-100">
                                                <i class="ph-link me-2"></i>
                                                Crea in lingua
                                            </a>
                                        {% endif %}
                                    </div>
<!--                                    <div class="d-sm-flex flex-sm-wrap mb-3">
                                        <div class="fw-semibold">Stato:</div>
                                        <div class="ms-sm-auto mt-1 mt-sm-0">
                                            <i class="ph-eye me-2"></i>
                                            Visibile
                                        </div>
                                    </div>  
                                    <div class="d-sm-flex flex-sm-wrap mb-3">
                                        <div class="fw-semibold">Data:</div>
                                        <div class="ms-sm-auto mt-1 mt-sm-0">
                                            <i class="ph-ph-calendar me-2"></i>
                                            {{ photo.publication | date('dd/MM/yyyy') }}
                                        </div>
                                    </div>-->
                                </div>
                            </div>
                        </li>                    
                    {% endfor %}
                </ul>
            </div>
        </div>
        <!-- /sub navigation -->

    </div>
    <!-- /sidebar content -->

</div>
<!-- /right sidebar -->