{% extends "be/include/base.html" %}

{% set area = 'SETTINGS' %}
{% set page = 'SETTINGS_USER' %}
{% set title = curUser is empty ? 'Nuovo utente' : 'Modifica utente' %}

{% block extrahead %}

<title>{{ title }} </title>

<!-- Specific script -->
{% include "be/include/snippets/plugins/filepond.html" %}
{% include "be/include/snippets/plugins/select2.html" %}
{% include "be/include/snippets/plugins/daterangepicker.html" %}
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
{% include "be/include/snippets/plugins/sweetalert.html" %}
<!-- specific script-->

<!-- Page script -->
<script src="{{ contextPath }}/be/js/pages/settings-user.js?{{ buildNumber }}"></script>
<!-- /page script -->

{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
    addRoute('BE_SETTINGS_USER', '{{ routes("BE_SETTINGS_USER") }}');
    addVariables('imageId', '{{ curUser.imageId }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header">
            {% if curUser is empty %}
            <h5 class="mb-0">Inserisci Utente</h5>
            {% else %}
            <h5 class="mb-0">Modifica Utente</h5>
            {% endif %}
        </div>

        <div class="card-body">
            {% set postUrl = routes('BE_SETTINGS_USER_SAVE') %}
            {% if curUser.id is not empty %}                
            {% set postUrl = routes('BE_SETTINGS_USER_SAVE') + '?userId=' + curUser.id %}
            {% endif %}

            <form id="user-edit" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Immagine profilo:</label>
                    <div class="col-lg-9">      
                        <div class="row">
                            <div class="col-lg-9">
                                <input id="logo" name="logo" type="file" class="filepond">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-9">
                                <div class="form-text text-muted">Carica la tua immagine di profilo. Il logo si vedrà in fase nell'area riservata in alto a destra (non nel sito).</div>                                
                            </div>
                        </div>
                    </div>

                </div>    
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Email: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="email" type="text" class="form-control" placeholder="Email" value="{{ curUser.email }}" required {{ disabled }}>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Name: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="name" type="text" class="form-control" placeholder="Name" value="{{ curUser.name }}" required {{ disabled }}>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Username:</label>
                    <div class="col-lg-9">
                        <input name="username" type="text" class="form-control" value="{{ curUser.username }}" {{ disabled }}>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Password: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="password" type="password" class="form-control" value="{{ curUser.password }}" required {{ disabled }}>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Tipologia Profilo: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <select name="profileType" class="form-control" data-minimum-results-for-search="Infinity">
                            <option value="system" {{ (curUser is not empty and curUser.profileType is not empty and curUser.profileType equals 'system') ? 'selected' : '' }}>System</option>
                            <option value="admin" {{ (curUser is not empty and curUser.profileType is not empty and curUser.profileType equals 'admin') ? 'selected' : '' }}>Admin</option>
                            <option value="operator" {{ (curUser is not empty and curUser.profileType is not empty and curUser.profileType equals 'operator') ? 'selected' : '' }}>Operatore</option>
                        </select>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Attivo:</label>
                    <div class="col-lg-9">
                        <input name="active" type="checkbox" class="form-control form-check-input" {{ curUser.active ? 'checked' : '' }} {{ disabled }}>
                    </div>
                </div>

                <div class="text-end">
                    {% if curUser is not empty %}
                    <button type="submit" class="btn btn-primary">Modifica Utente <i class="ph-paper-plane-tilt ms-2"></i></button>
                    {% else %}
                    <button type="submit" class="btn btn-primary">Inserisci Utente <i class="ph-paper-plane-tilt ms-2"></i></button>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}