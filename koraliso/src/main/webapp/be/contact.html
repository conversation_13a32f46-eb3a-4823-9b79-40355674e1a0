{% extends "be/include/base.html" %}

{% block extrahead %}
<title>Contatto</title>
<script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/tables/datatables/datatables.min.js"></script>
<script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/tables/datatables/extensions/select.min.js"></script>
<script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/tables/datatables/extensions/buttons.min.js"></script>    
<script src="https://anubi.us/static/themes/b/1/4.0/assets/demo/pages/datatables_extension_select.js"></script>
<script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/uploaders/fileinput/fileinput.min.js"></script>
<script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/uploaders/fileinput/plugins/sortable.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/contact.js?{{ buildNumber }}"></script>

{% endblock %}

{% block content %}

<a id="contactUri" style="display: none;" href="{{ routes('BE_CONTACT') }}" rel="nofollow"></a>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-body">
            <div class="row mb-3">
                <label class="col-lg-3 col-form-label">Nome: </label>
                <div class="col-lg-9">
                    <output class="col-form-label"> {{ contact.name }}</output>
                </div>
            </div>

            <div class="row mb-3">
                <label class="col-lg-3 col-form-label">Cognome: </label>
                <div class="col-lg-9">
                    <output class="col-form-label"> {{ contact.lastname }}</output>
                </div>
            </div>

            <div class="row mb-3">
                <label class="col-lg-3 col-form-label">Rag. sociale: </label>
                <div class="col-lg-9">
                    <output class="col-form-label"> {{ contact.fullname }}</output>
                </div>
            </div>
            <div class="row mb-3">
                <label class="col-lg-3 col-form-label">Numero di Telefono: </label>
                <div class="col-lg-9">
                    <output class="col-form-label"> {{ contact.phone }}</output>
                </div>
            </div>

            <div class="row mb-3">
                <label class="col-lg-3 col-form-label">Email: </label>
                <div class="col-lg-9">
                    <output class="col-form-label"> {{ contact.email }}</output>
                </div>
            </div>

            <div class="row mb-3">
                <label class="col-lg-3 col-form-label">Messaggio: </label>
                <div class="col-lg-9">
                    <output class="col-form-label"> {{ contact.message }}</output>
                </div>
            </div>

            <div class="row mb-3">
                <label class="col-lg-3 col-form-label">Oggetto: </label>
                <div class="col-lg-9">
                    <output class="col-form-label"> {{ contact.subject }}</output>
                </div>
            </div>

            <div class="row mb-3">
                <label class="col-lg-3 col-form-label">Template: </label>
                <div class="col-lg-9">
                    <output class="col-form-label"> {{ contact.template }}</output>
                </div>
            </div>
            
            <div class="row mb-3">
                <label class="col-lg-3 col-form-label">Mail: </label>
                {% autoescape false%}
                    {{ contact.mail }}
                {% endautoescape %}
            </div>
        </div>
    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}