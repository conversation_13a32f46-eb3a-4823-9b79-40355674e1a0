{% extends "be/include/base.html" %}

{% block extrahead %}
<title>Slider</title>
<script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/tables/datatables/datatables.min.js"></script>
<script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/tables/datatables/extensions/select.min.js"></script>
<script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/tables/datatables/extensions/buttons.min.js"></script>    
<script src="https://anubi.us/static/themes/b/1/4.0/assets/demo/pages/datatables_extension_select.js"></script>
<script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/uploaders/fileinput/fileinput.min.js"></script>
<script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/uploaders/fileinput/plugins/sortable.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/slider-edit.js?{{ buildNumber }}"></script>

{% endblock %}

{% block content %}

<a id="addSliderUri" style="display: none;" href="{{ routes('BE_SLIDER_EDIT_SAVE') }}" rel="nofollow"></a>
<a id="sliderUri" style="display: none;" href="{{ routes('BE_SLIDER') }}" rel="nofollow"></a>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header">
            {% if slider is empty %}
            <h5 class="mb-0">Inserisci Slider</h5>
            {% else %}
            <h5 class="mb-0">Modifica Slider</h5>
            {% endif %}
        </div>

        <div class="card-body">
            {% set postUrl = routes('BE_SLIDER_EDIT_SAVE') %}
            {% if slider.id is not empty %}                
            {% set postUrl = routes('BE_SLIDER_EDIT_SAVE') + '?sliderId=' + slider.id %}
            {% endif %}

            <form id="slider-edit" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Title: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="title" type="text" class="form-control" placeholder="Title" value="{{ slider.title }}" required {{ disabled }}>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Sub Title:</label>
                    <div class="col-lg-9">
                        <input name="subtitle" type="text" class="form-control" placeholder="Sub Title" value="{{ slider.subtitle }}" {{ disabled }}>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Link Text: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="linkText" type="text" class="form-control" placeholder="Link Text" value="{{ slider.linkText }}" required {{ disabled }}>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Link: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="link" type="text" class="form-control" placeholder="Link" value="{{ slider.link }}" required {{ disabled }}>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Language: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="language" type="text" class="form-control" placeholder="Language" value="{{ slider.language }}" required {{ disabled }}>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Page: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="page" type="text" class="form-control" placeholder="Page" value="{{ slider.page }}" required {{ disabled }}>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Image:</label>
                    <div class="col-lg-9">
                        <input name="image" type="file" class="file-input" data-show-remove="true" {{ disabled }}>
                    </div>
                </div>

                <div class="text-end">
                    {% if slider is not empty %}
                    <button type="submit" class="btn btn-primary">Modifica Slider <i class="ph-paper-plane-tilt ms-2"></i></button>
                    {% else %}
                    <button type="submit" class="btn btn-primary">Inserisci Slider <i class="ph-paper-plane-tilt ms-2"></i></button>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}