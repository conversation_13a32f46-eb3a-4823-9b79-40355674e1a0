
<h1 class="h2 pb-2 pb-lg-3"><PERSON><PERSON><PERSON> offerti</h1>

<div class="divingContainer">
    <div class="d-flex flex-column flex-sm-row flex-md-column flex-lg-row align-items-center justify-content-between gap-3 bg-body-tertiary rounded border border-dark p-3 mb-3 mb-sm-4">
        <div class="d-flex align-items-center gap-2">
            <img src="{{ contextPath }}/fe/img/account/diving.svg" alt="Image" width="32">
            <div class="d-flex align-items-center gap-1 ps-1">
                <div class="h6 mb-0">Immersioni</div>            
            </div>
        </div>
    </div>
    <!--
    #################################
    IMMERSIONI - CAMPI COMUNI
    #################################
    -->
    <h2 class="h6">Generali</h2>
    <div class="row gap-2 pb-3 pb-sm-4">
        <div class="col-12">
            <label for="divingCertifications" class="form-label">Certificazioni accettate</label>
            <!-- Multiple select example -->
            <select class="form-select" id="divingCertifications" name="divingCertifications" data-select multiple aria-label="Certificazioni accettate" required>
                <option value="">Seleziona certificazioni</option>

                <optgroup label="Immersioni Ricreative">
                    <option value="padi"{% if business.divingCertifications is not empty and business.divingCertifications contains 'padi' %} selected{% endif %}>PADI</option>
                    <option value="ssi"{% if business.divingCertifications is not empty and business.divingCertifications contains 'ssi' %} selected{% endif %}>SSI</option>
                    <option value="cmas"{% if business.divingCertifications is not empty and business.divingCertifications contains 'cmas' %} selected{% endif %}>CMAS</option>
                    <option value="fipsas"{% if business.divingCertifications is not empty and business.divingCertifications contains 'fipsas' %} selected{% endif %}>FIPSAS</option>
                    <option value="anis"{% if business.divingCertifications is not empty and business.divingCertifications contains 'anis' %} selected{% endif %}>ANIS</option>
                    <option value="naui"{% if business.divingCertifications is not empty and business.divingCertifications contains 'naui' %} selected{% endif %}>NAUI</option>
                    <option value="bsac"{% if business.divingCertifications is not empty and business.divingCertifications contains 'bsac' %} selected{% endif %}>BSAC</option>
                    <option value="fias"{% if business.divingCertifications is not empty and business.divingCertifications contains 'fias' %} selected{% endif %}>FIAS</option>
                </optgroup>

                <optgroup label="Immersioni Tecniche">
                    <option value="tdi"{% if business.divingCertifications is not empty and business.divingCertifications contains 'tdi' %} selected{% endif %}>TDI</option>
                    <option value="gue"{% if business.divingCertifications is not empty and business.divingCertifications contains 'gue' %} selected{% endif %}>GUE</option>
                    <option value="iantd"{% if business.divingCertifications is not empty and business.divingCertifications contains 'iantd' %} selected{% endif %}>IANTD</option>
                    <option value="andi"{% if business.divingCertifications is not empty and business.divingCertifications contains 'andi' %} selected{% endif %}>ANDI</option>
                    <option value="padi-tec"{% if business.divingCertifications is not empty and business.divingCertifications contains 'padi-tec' %} selected{% endif %}>PADI Tec</option>
                    <option value="ssi-tec"{% if business.divingCertifications is not empty and business.divingCertifications contains 'ssi-tec' %} selected{% endif %}>SSI Extended Range</option>
                    <option value="raid-tec"{% if business.divingCertifications is not empty and business.divingCertifications contains 'raid-tec' %} selected{% endif %}>RAID Technical</option>
                    <option value="psai"{% if business.divingCertifications is not empty and business.divingCertifications contains 'psai' %} selected{% endif %}>PSAI</option>
                </optgroup>

                <optgroup label="Altro">
                    <option value="other"{% if business.divingCertifications is not empty and business.divingCertifications contains 'other' %} selected{% endif %}>Altre certificazioni</option>
                </optgroup>
            </select>
            <div class="invalid-tooltip bg-transparent p-0">Seleziona una certificazione</div>
        </div>               
    </div>
    <div class="row gap-2 pb-3 pb-sm-4">    
        <div class="col-12">
            <label class="form-label pb-1 mb-2">Tipologie di immersioni</label>

            <div class="nav nav-pills flex-wrap gap-3">
                <div>
                    <input type="checkbox" class="btn-check" id="divingReef" name="divingReef"{% if business.divingReef %} checked{% endif %}>
                    <label class="nav-link" for="divingReef">
                        <img src="{{ contextPath }}/fe/img/icons/reef.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Reef
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingWrecks" name="divingWrecks"{% if business.divingWrecks %} checked{% endif %}>
                    <label class="nav-link" for="divingWrecks">
                        <img src="{{ contextPath }}/fe/img/icons/wrecks.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Relitti
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingNightDives" name="divingNightDives"{% if business.divingNightDives %} checked{% endif %}>
                    <label class="nav-link" for="divingNightDives">
                        <img src="{{ contextPath }}/fe/img/icons/night-dives.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Notturne
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingSeaBaptism" name="divingSeaBaptism"{% if business.divingSeaBaptism %} checked{% endif %}>
                    <label class="nav-link" for="divingSeaBaptism">
                        <img src="{{ contextPath }}/fe/img/icons/sea-baptism.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Battesimi del mare
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingOpenWater" name="divingOpenWater"{% if business.divingOpenWater %} checked{% endif %}>
                    <label class="nav-link" for="divingOpenWater">
                        <img src="{{ contextPath }}/fe/img/icons/open-water.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Acque libere
                    </label>
                </div>                
                <div>
                    <input type="checkbox" class="btn-check" id="divingShoreDeparture" name="divingShoreDeparture"{% if business.divingShoreDeparture %} checked{% endif %}>
                    <label class="nav-link" for="divingShoreDeparture">
                        <img src="{{ contextPath }}/fe/img/icons/shore-departure.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Partenza da riva
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingUnderIce" name="divingUnderIce"{% if business.divingUnderIce %} checked{% endif %}>
                    <label class="nav-link" for="divingUnderIce">
                        <img src="{{ contextPath }}/fe/img/icons/ice.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Acque fredde \ Ghiacci
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingCurrents" name="divingCurrents"{% if business.divingCurrents %} checked{% endif %}>
                    <label class="nav-link" for="divingCurrents">
                        <img src="{{ contextPath }}/fe/img/icons/currents.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        In corrente
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingAltitude" name="divingAltitude"{% if business.divingAltitude %} checked{% endif %}>
                    <label class="nav-link" for="divingAltitude">
                        <img src="{{ contextPath }}/fe/img/icons/altitude.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        In altitudine
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingSidemount" name="divingSidemount"{% if business.divingSidemount %} checked{% endif %}>
                    <label class="nav-link" for="divingSidemount">
                        <img src="{{ contextPath }}/fe/img/icons/sidemount.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        In sidemount
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingScooter" name="divingScooter"{% if business.divingScooter %} checked{% endif %}>
                    <label class="nav-link" for="divingScooter">
                        <img src="{{ contextPath }}/fe/img/icons/scooter.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Con scooter Subacqueo
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingSharkCage" name="divingSharkCage"{% if business.divingSharkCage %} checked{% endif %}>
                    <label class="nav-link" for="divingSharkCage">
                        <img src="{{ contextPath }}/fe/img/icons/shark.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        In gabbia (Squalo)
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingCenotes" name="divingCenotes"{% if business.divingCenotes %} checked{% endif %}>
                    <label class="nav-link" for="divingCenotes">
                        <img src="{{ contextPath }}/fe/img/icons/cenotes.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Nei cenotes
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingBigAnimals" name="divingBigAnimals"{% if business.divingBigAnimals %} checked{% endif %}>
                    <label class="nav-link" for="divingBigAnimals">
                        <img src="{{ contextPath }}/fe/img/icons/big-animals.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Con grandi animali
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingSardineRun" name="divingSardineRun"{% if business.divingSardineRun %} checked{% endif %}>
                    <label class="nav-link" for="divingSardineRun">
                        <img src="{{ contextPath }}/fe/img/icons/sardine-run.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Sardine run
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingPoolY40" name="divingPoolY40"{% if business.divingPoolY40 %} checked{% endif %}>
                    <label class="nav-link" for="divingPoolY40">
                        <img src="{{ contextPath }}/fe/img/icons/pool-y40.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        In piscina (Y40)
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingFreshWater" name="divingFreshWater"{% if business.divingFreshWater %} checked{% endif %}>
                    <label class="nav-link" for="divingFreshWater">
                        <img src="{{ contextPath }}/fe/img/icons/fresh-water.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        In acqua dolce
                    </label>
                </div>                
                <div>
                    <input type="checkbox" class="btn-check" id="divingFocusBio" name="divingFocusBio"{% if business.divingFocusBio %} checked{% endif %}>
                    <label class="nav-link" for="divingFocusBio">
                        <img src="{{ contextPath }}/fe/img/icons/focus-bio.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Focus biologia
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingFocusPhoto" name="divingFocusPhoto"{% if business.divingFocusPhoto %} checked{% endif %}>
                    <label class="nav-link" for="divingFocusPhoto">
                        <img src="{{ contextPath }}/fe/img/icons/underwater-photography.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Focus Fotografia
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingFocusVideo" name="divingFocusVideo"{% if business.divingFocusVideo %} checked{% endif %}>
                    <label class="nav-link" for="divingFocusVideo">
                        <img src="{{ contextPath }}/fe/img/icons/underwater-video.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Focus Video
                    </label>
                </div>                                            
                <div>
                    <input type="checkbox" class="btn-check" id="divingDeepDives" name="divingDeepDives"{% if business.divingDeepDives %} checked{% endif %}>
                    <label class="nav-link" for="divingDeepDives">
                        <img src="{{ contextPath }}/fe/img/icons/deep-dives.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Immersioni profonde
                    </label>
                </div>                                  
                <div>
                    <input type="checkbox" class="btn-check" id="divingRebreather" name="divingRebreather"{% if business.divingRebreather %} checked{% endif %}>
                    <label class="nav-link" for="divingRebreather">
                        <img src="{{ contextPath }}/fe/img/icons/rebreather.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Rebreather diving
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingDeco" name="divingDeco"{% if business.divingDeco %} checked{% endif %}>
                    <label class="nav-link" for="divingDeco">
                        <img src="{{ contextPath }}/fe/img/icons/deco.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Deco diving
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingExtreme" name="divingExtreme"{% if business.divingExtreme %} checked{% endif %}>
                    <label class="nav-link" for="divingExtreme">
                        <img src="{{ contextPath }}/fe/img/icons/extreme.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Immersioni estreme
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingWildlifeTech" name="divingWildlifeTech"{% if business.divingWildlifeTech %} checked{% endif %}>
                    <label class="nav-link" for="divingWildlifeTech">
                        <img src="{{ contextPath }}/fe/img/icons/wildlife.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Wildlife
                    </label>
                </div>            
            </div>
        </div>
    </div>
    <div class="row gap-2 pb-3 pb-sm-4">    
        <div class="col-12">
            <label class="form-label pb-1 mb-2">Servizi Foto \ Video</label>
            <div class="nav nav-pills flex-wrap gap-3">
                <div>
                    <input type="checkbox" class="btn-check" id="divingUnderwaterPhotography" name="divingUnderwaterPhotography"{% if business.divingUnderwaterPhotography %} checked{% endif %}>
                    <label class="nav-link" for="divingUnderwaterPhotography">
                        <img src="{{ contextPath }}/fe/img/icons/underwater-photography.svg" alt="Fotografia subacquea" class="ms-n1 me-2 w-25px">
                        Fotografia subacquea
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingUnderwaterVideo" name="divingUnderwaterVideo"{% if business.divingUnderwaterVideo %} checked{% endif %}>
                    <label class="nav-link" for="divingUnderwaterVideo">
                        <img src="{{ contextPath }}/fe/img/icons/underwater-video.svg" alt="Video subacqueo" class="ms-n1 me-2 w-25px">
                        Video subacqueo
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingEditingCourse" name="divingEditingCourse"{% if business.divingEditingCourse %} checked{% endif %}>
                    <label class="nav-link" for="divingEditingCourse">
                        <img src="{{ contextPath }}/fe/img/icons/editing-course.svg" alt="Corso editing e post produzione" class="ms-n1 me-2 w-25px">
                        Corso editing e post produzione
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingGoProUsage" name="divingGoProUsage"{% if business.divingGoProUsage %} checked{% endif %}>
                    <label class="nav-link" for="divingGoProUsage">
                        <img src="{{ contextPath }}/fe/img/icons/gopro.svg" alt="Utilizzo GoPro o simili" class="ms-n1 me-2 w-25px">
                        Utilizzo GoPro o simili
                    </label>
                </div>
            </div>
        </div>
    </div>
    <div class="row gap-2 pb-3 pb-sm-4">    
        <div class="col-12">
            <label for="divingVacationTypes" class="form-label">Tipologie di Vacanze/Uscite</label>
            <!-- Multiple select example -->
            <select class="form-select" id="divingVacationTypes" name="divingVacationTypes" data-select multiple aria-label="Tipologie di vacanze/uscite">
                <option value="">Seleziona tipologie</option>            
                <option value="blue-week"{% if business.divingVacationTypes is not empty and business.divingVacationTypes contains 'blue-week' %} selected{% endif %}>Settimana Blu</option>
                <option value="cruise"{% if business.divingVacationTypes is not empty and business.divingVacationTypes contains 'cruise' %} selected{% endif %}>Crociera</option>                        
                <option value="half-day"{% if business.divingVacationTypes is not empty and business.divingVacationTypes contains 'half-day' %} selected{% endif %}>Uscita half day</option>
                <option value="full-day"{% if business.divingVacationTypes is not empty and business.divingVacationTypes contains 'full-day' %} selected{% endif %}>Uscita full day</option>
                <option value="weekend"{% if business.divingVacationTypes is not empty and business.divingVacationTypes contains 'weekend' %} selected{% endif %}>Uscita weekend</option>
                <option value="tech-stage"{% if business.divingVacationTypes is not empty and business.divingVacationTypes contains 'tech-stage' %} selected{% endif %}>Stage Tecnico</option>
                <option value="ccr-expedition"{% if business.divingVacationTypes is not empty and business.divingVacationTypes contains 'ccr-expedition' %} selected{% endif %}>CCR Expedition</option>
                <option value="multi-location"{% if business.divingVacationTypes is not empty and business.divingVacationTypes contains 'multi-location' %} selected{% endif %}>Multi-Location Dive Trip</option>                
                <option value="other"{% if business.divingVacationTypes is not empty and business.divingVacationTypes contains 'other' %} selected{% endif %}>Altro</option>
            </select>
        </div>
    </div>
    <!--
    #################################
    IMMERSIONI - RICREATIVE
    #################################
    -->
    <hr>
    <h2 class="h6">Immersioni ricreative</h2>

    <!-- CORSI -->
    <div class="row gap-2 pb-3 pb-sm-4">
        <div class="col-12">
            <label for="divingRecCourses" class="form-label">Tipologia corsi</label>
            <!-- Multiple select example -->
            <select class="form-select" id="divingRecCourses" name="divingRecCourses" data-select multiple aria-label="Tipologia corsi">            
                <option value="">Seleziona corsi</option>
                <option value="padi-levels"{% if business.divingRecCourses is not empty and business.divingRecCourses contains 'padi-levels' %} selected{% endif %}>PADI (1° 2° 3° livello)</option>
                <option value="ssi-levels"{% if business.divingRecCourses is not empty and business.divingRecCourses contains 'ssi-levels' %} selected{% endif %}>SSI (1° 2° 3° livello)</option>
                <option value="cmas-levels"{% if business.divingRecCourses is not empty and business.divingRecCourses contains 'cmas-levels' %} selected{% endif %}>CMAS (1° 2° 3° livello)</option>
                <option value="bsac-levels"{% if business.divingRecCourses is not empty and business.divingRecCourses contains 'bsac-levels' %} selected{% endif %}>BSAC (1° 2° 3° livello)</option>
                <option value="rescue-diver"{% if business.divingRecCourses is not empty and business.divingRecCourses contains 'rescue-diver' %} selected{% endif %}>Rescue Diver</option>
                <option value="specialty-courses"{% if business.divingRecCourses is not empty and business.divingRecCourses contains 'specialty-courses' %} selected{% endif %}>Specialty Courses</option>
                <option value="become-instructor"{% if business.divingRecCourses is not empty and business.divingRecCourses contains 'become-instructor' %} selected{% endif %}>Diventa istruttore</option>
                <option value="become-guide"{% if business.divingRecCourses is not empty and business.divingRecCourses contains 'become-guide' %} selected{% endif %}>Diventa guida</option>
                <option value="mini-sub-kids"{% if business.divingRecCourses is not empty and business.divingRecCourses contains 'mini-sub-kids' %} selected{% endif %}>Mini sub (per ragazzi)</option>
            </select>
        </div> 
        <div class="col-12">
            <label class="form-label pb-1 mb-2">Tipologie di imbarcazioni</label>
            <div class="nav nav-pills flex-wrap gap-3">
                <div>
                    <input type="checkbox" class="btn-check" id="divingRecInflatable" name="divingRecInflatable"{% if business.divingRecInflatable %} checked{% endif %}>
                    <label class="nav-link" for="divingRecInflatable">
                        <img src="{{ contextPath }}/fe/img/icons/inflatable.svg" alt="Gommoni" class="ms-n1 me-2 w-25px">
                        Gommoni
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingRecRigidBoat" name="divingRecRigidBoat"{% if business.divingRecRigidBoat %} checked{% endif %}>
                    <label class="nav-link" for="divingRecRigidBoat">
                        <img src="{{ contextPath }}/fe/img/icons/rigid-boat.svg" alt="Barca rigida" class="ms-n1 me-2 w-25px">
                        Barca rigida
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingRecCruise" name="divingRecCruise"{% if business.divingRecCruise %} checked{% endif %}>
                    <label class="nav-link" for="divingRecCruise">
                        <img src="{{ contextPath }}/fe/img/icons/cruise.svg" alt="Crociere" class="ms-n1 me-2 w-25px">
                        Crociere
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingRecCatamaran" name="divingRecCatamaran"{% if business.divingRecCatamaran %} checked{% endif %}>
                    <label class="nav-link" for="divingRecCatamaran">
                        <img src="{{ contextPath }}/fe/img/icons/catamaran.svg" alt="Catamarano" class="ms-n1 me-2 w-25px">
                        Catamarano
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!--
    #################################
    IMMERSIONI - TECNICHE
    #################################
    -->
    <hr>
    <h2 class="h6">Immersioni tecniche</h2>

    <!-- GAS -->
    <div class="row gap-2 pb-3 pb-sm-4">
        <div class="col-12">
            <label class="form-label pb-1 mb-2">Gas supportati</label>
            <div class="nav nav-pills flex-wrap gap-3">
                <div>
                    <input type="checkbox" class="btn-check" id="divingTecAir" name="divingTecAir"{% if business.divingTecAir %} checked{% endif %}>
                    <label class="nav-link" for="divingTecAir">
                        <img src="{{ contextPath }}/fe/img/icons/air.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Aria
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingTecNitrox40" name="divingTecNitrox40"{% if business.divingTecNitrox40 %} checked{% endif %}>
                    <label class="nav-link" for="divingTecNitrox40">
                        <img src="{{ contextPath }}/fe/img/icons/nitrox.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Nitrox
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingTecAdvancedNitrox" name="divingTecAdvancedNitrox"{% if business.divingTecAdvancedNitrox %} checked{% endif %}>
                    <label class="nav-link" for="divingTecAdvancedNitrox">
                        <img src="{{ contextPath }}/fe/img/icons/nitrox.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Nitrox Avanzato
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingTecTrimixNormoxic" name="divingTecTrimixNormoxic"{% if business.divingTecTrimixNormoxic %} checked{% endif %}>
                    <label class="nav-link" for="divingTecTrimixNormoxic">
                        <img src="{{ contextPath }}/fe/img/icons/trimix.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Trimix Normossico
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingTecTrimixHypoxic" name="divingTecTrimixHypoxic"{% if business.divingTecTrimixHypoxic %} checked{% endif %}>
                    <label class="nav-link" for="divingTecTrimixHypoxic">
                        <img src="{{ contextPath }}/fe/img/icons/trimix.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Trimix Ipossico
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingTecPureOxygen" name="divingTecPureOxygen"{% if business.divingTecPureOxygen %} checked{% endif %}>
                    <label class="nav-link" for="divingTecPureOxygen">
                        <img src="{{ contextPath }}/fe/img/icons/oxygen.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Ossigeno puro
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingTecHeliox" name="divingTecHeliox"{% if business.divingTecHeliox %} checked{% endif %}>
                    <label class="nav-link" for="divingTecHeliox">
                        <img src="{{ contextPath }}/fe/img/icons/heliox.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Heliox
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- SISTEMI RESPIRAZIONE -->
    <div class="row gap-2 pb-3 pb-sm-4">
        <div class="col-12">
            <label class="form-label pb-1 mb-2">Sistema di respirazione</label>
            <div class="nav nav-pills flex-wrap gap-3">
                <div>
                    <input type="checkbox" class="btn-check" id="divingTecOpenCircuit" name="divingTecOpenCircuit"{% if business.divingTecOpenCircuit %} checked{% endif %}>
                    <label class="nav-link" for="divingTecOpenCircuit">
                        <img src="{{ contextPath }}/fe/img/icons/open-circuit.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Open Circuit
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingTecSemiClosedCircuit" name="divingTecSemiClosedCircuit"{% if business.divingTecSemiClosedCircuit %} checked{% endif %}>
                    <label class="nav-link" for="divingTecSemiClosedCircuit">
                        <img src="{{ contextPath }}/fe/img/icons/semi-closed-circuit.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Semi Closed (SCR)
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="divingTecClosedCircuit" name="divingTecClosedCircuit"{% if business.divingTecClosedCircuit %} checked{% endif %}>
                    <label class="nav-link" for="divingTecClosedCircuit">
                        <img src="{{ contextPath }}/fe/img/icons/closed-circuit.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Closed Circuit (CCR)
                    </label>
                </div>            
            </div>
        </div>
    </div>

    <!-- ATTREZZATURA -->
    <div class="row gap-2 pb-3 pb-sm-4">
        <div class="col-12">
            <label for="divingTecEquipmentConfiguration" class="form-label">Configurazioni attrezzatura</label><br>        
            <select class="form-select form-select-lg" data-select multiple aria-label="Configurazioni attrezzatura" name="divingTecEquipmentConfiguration" id="divingTecEquipmentConfiguration">
                <option value="">Seleziona configurazione</option>
                <option value="monobombola"{% if business.divingTecEquipmentConfiguration is not empty and business.divingTecEquipmentConfiguration contains 'monobombola' %} selected{% endif %}>Monobombola</option>
                <option value="bibombolaBackmount"{% if business.divingTecEquipmentConfiguration is not empty and business.divingTecEquipmentConfiguration contains 'bibombolaBackmount' %} selected{% endif %}>Bibombola backmount</option>
                <option value="sidemount"{% if business.divingTecEquipmentConfiguration is not empty and business.divingTecEquipmentConfiguration contains 'sidemount' %} selected{% endif %}>Sidemount</option>
                <option value="stageDeco"{% if business.divingTecEquipmentConfiguration is not empty and business.divingTecEquipmentConfiguration contains 'stageDeco' %} selected{% endif %}>Stage deco</option>
                <option value="bailoutCcr"{% if business.divingTecEquipmentConfiguration is not empty and business.divingTecEquipmentConfiguration contains 'bailoutCcr' %} selected{% endif %}>Bailout (CCR)</option>
            </select>         
        </div>
    </div>

    <!-- PROFONDITÀ MASSIMA -->
    <div class="row gap-2 pb-3 pb-sm-4">
        <div class="col-12">
            <label for="divingTecMaxDepth" class="form-label">Profondità massima</label><br>        
            <select class="form-select form-select-lg" aria-label="Profondità massima" name="divingTecMaxDepth" id="divingTecMaxDepth">
                <option value="">Seleziona profondità</option>
                <option value="40-60"{% if business.divingTecMaxDepth == '40-60' %} selected{% endif %}>40–60 m</option>
                <option value="60-100"{% if business.divingTecMaxDepth == '60-100' %} selected{% endif %}>60–100 m</option>
                <option value="100plus"{% if business.divingTecMaxDepth == '100plus' %} selected{% endif %}>100+ m</option>
            </select>         
        </div>
    </div>

    <!--
    #################################
    IMMERSIONI - VARIE
    #################################
    -->
    <hr>
    <h2 class="h6">Varie</h2>
    <div class="row pb-3 pb-sm-4">
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Noleggio attrezzatura</label><br>
            <input type="checkbox" class="form-check-input" id="divingEquipmentRental" name="divingEquipmentRental"{% if business.divingEquipmentRental %} checked{% endif %}>
            <label class="form-check-label" for="divingEquipmentRental">Sì</label>
        </div>
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Acquisto attrezzatura \ Negozio</label><br>
            <input type="checkbox" class="form-check-input" id="divingEquipmentPurchase" name="divingEquipmentPurchase"{% if business.divingEquipmentPurchase %} checked{% endif %}>
            <label class="form-check-label" for="divingEquipmentPurchase">Sì</label>
        </div>    
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Lezioni private</label><br>
            <input type="checkbox" class="form-check-input" id="divingPrivateLessons" name="divingPrivateLessons"{% if business.divingPrivateLessons %} checked{% endif %}>
            <label class="form-check-label" for="divingPrivateLessons">Sì</label>
        </div>    
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Deposito attrezzatura</label><br>
            <input type="checkbox" class="form-check-input" id="divingEquipmentStorage" name="divingEquipmentStorage"{% if business.divingEquipmentStorage %} checked{% endif %}>
            <label class="form-check-label" for="divingEquipmentStorage">Sì</label>
        </div>
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Vasche risciacquo</label><br>
            <input type="checkbox" class="form-check-input" id="divingRinseTanks" name="divingRinseTanks"{% if business.divingRinseTanks %} checked{% endif %}>
            <label class="form-check-label" for="divingRinseTanks">Sì</label>
        </div>    
    </div>
</div>

<div class="freedivingApneaContainer">
    <!--
    #################################
    FREEDIVING E APNEA
    #################################
    -->
    <hr>
    <div class="d-flex flex-column flex-sm-row flex-md-column flex-lg-row align-items-center justify-content-between gap-3 bg-body-tertiary rounded border border-dark p-3 mb-3 mb-sm-4">
        <div class="d-flex align-items-center gap-2">
            <img src="{{ contextPath }}/fe/img/account/apnea.svg" alt="Image" width="32">
            <div class="d-flex align-items-center gap-1 ps-1">
                <div class="h6 mb-0">Freediving & Apnea</div>            
            </div>
        </div>
    </div>

    <!-- CORSI -->
    <div class="row gap-2 pb-3 pb-sm-4">

        <div class="col-12">
            <label for="freedivingApneaCourses" class="form-label">Tipologia corsi</label>
            <!-- Multiple select example -->
            <select class="form-select" id="freedivingApneaCourses" name="freedivingApneaCourses" data-select multiple aria-label="Tipologia corsi">
                <option value="">Seleziona corsi</option>
                <option value="apnea-academy"{% if business.freedivingApneaCourses is not empty and business.freedivingApneaCourses contains 'apnea-academy' %} selected{% endif %}>Apnea Academy (1° 2° 3° livello)</option>
                <option value="aida-international"{% if business.freedivingApneaCourses is not empty and business.freedivingApneaCourses contains 'aida-international' %} selected{% endif %}>AIDA International (1° 2° 3° livello)</option>
                <option value="ssi"{% if business.freedivingApneaCourses is not empty and business.freedivingApneaCourses contains 'ssi' %} selected{% endif %}>SSI (1° 2° 3° livello)</option>
                <option value="instructor"{% if business.freedivingApneaCourses is not empty and business.freedivingApneaCourses contains 'instructor' %} selected{% endif %}>Istruttore</option>            
            </select>
        </div>    
    </div>

    <!-- ALLENAMENTI -->
    <div class="row gap-2 pb-3 pb-sm-4">
        <div class="col-12 mb-3">
            <label class="form-label pb-1 mb-2">Allenamenti in</label>
            <div class="nav nav-pills flex-wrap gap-3">
                <div>
                    <input type="checkbox" class="btn-check" id="freedivingApneaTrainingPlacePool" name="freedivingApneaTrainingPlacePool"{% if business.freedivingApneaTrainingPlacePool %} checked{% endif %}>
                    <label class="nav-link" for="freedivingApneaTrainingPlacePool">
                        <img src="{{ contextPath }}/fe/img/icons/pool.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Piscina
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="freedivingApneaTrainingPlaceLake" name="freedivingApneaTrainingPlaceLake"{% if business.freedivingApneaTrainingPlaceLake %} checked{% endif %}>
                    <label class="nav-link" for="freedivingApneaTrainingPlaceLake">
                        <img src="{{ contextPath }}/fe/img/icons/lake.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Lago
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="freedivingApneaTrainingPlaceSea" name="freedivingApneaTrainingPlaceSea"{% if business.freedivingApneaTrainingPlaceSea %} checked{% endif %}>
                    <label class="nav-link" for="freedivingApneaTrainingPlaceSea">
                        <img src="{{ contextPath }}/fe/img/icons/sea.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Mare
                    </label>
                </div>
            </div>
        </div>

        <div class="col-12">
            <label for="freedivingApneaInstructorCourses" class="form-label">Corsi per Istruttori</label>
            <select class="form-select" id="freedivingApneaInstructorCourses" name="freedivingApneaInstructorCourses" data-select multiple aria-label="Corsi per Istruttori">
                <option value="">Seleziona corso</option>
                <option value="freediving-instructor"{% if business.freedivingApneaInstructorCourses is not empty and business.freedivingApneaInstructorCourses contains 'freediving-instructor' %} selected{% endif %}>Freediving Instructor (SSI / PADI / AIDA / AA / Molchanovs)</option>
                <option value="assistant-instructor"{% if business.freedivingApneaInstructorCourses is not empty and business.freedivingApneaInstructorCourses contains 'assistant-instructor' %} selected{% endif %}>Assistant Instructor / Coach</option>
            </select>
        </div>

        <div class="col-12 mt-3">
            <label for="freedivingApneaSpecialties" class="form-label">Specialità</label>
            <select class="form-select" id="freedivingApneaSpecialties" name="freedivingApneaSpecialties" data-select multiple aria-label="Specialità">
                <option value="">Seleziona specialità</option>
                <option value="static-apnea"{% if business.freedivingApneaSpecialties is not empty and business.freedivingApneaSpecialties contains 'static-apnea' %} selected{% endif %}>Apnea statica (STA)</option>
                <option value="dynamic-apnea"{% if business.freedivingApneaSpecialties is not empty and business.freedivingApneaSpecialties contains 'dynamic-apnea' %} selected{% endif %}>Apnea dinamica (DYN/DNF)</option>
                <option value="constant-weight"{% if business.freedivingApneaSpecialties is not empty and business.freedivingApneaSpecialties contains 'constant-weight' %} selected{% endif %}>Assetto costante (CWT/CNF/FIM)</option>
                <option value="advanced-equalization"{% if business.freedivingApneaSpecialties is not empty and business.freedivingApneaSpecialties contains 'advanced-equalization' %} selected{% endif %}>Compensazione avanzata (Equalization)</option>
                <option value="deep-equalization"{% if business.freedivingApneaSpecialties is not empty and business.freedivingApneaSpecialties contains 'deep-equalization' %} selected{% endif %}>Apnea profonda / Deep Equalization</option>
                <option value="mental-training"{% if business.freedivingApneaSpecialties is not empty and business.freedivingApneaSpecialties contains 'mental-training' %} selected{% endif %}>Allenamento mentale / Mental Training per apnea</option>
                <option value="monofin-noseclip-goggles"{% if business.freedivingApneaSpecialties is not empty and business.freedivingApneaSpecialties contains 'monofin-noseclip-goggles' %} selected{% endif %}>Monopinna e utilizzo tappa naso e occhialini</option>
                <option value="athlete-preparation"{% if business.freedivingApneaSpecialties is not empty and business.freedivingApneaSpecialties contains 'athlete-preparation' %} selected{% endif %}>Preparazione atleti</option>
            </select>
        </div>

        <div class="col-12 mt-3">
            <label for="freedivingApneaWorkshops" class="form-label">Workshop</label>
            <select class="form-select" id="freedivingApneaWorkshops" name="freedivingApneaWorkshops" data-select multiple aria-label="Workshop">
                <option value="">Seleziona workshop</option>
                <option value="clinic-professional-athletes"{% if business.freedivingApneaWorkshops is not empty and business.freedivingApneaWorkshops contains 'clinic-professional-athletes' %} selected{% endif %}>Clinic con atleti professionisti</option>
                <option value="freediving-camp"{% if business.freedivingApneaWorkshops is not empty and business.freedivingApneaWorkshops contains 'freediving-camp' %} selected{% endif %}>Campi di allenamento apnea (Freediving Camp)</option>
                <option value="equalization-workshop"{% if business.freedivingApneaWorkshops is not empty and business.freedivingApneaWorkshops contains 'equalization-workshop' %} selected{% endif %}>Workshop compensazione</option>
                <option value="yoga-apnea"{% if business.freedivingApneaWorkshops is not empty and business.freedivingApneaWorkshops contains 'yoga-apnea' %} selected{% endif %}>Yoga & Apnea</option>
                <option value="rescue-safety"{% if business.freedivingApneaWorkshops is not empty and business.freedivingApneaWorkshops contains 'rescue-safety' %} selected{% endif %}>Rescue & Safety for Freedivers</option>
            </select>
        </div>

        <div class="col-12 mt-3">
            <label for="freedivingApneaStages" class="form-label">Stage</label>
            <select class="form-select" id="freedivingApneaStages" name="freedivingApneaStages" data-select multiple aria-label="Stage">
                <option value="">Seleziona stage</option>
                <option value="multidisciplinary-stage"{% if business.freedivingApneaStages is not empty and business.freedivingApneaStages contains 'multidisciplinary-stage' %} selected{% endif %}>Stage Multidisciplinare</option>
                <option value="deep-equalization-stage"{% if business.freedivingApneaStages is not empty and business.freedivingApneaStages contains 'deep-equalization-stage' %} selected{% endif %}>Stage Compensazione Profonda</option>
                <option value="static-apnea-stage"{% if business.freedivingApneaStages is not empty and business.freedivingApneaStages contains 'static-apnea-stage' %} selected{% endif %}>Stage di Apnea Statica (STA Camp)</option>
                <option value="constant-weight-stage"{% if business.freedivingApneaStages is not empty and business.freedivingApneaStages contains 'constant-weight-stage' %} selected{% endif %}>Stage Assetto Costante (CWT/CNF/FIM)</option>
                <option value="altitude-stage"{% if business.freedivingApneaStages is not empty and business.freedivingApneaStages contains 'altitude-stage' %} selected{% endif %}>Stage in Altitudine</option>
                <option value="athlete-stage"{% if business.freedivingApneaStages is not empty and business.freedivingApneaStages contains 'athlete-stage' %} selected{% endif %}>Stage per Atleti / Agonisti</option>
                <option value="monothematic-stage"{% if business.freedivingApneaStages is not empty and business.freedivingApneaStages contains 'monothematic-stage' %} selected{% endif %}>Stage Monotematico (Tecnica, Sicurezza, Rescue, ecc.)</option>
                <option value="photo-biology-stage"{% if business.freedivingApneaStages is not empty and business.freedivingApneaStages contains 'photo-biology-stage' %} selected{% endif %}>Apnea & Fotografia Subacquea / Biologia Marina</option>
                <option value="instructor-assistant-stage"{% if business.freedivingApneaStages is not empty and business.freedivingApneaStages contains 'instructor-assistant-stage' %} selected{% endif %}>Stage per Istruttori / Assistenti</option>
            </select>
        </div>           
    </div>

    <!-- ALTRO -->
    <div class="row">
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Sessioni Yoga</label><br>
            <input type="checkbox" class="form-check-input" id="freedivingApneaYoga" name="freedivingApneaYoga"{% if business.freedivingApneaYoga %} checked{% endif %}>
            <label class="form-check-label" for="freedivingApneaYoga">Sì</label>
        </div>
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Allenatori certificati</label><br>
            <input type="checkbox" class="form-check-input" id="freedivingApneaCertifiedInstructors" name="freedivingApneaCertifiedInstructors"{% if business.freedivingApneaCertifiedInstructors %} checked{% endif %}>
            <label class="form-check-label" for="freedivingApneaCertifiedInstructors">Sì</label>
        </div>
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Foto e video</label><br>
            <input type="checkbox" class="form-check-input" id="freedivingApneaPhotoVideo" name="freedivingApneaPhotoVideo"{% if business.freedivingApneaPhotoVideo %} checked{% endif %}>
            <label class="form-check-label" for="freedivingApneaPhotoVideo">Sì</label>
        </div>
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Noleggio attrezzatura</label><br>
            <input type="checkbox" class="form-check-input" id="freedivingApneaEquipmentRental" name="freedivingApneaEquipmentRental"{% if business.freedivingApneaEquipmentRental %} checked{% endif %}>
            <label class="form-check-label" for="freedivingApneaEquipmentRental">Sì</label>
        </div>
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Acquisto attrezzatura \ Negozio</label><br>
            <input type="checkbox" class="form-check-input" id="freedivingApneaEquipmentPurchase" name="freedivingApneaEquipmentPurchase"{% if business.freedivingApneaEquipmentPurchase %} checked{% endif %}>
            <label class="form-check-label" for="freedivingApneaEquipmentPurchase">Sì</label>
        </div>
    </div>
</div>

<div class="experienceContainer">
    <!--
    #################################
    ESPERIENZE
    #################################
    -->
    <hr>
    <div class="d-flex flex-column flex-sm-row flex-md-column flex-lg-row align-items-center justify-content-between gap-3 bg-body-tertiary rounded border border-dark p-3 mb-3 mb-sm-4">
        <div class="d-flex align-items-center gap-2">
            <img src="{{ contextPath }}/fe/img/account/experience.svg" alt="Image" width="32">
            <div class="d-flex align-items-center gap-1 ps-1">
                <div class="h6 mb-0">Esperienze</div>            
            </div>
        </div>
    </div>

    <!-- ESPERIENZE -->
    <div class="row gap-2 pb-3 pb-sm-4">
        <div class="col-12 mb-3">
            <label class="form-label pb-1 mb-2">Esperienze disponibili</label>
            <div class="nav nav-pills flex-wrap gap-3">
                <div>
                    <input type="checkbox" class="btn-check" id="experienceSnorkeling" name="experienceSnorkeling"{% if business.experienceSnorkeling %} checked{% endif %}>
                    <label class="nav-link" for="experienceSnorkeling">
                        <img src="{{ contextPath }}/fe/img/icons/snorkeling.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Snorkeling
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="experienceBoatTrip" name="experienceBoatTrip"{% if business.experienceBoatTrip %} checked{% endif %}>
                    <label class="nav-link" for="experienceBoatTrip">
                        <img src="{{ contextPath }}/fe/img/icons/boat-trip.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Gite in barca
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="experienceTurtleWatching" name="experienceTurtleWatching"{% if business.experienceTurtleWatching %} checked{% endif %}>
                    <label class="nav-link" for="experienceTurtleWatching">
                        <img src="{{ contextPath }}/fe/img/icons/turtle.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Avvistamento Tartarughe
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="experienceDolphinWatching" name="experienceDolphinWatching"{% if business.experienceDolphinWatching %} checked{% endif %}>
                    <label class="nav-link" for="experienceDolphinWatching">
                        <img src="{{ contextPath }}/fe/img/icons/dolphin.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Avvistamento Delfini
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="experienceWhaleWatching" name="experienceWhaleWatching"{% if business.experienceWhaleWatching %} checked{% endif %}>
                    <label class="nav-link" for="experienceWhaleWatching">
                        <img src="{{ contextPath }}/fe/img/icons/whale.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Avvistamento Balene
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="experienceSunsetTrip" name="experienceSunsetTrip"{% if business.experienceSunsetTrip %} checked{% endif %}>
                    <label class="nav-link" for="experienceSunsetTrip">
                        <img src="{{ contextPath }}/fe/img/icons/sunset-trip.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Gita al tramonto
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="experienceIsletExcursion" name="experienceIsletExcursion"{% if business.experienceIsletExcursion %} checked{% endif %}>
                    <label class="nav-link" for="experienceIsletExcursion">
                        <img src="{{ contextPath }}/fe/img/icons/islet-excursion.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Escursione su isolotti
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="experienceNatureTour" name="experienceNatureTour"{% if business.experienceNatureTour %} checked{% endif %}>
                    <label class="nav-link" for="experienceNatureTour">
                        <img src="{{ contextPath }}/fe/img/icons/nature-tour.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Tour naturalistico
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="experienceBoatYoga" name="experienceBoatYoga"{% if business.experienceBoatYoga %} checked{% endif %}>
                    <label class="nav-link" for="experienceBoatYoga">
                        <img src="{{ contextPath }}/fe/img/icons/boat-yoga.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Yoga in barca
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="experienceNightBoatTrip" name="experienceNightBoatTrip"{% if business.experienceNightBoatTrip %} checked{% endif %}>
                    <label class="nav-link" for="experienceNightBoatTrip">
                        <img src="{{ contextPath }}/fe/img/icons/night-boat.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Notturna in barca
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="experienceDinnerTasting" name="experienceDinnerTasting"{% if business.experienceDinnerTasting %} checked{% endif %}>
                    <label class="nav-link" for="experienceDinnerTasting">
                        <img src="{{ contextPath }}/fe/img/icons/dinner-tasting.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Degustazione / Cena in Barca
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="experiencePhotoTour" name="experiencePhotoTour"{% if business.experiencePhotoTour %} checked{% endif %}>
                    <label class="nav-link" for="experiencePhotoTour">
                        <img src="{{ contextPath }}/fe/img/icons/photo-tour.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Tour fotografico
                    </label>
                </div>

            </div>
        </div>

    </div>

    <!-- ALTRO -->
    <div class="row">
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Tour privati</label><br>
            <input type="checkbox" class="form-check-input" id="experiencePrivateTours" name="experiencePrivateTours"{% if business.experiencePrivateTours %} checked{% endif %}>
            <label class="form-check-label" for="experiencePrivateTours">Sì</label>
        </div>        
    </div>
</div>

<div class="sportContainer">
    <!--
    #################################
    SPORT
    #################################
    -->
    <hr>
    <div class="d-flex flex-column flex-sm-row flex-md-column flex-lg-row align-items-center justify-content-between gap-3 bg-body-tertiary rounded border border-dark p-3 mb-3 mb-sm-4">
        <div class="d-flex align-items-center gap-2">
            <img src="{{ contextPath }}/fe/img/account/sport.svg" alt="Image" width="32">
            <div class="d-flex align-items-center gap-1 ps-1">
                <div class="h6 mb-0">Sport</div>            
            </div>
        </div>
    </div>

    <!-- TIPI DI SPORT -->
    <div class="row gap-2 pb-3 pb-sm-4">
        <div class="col-12 mb-3">
            <label class="form-label pb-1 mb-2">Tipi di sport offerti</label>
            <div class="nav nav-pills flex-wrap gap-3">            
                <div>
                    <input type="checkbox" class="btn-check" id="sportSurf" name="sportSurf"{% if business.sportSurf %} checked{% endif %}>
                    <label class="nav-link" for="sportSurf">
                        <img src="{{ contextPath }}/fe/img/icons/surf.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Surf
                    </label>
                </div>        
                <div>
                    <input type="checkbox" class="btn-check" id="sportWindsurf" name="sportWindsurf"{% if business.sportWindsurf %} checked{% endif %}>
                    <label class="nav-link" for="sportWindsurf">
                        <img src="{{ contextPath }}/fe/img/icons/windsurf.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Windsurf
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="sportKitesurf" name="sportKitesurf"{% if business.sportKitesurf %} checked{% endif %}>
                    <label class="nav-link" for="sportKitesurf">
                        <img src="{{ contextPath }}/fe/img/icons/kitesurf.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Kitesurf
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="sportSup" name="sportSup"{% if business.sportSup %} checked{% endif %}>
                    <label class="nav-link" for="sportSup">
                        <img src="{{ contextPath }}/fe/img/icons/sup.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        SUP
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="sportKayak" name="sportKayak"{% if business.sportKayak %} checked{% endif %}>
                    <label class="nav-link" for="sportKayak">
                        <img src="{{ contextPath }}/fe/img/icons/kayak.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Kayak
                    </label>
                </div>
                <div>
                    <input type="checkbox" class="btn-check" id="sportCanoe" name="sportCanoe"{% if business.sportCanoe %} checked{% endif %}>                
                    <label class="nav-link" for="sportCanoe">
                        <img src="{{ contextPath }}/fe/img/icons/canoe.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Canoa
                    </label>
                </div> 
                <div>
                    <input type="checkbox" class="btn-check" id="sportBodyboard" name="sportBodyboard"{% if business.sportBodyboard %} checked{% endif %}>                
                    <label class="nav-link" for="sportBodyboard">
                        <img src="{{ contextPath }}/fe/img/icons/bodyboard.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Bodyboard
                    </label>
                </div> 
                <div>
                    <input type="checkbox" class="btn-check" id="sportWakeboard" name="sportWakeboard"{% if business.sportWakeboard %} checked{% endif %}>                
                    <label class="nav-link" for="sportWakeboard">
                        <img src="{{ contextPath }}/fe/img/icons/wakeboard.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Wakeboard
                    </label>
                </div> 
                <div>
                    <input type="checkbox" class="btn-check" id="sportSkimboard" name="sportSkimboard"{% if business.sportSkimboard %} checked{% endif %}>                
                    <label class="nav-link" for="sportSkimboard">
                        <img src="{{ contextPath }}/fe/img/icons/skimboard.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Skimboard
                    </label>
                </div> 
                <div>
                    <input type="checkbox" class="btn-check" id="sportParasailing" name="sportParasailing"{% if business.sportParasailing %} checked{% endif %}>                
                    <label class="nav-link" for="sportParasailing">
                        <img src="{{ contextPath }}/fe/img/icons/parasailing.svg" alt="Image" class="ms-n1 me-2 w-25px">
                        Parasailing
                    </label>
                </div> 
            </div>
        </div>
    </div>
    <div class="row pb-3 pb-sm-4">
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Noleggio attrezzatura</label><br>
            <input type="checkbox" class="form-check-input" id="sportEquipmentRental" name="sportEquipmentRental"{% if business.sportEquipmentRental %} checked{% endif %}>
            <label class="form-check-label" for="sportEquipmentRental">Sì</label>
        </div>
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Acquisto attrezzatura \ Negozio</label><br>
            <input type="checkbox" class="form-check-input" id="sportEquipmentPurchase" name="sportEquipmentPurchase"{% if business.sportEquipmentPurchase %} checked{% endif %}>
            <label class="form-check-label" for="sportEquipmentPurchase">Sì</label>
        </div>
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Corsi</label><br>
            <input type="checkbox" class="form-check-input" id="sportCourses" name="sportCourses"{% if business.sportCourses %} checked{% endif %}>
            <label class="form-check-label" for="sportCourses">Sì</label>
        </div>
        <div class="col-6 col-md-4 mb-3">
            <label class="form-label">Lezioni private</label><br>
            <input type="checkbox" class="form-check-input" id="sportPrivateLessons" name="sportPrivateLessons"{% if business.sportPrivateLessons %} checked{% endif %}>
            <label class="form-check-label" for="sportPrivateLessons">Sì</label>
        </div>
    </div>
</div>

<!--
#################################
VARIE (sempre presente)
#################################
-->

<div class="d-flex flex-column flex-sm-row flex-md-column flex-lg-row align-items-center justify-content-between gap-3 bg-body-tertiary rounded border border-dark p-3 mb-3 mb-sm-4">
    <div class="d-flex align-items-center gap-2">
        <img src="{{ contextPath }}/fe/img/account/misc.svg" alt="Image" width="32">
        <div class="d-flex align-items-center gap-1 ps-1">
            <div class="h6 mb-0">Varie</div>            
        </div>
    </div>
</div>

<div class="row pb-3 pb-sm-4">    
    <div class="col-md-6 mb-3">
        <label for="miscLargeGroupsAvailability" class="form-label">Disponibilità grandi gruppi</label>
        <select class="form-select form-select-lg" id="miscLargeGroupsAvailability" name="miscLargeGroupsAvailability">
            <option value="">Seleziona disponibilità</option>
            <option value="15-50"{% if business.miscLargeGroupsAvailability == '15-50' %} selected{% endif %}>Da 15 a 50 persone</option>
            <option value="50plus"{% if business.miscLargeGroupsAvailability == '50plus' %} selected{% endif %}>Sopra 50 persone</option>
            <option value="no"{% if business.miscLargeGroupsAvailability == 'no' %} selected{% endif %}>No</option>
        </select>
    </div>       
    <div class="col-md-6 mb-3">
        <label class="form-label">Scontistica grandi gruppi</label><br>
        <input type="checkbox" class="form-check-input" id="miscLargeGroupsDiscount" name="miscLargeGroupsDiscount"{% if business.miscLargeGroupsDiscount %} checked{% endif %}>
        <label class="form-check-label" for="miscLargeGroupsDiscount">Sì</label>
    </div>
    <div class="col-6 col-md-4 mb-3">
        <label class="form-label">WiFi</label><br>
        <input type="checkbox" class="form-check-input" id="miscWifi" name="miscWifi"{% if business.miscWifi %} checked{% endif %}>
        <label class="form-check-label" for="miscWifi">Sì</label>
    </div>
    <div class="col-6 col-md-4 mb-3">
        <label class="form-label">Spogliatoi \ Docce</label><br>
        <input type="checkbox" class="form-check-input" id="miscChangeRoomsShowers" name="miscChangeRoomsShowers"{% if business.miscChangeRoomsShowers %} checked{% endif %}>
        <label class="form-check-label" for="miscChangeRoomsShowers">Sì</label>
    </div>    
    <div class="col-6 col-md-4 mb-3">
        <label class="form-label">Zona relax \ Bar</label><br>
        <input type="checkbox" class="form-check-input" id="miscRelaxArea" name="miscRelaxArea"{% if business.miscRelaxArea %} checked{% endif %}>
        <label class="form-check-label" for="miscRelaxArea">Sì</label>
    </div>
    <div class="col-6 col-md-4 mb-3">
        <label class="form-label">Acqua gratuita</label><br>
        <input type="checkbox" class="form-check-input" id="miscFreeWater" name="miscFreeWater"{% if business.miscFreeWater %} checked{% endif %}>
        <label class="form-check-label" for="miscFreeWater">Sì</label>
    </div>
    <div class="col-6 col-md-4 mb-3">
        <label class="form-label">Kit emergenza \ Camera iperbarica</label><br>
        <input type="checkbox" class="form-check-input" id="miscEmergencyKit" name="miscEmergencyKit"{% if business.miscEmergencyKit %} checked{% endif %}>
        <label class="form-check-label" for="miscEmergencyKit">Sì</label>
    </div>
    <div class="col-6 col-md-4 mb-3">
        <label class="form-label">Convenzioni hotel \ ristoranti</label><br>
        <input type="checkbox" class="form-check-input" id="miscHotelRestaurantDeals" name="miscHotelRestaurantDeals"{% if business.miscHotelRestaurantDeals %} checked{% endif %}>
        <label class="form-check-label" for="miscHotelRestaurantDeals">Sì</label>
    </div>
    <div class="col-6 col-md-4 mb-3">
        <label class="form-label">Transfer</label><br>
        <input type="checkbox" class="form-check-input" id="miscTransfer" name="miscTransfer"{% if business.miscTransfer %} checked{% endif %}>
        <label class="form-check-label" for="miscTransfer">Sì</label>
    </div>
</div>