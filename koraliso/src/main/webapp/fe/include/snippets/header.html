{% set navbarClasses = currentPage == '404' ? 'navbar navbar-expand-lg navbar-dark position-absolute w-100 z-fixed px-0' : 'navbar navbar-expand-lg bg-body navbar-sticky sticky-top z-fixed px-0' %}
<!-- Navigation bar (Page header) -->
<header class="{{ navbarClasses }}" data-sticky-element="">    
    <div class="container">

        <!-- Mobile offcanvas menu toggler (Hamburger) -->
        <button type="button" class="navbar-toggler me-3 me-lg-0" data-bs-toggle="offcanvas" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navbar brand (Logo) -->
        <a class="navbar-brand py-1 py-md-2 py-xl-1 me-2 me-sm-n4 me-md-n5 me-lg-0" href="{{ routes('HOME') }}">
            Koraliso
        </a>

        <!-- Main navigation that turns into offcanvas on screens < 992px wide (lg breakpoint) -->
        <nav class="offcanvas offcanvas-start" id="navbarNav" tabindex="-1" aria-labelledby="navbarNavLabel">
            <div class="offcanvas-header py-3">
                <h5 class="offcanvas-title" id="navbarNavLabel">Browse Finder</h5>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body pt-2 pb-4 py-lg-0 mx-lg-auto">
                <ul class="navbar-nav position-relative">
                    <li class="nav-item dropdown py-lg-2 me-lg-n1 me-xl-0">
                    <li class="nav-item py-lg-2 me-lg-n2 me-xl-0">
                        <a class="nav-link rounded-pill active" href="{{ routes('BLOG') }}">Home</a>
                    </li>
                    <li class="nav-item dropdown position-static py-lg-2 me-lg-n1 me-xl-0">
                        <a class="nav-link dropdown-toggle rounded-pill btn btn-primary" aria-current="page" href="#" role="button" data-bs-toggle="dropdown" data-bs-trigger="hover" aria-expanded="false">Attività</a>
                        <ul class="dropdown-menu">                        
                            <li class="hover-effect-opacity px-2 mx-n2" data-video-src="{{ contextPath }}/fe/video/diving.mp4">
                                <a class="dropdown-item d-block rounded-pill mb-0" href="">
                                    <span class="fw-medium">Immersioni</span>
                                    <span class="d-block fs-xs text-body-secondary">Per subacquei esperti e professionisti</span>
                                    <div class="d-none d-lg-block hover-effect-target position-absolute top-0 start-100 bg-body border border-light-subtle rounded rounded-start-0 transition-none invisible opacity-0 p-2 ms-n2" style="width: 212px; height: calc(100% + 2px); margin-top: -1px">
                                        <video 
                                            class="position-relative z-2 menu-video" 
                                            muted 
                                            loop 
                                            preload="none"
                                            playsinline>
                                            <source type="video/mp4">
                                            Il tuo browser non supporta il video.
                                        </video>
                                        <span class="position-absolute top-0 start-0 w-100 h-100 rounded rounded-start-0" style="box-shadow: .875rem .5rem 2rem -.5rem rgba(103, 111, 123, 0.1);"></span>
                                    </div>
                                </a>
                            </li>

                            <li class="hover-effect-opacity px-2 mx-n2" data-video-src="{{ contextPath }}/fe/video/freediving.mp4">
                                <a class="dropdown-item d-block rounded-pill mb-0" href="">
                                    <span class="fw-medium">Freediving & Apnea</span>
                                    <span class="d-block fs-xs text-body-secondary">Disciplina e controllo del respiro sott'acqua</span>
                                    <div class="d-none d-lg-block hover-effect-target position-absolute top-0 start-100 bg-body border border-light-subtle rounded rounded-start-0 transition-none invisible opacity-0 p-2 ms-n2" style="width: 212px; height: calc(100% + 2px); margin-top: -1px">
                                        <video 
                                            class="position-relative z-2 menu-video" 
                                            muted 
                                            loop 
                                            preload="none"
                                            playsinline>
                                            <source type="video/mp4">
                                            Il tuo browser non supporta il video.
                                        </video>
                                        <span class="position-absolute top-0 start-0 w-100 h-100 rounded rounded-start-0" style="box-shadow: .875rem .5rem 2rem -.5rem rgba(103, 111, 123, 0.1);"></span>
                                    </div>
                                </a>
                            </li>

                            <li class="hover-effect-opacity px-2 mx-n2" data-video-src="{{ contextPath }}/fe/video/snorkeling.mp4">
                                <a class="dropdown-item d-block rounded-pill mb-0" href="">
                                    <span class="fw-medium">Snorkeling</span>
                                    <span class="d-block fs-xs text-body-secondary">Scopri il mondo marino dalla superficie</span>
                                    <div class="d-none d-lg-block hover-effect-target position-absolute top-0 start-100 bg-body border border-light-subtle rounded rounded-start-0 transition-none invisible opacity-0 p-2 ms-n2" style="width: 212px; height: calc(100% + 2px); margin-top: -1px">
                                        <video 
                                            class="position-relative z-2 menu-video" 
                                            muted 
                                            loop 
                                            preload="none"
                                            playsinline>
                                            <source type="video/mp4">
                                            Il tuo browser non supporta il video.
                                        </video>
                                        <span class="position-absolute top-0 start-0 w-100 h-100 rounded rounded-start-0" style="box-shadow: .875rem .5rem 2rem -.5rem rgba(103, 111, 123, 0.1);"></span>
                                    </div>
                                </a>
                            </li>

                            <li class="hover-effect-opacity px-2 mx-n2" data-video-src="{{ contextPath }}/fe/video/sport.mp4">
                                <a class="dropdown-item d-block rounded-pill mb-0" href="">
                                    <span class="fw-medium">Sport Acquatici</span>
                                    <span class="d-block fs-xs text-body-secondary">Divertimento e adrenalina sull'acqua</span>
                                    <div class="d-none d-lg-block hover-effect-target position-absolute top-0 start-100 bg-body border border-light-subtle rounded rounded-start-0 transition-none invisible opacity-0 p-2 ms-n2" style="width: 212px; height: calc(100% + 2px); margin-top: -1px">
                                        <video 
                                            class="position-relative z-2 menu-video" 
                                            muted 
                                            loop 
                                            preload="none"
                                            playsinline>
                                            <source type="video/mp4">
                                            Il tuo browser non supporta il video.
                                        </video>
                                        <span class="position-absolute top-0 start-0 w-100 h-100 rounded rounded-start-0" style="box-shadow: .875rem .5rem 2rem -.5rem rgba(103, 111, 123, 0.1);"></span>
                                    </div>
                                </a>
                            </li>
                        </ul>

                    </li>                    
                    <li class="nav-item py-lg-2 me-lg-n2 me-xl-0">
                        <a class="nav-link rounded-pill" href="{{ routes('PROJECTS') }}">Progetti</a>
                    </li>
                    <li class="nav-item py-lg-2 me-lg-n2 me-xl-0">
                        <a class="nav-link rounded-pill" href="{{ routes('EVENTS') }}">Eventi</a>
                    </li>
                    <li class="nav-item py-lg-2 me-lg-n2 me-xl-0">
                        <a class="nav-link rounded-pill" href="{{ routes('BLOG') }}">Blog</a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Button group -->
        <div class="d-flex gap-sm-1">

            <!-- Theme switcher (light/dark/auto) -->
            <div class="dropdown">
                <button type="button" class="theme-switcher btn btn-icon btn-outline-secondary fs-lg border-0 animate-scale {{ currentPage == '404' ? 'text-light' : '' }}" data-bs-toggle="dropdown" data-bs-display="dynamic" aria-expanded="false" aria-label="Toggle theme (light)">
                    <span class="theme-icon-active d-flex animate-target">
                        <i class="fi-sun"></i>
                    </span>
                </button>
                <ul class="dropdown-menu start-50 translate-middle-x" style="--fn-dropdown-min-width: 9rem; --fn-dropdown-spacer: .5rem">
                    <li>
                        <button type="button" class="dropdown-item active" data-bs-theme-value="light" aria-pressed="true">
                            <span class="theme-icon d-flex fs-base me-2">
                                <i class="fi-sun"></i>
                            </span>
                            <span class="theme-label">Light</span>
                            <i class="item-active-indicator fi-check ms-auto"></i>
                        </button>
                    </li>
                    <li>
                        <button type="button" class="dropdown-item" data-bs-theme-value="dark" aria-pressed="false">
                            <span class="theme-icon d-flex fs-base me-2">
                                <i class="fi-moon"></i>
                            </span>
                            <span class="theme-label">Dark</span>
                            <i class="item-active-indicator fi-check ms-auto"></i>
                        </button>
                    </li>
                    <li>
                        <button type="button" class="dropdown-item" data-bs-theme-value="auto" aria-pressed="false">
                            <span class="theme-icon d-flex fs-base me-2">
                                <i class="fi-auto"></i>
                            </span>
                            <span class="theme-label">Auto</span>
                            <i class="item-active-indicator fi-check ms-auto"></i>
                        </button>
                    </li>
                </ul>
            </div>

            <!-- Account button -->
            <div data-bs-theme="{{ currentPage == '404' ? 'dark' : 'light' }}">
                <a class="btn btn-icon btn-outline-secondary fs-lg border-0 animate-scale me-2" href="{{ routes('ACCOUNT_INFO') }}" aria-label="Registrati o accedi">
                    <i class="fi-user animate-target"></i>
                </a>
            </div>

            <!-- Add business button  -->
            <a class="btn btn-primary animate-scale rounded-pill" href="{{ routes('ACCOUNT_BUSINESS_EDIT') }}">
                <i class="fi-plus fs-lg animate-target ms-n2 me-1 me-sm-2"></i>
                Nuova<span class="d-none d-xl-inline ms-1">attività</span>
            </a>
        </div>
    </div>
</header>

