<a id="imageUri" style="display: none;" href="{{ routes('BE_IMAGE') }}?oid=" rel="nofollow"></a>
<div id="loadedImageIds" style="display: none;">
    {% if business.id is not empty %}
        {% if business.imageIds is not empty %}
            {% for imageId in business.imageIds %}{{ imageId }}|{% endfor %}
        {% endif %}
    {% endif %}
</div>

<h1 class="h2 pb-1 pb-lg-2">Foto e video</h1>
<p class="fs-sm">La dimensione massima per le foto è di 8 MB. Formati supportati: jpeg, jpg, png. Inserisci per prima l'immagine principale (puoi trascinare le foto).</p>
<div class="border rounded p-3">
    <div class="row g-2">
        {% if gallery or business.imageIds is empty %}
            <div class="form-group no-margin-bottom">
                <div class="col-md-12">
                    <div class="file-drop-area image-std">
                        <label for="imageIds">Trascina qui le foto</label>
                        <input name="imageIds" id="imageIds" type="file" multiple>
                    </div>
                </div>
            </div>
            <span class="help-block">La prima immagine sarà l'immagine di copertina. Puoi ordinare le foto trascinandole.</span>
            <br>
        {% else %}
            <div class="form-group text-center mb-5">
                {% if business.id is not empty %}
                {% set businessEditWithGalleryUri = routes('ACCOUNT_BUSINESS_EDIT') + '?oid=' + business.id + '&gallery=true&step=3' %}
                {% endif %}
                <a href="{{ businessEditWithGalleryUri }}" class="btn bg-dark-blue legitRipple"><i class="icon-stack-picture"></i> Modifica galleria</a>
            </div>
            {% if business.imageIds is not empty %}
                <span class="help-block text-center">Questa attività contiene delle foto. Clicca sul bottone per modificarle.</span>
            {% else %}
                <span class="help-block text-center">Questa attività non contiene foto. Clicca sul bottone per aggiungerle.</span>
            {% endif %}
            <br>
        {% endif %}
    </div>
</div>
<div class="pt-3 mt-3">
    <label for="linkYoutube" class="form-label">Link video YouTube</label>
    <div class="position-relative">
        <i class="fi-link position-absolute top-50 start-0 translate-middle-y fs-lg ms-3"></i>
        <input type="url" class="form-control form-control-lg form-icon-start" id="linkYoutube" name="linkYoutube" placeholder="www.youtube.com/..." value="{{ business.linkYoutube }}">
    </div>
</div>