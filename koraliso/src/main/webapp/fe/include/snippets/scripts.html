<!-- TEMPLATE SCRIPTS -->
<script src="{{ contextPath }}/fe/js/custom.js"></script>
<script>
addVariables('contextPath', '{{ contextPath }}');
addVariables('baseUrl', '{{ baseUrl }}');
addVariables('publicUrl', '{{ publicUrl }}');
addVariables('language', '{{ language }}');
addVariables('recaptchaSite', '{{ recaptchaSite }}');
</script>

<!-- VENDOR LIBS -->
<script src="{{ contextPath }}/fe/vendor/jquery/jquery-3.7.1.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/swiper/swiper-bundle.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/glightbox/glightbox.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/leaflet/leaflet.js"></script>
<script src="{{ contextPath }}/fe/vendor/list.js/list.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/simplebar/simplebar.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/choices.js/choices.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/cleave.js/cleave.min.js"></script>    
<script src="{{ contextPath }}/fe/vendor/flatpickr/flatpickr.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/flatpickr/it.js"></script>
<script src="{{ contextPath }}/fe/vendor/jquery-validate/jquery.validate.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/jquery-validate/messages_it.min.js"></script>
<script src="{{ contextPath }}/fe/js/config-validate.js"></script>
<script src="{{ contextPath }}/fe/vendor/jquery-blockui/jquery.blockUI.min.js"></script>
<script defer src="{{ contextPath }}/fe/js/sweetalert.js"></script>
<script src="{{ contextPath }}/fe/vendor/jquery-confirm/jquery-confirm.min.js"></script>
<script defer src="{{ contextPath }}/fe/vendor/uri/URI.js"></script>   

<!-- THEME SCRIPTS -->    
<script src="{{ contextPath }}/fe/js/theme-switcher.js"></script>
<script src="{{ contextPath }}/fe/js/theme.min.js"></script>

<script>
(function () {

// INITIALIZATION OF BLOCKUI
// =======================================================
$.blockUI.defaults = {
message: '<div class="fancybox-loading">',
css: {
    padding: 0,
    margin: 0,
    top: '45%',
    left: '50%',
    right: '50%',
    border: 'none',
    backgroundColor: 'transparent',
    cursor: 'wait'
},
overlayCSS: {
    backgroundColor: '#000',
    opacity: 0.4,
    cursor: 'wait'
},
baseZ: 1100,
showOverlay: true
};

})();
</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const menuItems = document.querySelectorAll('.hover-effect-opacity[data-video-src]');
    
    menuItems.forEach(item => {
        const video = item.querySelector('video');
        const source = video.querySelector('source');
        const videoSrc = item.getAttribute('data-video-src');
        let isLoaded = false;
        
        // Preload al mouseenter
        item.addEventListener('mouseenter', function() {
            if (!isLoaded && videoSrc) {
                // Imposta la sorgente del video
                source.src = videoSrc;
                video.load(); // Forza il caricamento
                isLoaded = true;
                
                // Quando il video è pronto, lo fa partire
                video.addEventListener('canplay', function() {
                    video.play().catch(e => {
                        console.log('Autoplay prevented:', e);
                    });
                }, { once: true });
            } else if (isLoaded) {
                // Se già caricato, riproduci
                video.play().catch(e => {
                    console.log('Autoplay prevented:', e);
                });
            }
        });
        
        // Pausa il video quando si esce dal menu
        item.addEventListener('mouseleave', function() {
            if (isLoaded && video) {
                video.pause();
            }
        });
    });
});
</script>
<!-- PAGE SCRIPTS -->
<script src="https://www.google.com/recaptcha/api.js?render={{ recaptchaSite }}"></script>        