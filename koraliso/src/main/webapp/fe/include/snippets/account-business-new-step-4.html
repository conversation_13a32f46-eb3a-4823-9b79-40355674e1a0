<h1 class="h2 pb-1 pb-lg-2">Posizione</h1>
        
<div class="row pb-3 pb-sm-4">
    <div class="col-12 mb-3">
        <label for="country" class="form-label">Paese *</label>
        <select class="form-select form-select-lg" id="country" name="country" data-select="{
                &quot;classNames&quot;: {
                &quot;containerInner&quot;: [&quot;form-select&quot;, &quot;form-select-lg&quot;]
                },
                &quot;searchEnabled&quot;: true
                }" aria-label="Seleziona paese" required="">
            <option value="">Select country</option>
            <optgroup label="Africa">
                <option value="Nigeria"{% if business.country == 'Nigeria' %} selected{% endif %}>Nigeria</option>
                <option value="South Africa"{% if business.country == 'South Africa' %} selected{% endif %}>South Africa</option>
                <option value="Kenya"{% if business.country == 'Kenya' %} selected{% endif %}>Kenya</option>
                <option value="Egypt"{% if business.country == 'Egypt' %} selected{% endif %}>Egypt</option>
                <option value="Ethiopia"{% if business.country == 'Ethiopia' %} selected{% endif %}>Ethiopia</option>
            </optgroup>
            <optgroup label="Asia">
                <option value="China"{% if business.country == 'China' %} selected{% endif %}>China</option>
                <option value="India"{% if business.country == 'India' %} selected{% endif %}>India</option>
                <option value="Japan"{% if business.country == 'Japan' %} selected{% endif %}>Japan</option>
                <option value="South Korea"{% if business.country == 'South Korea' %} selected{% endif %}>South Korea</option>
                <option value="Saudi Arabia"{% if business.country == 'Saudi Arabia' %} selected{% endif %}>Saudi Arabia</option>
            </optgroup>
            <optgroup label="Europe">
                <option value="Germany"{% if business.country == 'Germany' %} selected{% endif %}>Germany</option>
                <option value="France"{% if business.country == 'France' %} selected{% endif %}>France</option>
                <option value="United Kingdom"{% if business.country == 'United Kingdom' %} selected{% endif %}>United Kingdom</option>
                <option value="Italy"{% if business.country == 'Italy' or business.country is empty %} selected{% endif %}>Italy</option>
                <option value="Spain"{% if business.country == 'Spain' %} selected{% endif %}>Spain</option>
            </optgroup>
            <optgroup label="North America">
                <option value="United States"{% if business.country == 'United States' %} selected{% endif %}>United States</option>
                <option value="Canada"{% if business.country == 'Canada' %} selected{% endif %}>Canada</option>
                <option value="Mexico"{% if business.country == 'Mexico' %} selected{% endif %}>Mexico</option>
                <option value="Jamaica"{% if business.country == 'Jamaica' %} selected{% endif %}>Jamaica</option>
                <option value="Costa Rica"{% if business.country == 'Costa Rica' %} selected{% endif %}>Costa Rica</option>
            </optgroup>
            <optgroup label="South America">
                <option value="Brazil"{% if business.country == 'Brazil' %} selected{% endif %}>Brazil</option>
                <option value="Argentina"{% if business.country == 'Argentina' %} selected{% endif %}>Argentina</option>
                <option value="Colombia"{% if business.country == 'Colombia' %} selected{% endif %}>Colombia</option>
                <option value="Chile"{% if business.country == 'Chile' %} selected{% endif %}>Chile</option>
                <option value="Peru"{% if business.country == 'Peru' %} selected{% endif %}>Peru</option>
            </optgroup>
            <optgroup label="Oceania">
                <option value="Australia"{% if business.country == 'Australia' %} selected{% endif %}>Australia</option>
                <option value="New Zealand"{% if business.country == 'New Zealand' %} selected{% endif %}>New Zealand</option>
                <option value="Papua New Guinea"{% if business.country == 'Papua New Guinea' %} selected{% endif %}>Papua New Guinea</option>
                <option value="Fiji"{% if business.country == 'Fiji' %} selected{% endif %}>Fiji</option>
                <option value="Solomon Islands"{% if business.country == 'Solomon Islands' %} selected{% endif %}>Solomon Islands</option>
            </optgroup>
        </select>
    </div>
    <div class="col-12 mb-3">
        <label for="fulladdress" class="form-label">Indirizzo Completo *</label>
        <input type="text" class="form-control form-control-lg" autocomplete="off" id="fulladdress" name="fulladdress" placeholder="Inserisci indirizzo" value="{{ business.fulladdress }}" required>
    </div>
    <div class="col-12 mb-3">        
        <label for="address" class="form-label">Indirizzo *</label>
        <input type="text" class="form-control form-control-lg" id="address" name="address" placeholder="Inserisci indirizzo" value="{{ business.address }}" required>
    </div>
    <div class="col-md-5 mb-3">
        <label for="city" class="form-label">Città *</label>
        <input role="presentation" autocomplete="off" type="text" name="city" id="city" class="form-control form-control-lg maxlength" maxlength="100" placeholder="Seleziona città" value="{{ business.city }}" required>
    </div>
    <div class="col-md-5 mb-3">
        <label for="provinceCode" class="form-label">Provincia *</label>
        <select class="form-select form-select-lg" id="provinceCode" name="provinceCode" required aria-label="Seleziona provincia">
            <option value="">Seleziona provincia</option>
            {% for item in lookup("Province", checkPublished=false, language='false') %}
            <option value="{{ item.code }}" {{ item.code == business.provinceCode ? 'selected' : ''}}>{{ item.description }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="col-md-2">
        <label for="zip" class="form-label">CAP *</label>
        <input type="text" class="form-control form-control-lg" id="zip" name="zip" placeholder="Inserisci CAP" value="{{ business.zip }}" required>
    </div>
</div>
<!-- Map -->
<h2 class="h6 mb-2">Posizione sulla mappa</h2>
<p class="fs-sm">Sposta il marcatore per determinare la posizione esatta.</p>
<div class="ratio ratio-21x9 bg-body-tertiary border rounded overflow-hidden" data-map="{
     &quot;tileLayer&quot;: &quot;https://api.maptiler.com/maps/pastel/{z}/{x}/{y}.png?key=rqrCHwDtUZCUA2fCt3vV&quot;,
     &quot;attribution&quot;: &quot;© Maptiler © OpenStreetMap contributors&quot;,
     &quot;zoom&quot;: 14,
     &quot;tileSize&quot;: 512,
     &quot;zoomOffset&quot;: -1,
     &quot;templates&quot;: {
     &quot;marker&quot;: &quot;<div class=\&quot;map-marker\&quot;><i class=\&quot;fi-map-pin-filled text-primary fs-4\&quot;></i></div>&quot;,
     &quot;popup&quot;: &quot;<div class=\&quot;p-3\&quot; data-bs-theme=\&quot;light\&quot;><h6 class=\&quot;pb-1 mb-2\&quot;>Property location</h6><p class=\&quot;fs-sm text-body m-0\&quot;>929 Hart Street, Brooklyn, NY, 11237</p></div>&quot;
     }
     }" data-map-markers="[{&quot;coordinates&quot;: {&quot;lat&quot;: 40.719, &quot;lng&quot;: -73.994 }}]"></div>