{% extends "fe/include/base.html" %}
{% set currentPage = 'PHOTO' %}

{% set seoTitle = photo.title %}
{% set seoDescription = photo.description | removeHtmlTags | abbreviate(150) %}

{% block title %}{{ seoTitle }} | {{ varsCompanyName }}{% endblock %}

{% block canonical %}
    <meta name="robots" content="index, follow">
    <meta name="description" content="{{ seoDescription }}">    
    <link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
    <meta property="og:locale"          content="it_IT" />
    <meta property="og:url"             content="{{ publicUrl }}" />
    <meta property="og:type"            content="website" />
    <meta property="og:title"           content="{{ seoTitle }} | {{ varsCompanyName }}" />
    <meta property="og:description"     content="{{ seoDescription }}" />
    <meta property="og:image"           content="{{ routes('BE_IMAGE') }}?oid={{ photo.imageIds[0] }}" />
    <meta property="og:image:width"     content="1200" />
    <meta property="og:image:height"    content="630" />
    <meta property="og:image:alt"       content="{{ seoTitle }} | {{ varsCompanyName }}" />
{% endblock %}

{% block content %}

    <!-- Header Section -->
    <section class="home-section bg-dark-1 bg-dark-alpha-30 light-content parallax-5" style="background-image: url({{ routes('BE_IMAGE') }}?oid={{ photo.imageIds[0] }})" id="home">
        <div class="position-absolute top-0 bottom-0 start-0 end-0 bg-gradient-dark opacity-07"></div>
        <div class="container position-relative min-height-100vh d-flex align-items-end pt-100 pb-100">

            <!-- Section Content -->
            <div class="home-content text-center">
                <div class="row">

                    <!-- Page Title -->
                    <div class="col-md-8 offset-md-2">

                        <div class="mb-20">
                            <a href="{{ routes('PHOTO_COLLECTION') }}" class="btn btn-mod btn-small btn-border-w btn-circle" data-btn-animate="y"><i class="mi-arrow-left align-center size-18"></i> Torna ai progetti</a>
                        </div>

                        <h1 class="hs-title-1 mb-20">
                            <span class="wow charsAnimIn" data-splitting="chars">{{ photo.title }}</span>
                        </h1>

                        <div class="row">
                            <div class="col-md-10 offset-md-1 col-lg-8 offset-lg-2">
                                <p class="section-descr mb-0 wow fadeIn" data-wow-delay="0.2s" data-wow-duration="1.2s">
                                    {{ photo.category }}
                                </p>
                            </div>
                        </div>

                    </div>
                    <!-- End Page Title -->

                </div>                            
            </div>
            <!-- End Section Content -->

            <!-- Scroll Down -->
            <div class="local-scroll scroll-down-wrap wow fadeInUp" data-wow-offset="0">
                <a href="#start" class="scroll-down"><i class="mi-chevron-down"></i><span class="visually-hidden">Scroll to the next section</span></a>
            </div>
            <!-- End Scroll Down -->

        </div>
    </section>
    <!-- End Header Section -->                      
  
<!-- Section -->
    <section class="page-section" id="start">
        <div class="container relative">

            <div class="row mb-80 mb-sm-40">

                <!-- Project Details -->
                <div class="col-md-6">

                    <h2 class="h3 mb-20">Dettagli</h2>                               

                    <div class="row text-gray">
                        <div class="col-sm-4">
                            <b>Anno:</b>
                        </div>
                        <div class="col-sm-8">
                            {{ photo.year }}
                        </div>
                    </div>

                    <hr class="mb-20" />

                    <div class="row text-gray">
                        <div class="col-sm-4">
                            <b>Città:</b>
                        </div>
                        <div class="col-sm-8">
                            {{ photo.city }}
                        </div>
                    </div>                               

                    <hr class="mb-20" />

                    <div class="row text-gray">
                        <div class="col-sm-4">
                            <b>Prezzo:</b>
                        </div>
                        <div class="col-sm-8">
                            {{ photo.price }}
                        </div>
                    </div>

                    <hr class="mb-20" />

                </div>
                <!-- End Project Details -->

                <!-- Project Description -->
                <div class="col-md-6">

                    <h2 class="h3 mb-20">Descrizione</h2>

                    <p class="text-gray mb-0">
                        {% autoescape false %}
                        {{ photo.description }}
                        {% endautoescape %}
                    </p>

                </div>
                <!-- End Project Description -->

            </div>                        

            <div>

                {% if photo.imageIds is not empty %}

                {% for imageId in photo.imageIds %}

                <!-- Photo Item -->
                <div class="mb-30 wow fadeInUp">
                    <img src="{{ routes('BE_IMAGE') }}?oid={{ imageId }}" alt="Domus Nova" loading="lazy"/>
                </div>
                <!-- End Photo Item -->                       

                {% endfor %}

                {% endif %}

            </div>                            

        </div>
    </section>
    <!-- End Section -->


    <!-- Divider -->
    <hr class="mt-0 mb-0" />
    <!-- End Divider -->
    
    <!-- Section -->
    <section class="page-section">
        <div class="container position-relative">

            <div class="row">
                <div class="col-md-12 wow fadeInUp">                               
                    <h2 class="h3 mb-20">Altre informazioni</h2>  
                </div>
            </div>
            <div class="row">

                <!-- Project Details -->
                <div class="col-md-4 mb-sm-40 wow fadeInUp">                                                   
                                                
                        {% if photo.surfaceArea is not empty %}
                        <hr class="mb-20" />

                        <div class="row text-gray small">
                            <div class="col-sm-4">
                                <b>Superficie:</b>
                            </div>
                            <div class="col-sm-8">
                                {{ photo.surfaceArea }}
                            </div>
                        </div>                          
                        {% endif %}
                        {% if photo.energyClass is not empty %}
                        <hr class="mb-20" />

                        <div class="row text-gray small">
                            <div class="col-sm-4">
                                <b>Classe:</b>
                            </div>
                            <div class="col-sm-8">
                                {{ photo.energyClass }}
                            </div>
                        </div>                          
                        {% endif %}
                        {% if photo.bedrooms is not empty %}
                        <hr class="mb-20" />

                        <div class="row text-gray small">
                            <div class="col-sm-4">
                                <b>Camere:</b>
                            </div>
                            <div class="col-sm-8">
                                {{ photo.bedrooms }}
                            </div>
                        </div>                          
                        {% endif %}
                        {% if photo.bathrooms is not empty %}
                        <hr class="mb-20" />

                        <div class="row text-gray small">
                            <div class="col-sm-4">
                                <b>Bagni:</b>
                            </div>
                            <div class="col-sm-8">
                                {{ photo.bathrooms }}
                            </div>
                        </div>                          
                        {% endif %}                                                                                              
                    
                </div>
                <!-- End Project Details -->

                <div class="col-md-4 mb-sm-40 wow fadeInUp">                               

                    {% if photo.units is not empty %}
                    <hr class="mb-20" />

                    <div class="row text-gray small">
                        <div class="col-sm-4">
                            <b>Unità:</b>
                        </div>
                        <div class="col-sm-8">
                            {{ photo.units }}
                        </div>
                    </div>                          
                    {% endif %}
                    {% if photo.floors is not empty %}
                    <hr class="mb-20" />

                    <div class="row text-gray small">
                        <div class="col-sm-4">
                            <b>Piani:</b>
                        </div>
                        <div class="col-sm-8">
                            {{ photo.floors }}
                        </div>
                    </div>                          
                    {% endif %}
                    {% if photo.parkingSpaces is not empty %}
                    <hr class="mb-20" />

                    <div class="row text-gray small">
                        <div class="col-sm-4">
                            <b>Posti auto:</b>
                        </div>
                        <div class="col-sm-8">
                            {{ photo.parkingSpaces }}
                        </div>
                    </div>                          
                    {% endif %}
                    {% if photo.deliveryDate is not empty %}
                        <hr class="mb-20" />

                        <div class="row text-gray small">
                            <div class="col-sm-4">
                                <b>Consegna:</b>
                            </div>
                            <div class="col-sm-8">
                                {{ photo.deliveryDate }}
                            </div>
                        </div>                          
                        {% endif %}
                                            
                </div>

                <div class="col-md-4 mb-sm-40 wow fadeInUp">                               
                    
                        {% if photo.otherFeatures is not empty %}
                        <hr class="mb-20" />

                        <div class="row text-gray small">
                            <div class="col-sm-4">
                                <b>Specifiche:</b>
                            </div>
                            <div class="col-sm-8">
                                {{ photo.otherFeatures }}
                            </div>
                        </div>                          
                        {% endif %}
                        {% if photo.price is not empty %}
                        <hr class="mb-20" />
                        
                        <div class="row text-gray small">
                            <div class="col-sm-4">
                                <b>Tecnologie:</b>
                            </div>
                            <div class="col-sm-8">
                                {{ photo.technologies }}
                            </div>
                        </div> 
                        {% endif %}
                </div>
                
            </div>                        

        </div>
    </section>
    <!-- End Section -->


    <!-- Divider -->
    <hr class="mt-0 mb-0" />
    <!-- End Divider -->


    <!-- Section -->
    {% set nextPhoto = next(table="Photo", currentElement=photo.id, checkPublished=false, orderBy="creation", orderType="desc") %}
    {% if nextPhoto is not empty %}
        {% set nextSecondPhoto = next(table="Photo", currentElement=nextPhoto.id, checkPublished=false, orderBy="creation", orderType="desc") %}
    {% endif %}
    {% if nextPhoto is not empty %}
        <section class="page-section">
            <div class="container relative">

                <div class="text-center mb-60 mb-sm-40">
                    <h2 class="section-title-small">Altri progetti</h2>
                </div>

                <!-- Works Grid -->
                <ul class="works-grid work-grid-2 work-grid-gut hover-white work-grid-hover-alt">                
                        <!-- Work Item (External Page) -->
                        <li class="work-item">
                            <a href="{{ routes('PHOTO_COLLECTION') + '/' + nextPhoto.identifier }}" class="work-ext-link">
                                <div class="work-img">
                                    <div class="work-img-bg wow-p scalexIn"></div>
                                    <img src="{{ routes('BE_IMAGE') }}?oid={{ nextPhoto.imageIds[0] }}&scaleFactor=0.30" alt="Domus Nova" class="wow-p fadeIn" data-wow-delay="1s" loading="lazy"/>
                                </div>
                                <div class="work-intro">
                                    <h3 class="work-title">{{ nextPhoto.title | abbreviate(100)  }}</h3>
                                    <div class="work-descr">
                                        {{ nextPhoto.category }}
                                    </div>
                                </div>
                            </a>
                        </li>
                        {% if nextSecondPhoto is not empty %}
                            <li class="work-item">
                                <a href="{{ routes('PHOTO_COLLECTION') + '/' + nextSecondPhoto.identifier }}" class="work-ext-link">
                                    <div class="work-img">
                                        <div class="work-img-bg wow-p scalexIn"></div>
                                        <img src="{{ routes('BE_IMAGE') }}?oid={{ nextSecondPhoto.imageIds[0] }}&scaleFactor=0.30" alt="Domus Nova" class="wow-p fadeIn" data-wow-delay="1s" loading="lazy"/>
                                    </div>
                                    <div class="work-intro">
                                        <h3 class="work-title">{{ nextSecondPhoto.title | abbreviate(100)  }}</h3>
                                        <div class="work-descr">
                                            {{ nextSecondPhoto.category }}
                                        </div>
                                    </div>
                                </a>
                            </li>
                        {% endif %}
                        <!-- End Work Item -->                 
                </ul>
                <!-- End Works Grid -->

            </div>
        </section>
    {% endif %}
    <!-- End Section -->
    
    {% include "fe/include/snippets/contacts.html" %}    
    
{% endblock %}

{% block pagescripts %}
    
{% endblock %}