{% extends "fe/include/base.html" %}

{% set currentPage = 'ACCOUNT_FAVOURITES' %}
{% set seoTitle = '' %}
{% set seoDescription = '' %}

{% block title %}{{ seoTitle }}{% endblock %}

{% block canonical %}
<meta name="robots" content="index, follow">
<meta name="description" content="{{ seoDescription }}">    
<link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
<meta property="og:locale"          content="it_IT" />
<meta property="og:url"             content="{{ publicUrl }}" />
<meta property="og:type"            content="website" />
<meta property="og:title"           content="{{ seoTitle }}" />
<meta property="og:description"     content="{{ seoDescription }}" />
<meta property="og:image"           content="" />
<meta property="og:image:width"     content="1200" />
<meta property="og:image:height"    content="630" />
<meta property="og:image:alt"       content="{{ seoTitle }}" />
{% endblock %}

{% block content %}
    
    <!-- Page content -->
    <main class="content-wrapper">
        <div class="container pt-4 pt-sm-5 pb-5 mb-xxl-3">
            <div class="row pt-2 pt-sm-0 pt-lg-2 pb-2 pb-sm-3 pb-md-4 pb-lg-5">


                <!-- Sidebar navigation that turns into offcanvas on screens < 992px wide (lg breakpoint) -->
                {% include "fe/include/snippets/account-aside.html" %}

                <!-- Account favorites content -->
                <div class="col-lg-9">

                    <!-- Heading + Action buttons -->
                    <div class="d-flex align-items-center justify-content-between pb-2 pb-lg-3 mb-3">
                        <h1 class="h2 mb-0 me-3">Posti del cuore</h1>                        
                    </div>


                    <!-- Listings (Grid) -->
                    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-4 g-md-3 g-lg-4">

                        <!-- Item -->
                        <div class="col">
                            <article class="card hover-effect-opacity h-100">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="swiper z-2" data-swiper="{
                                         &quot;pagination&quot;: {
                                         &quot;el&quot;: &quot;.swiper-pagination&quot;
                                         },
                                         &quot;navigation&quot;: {
                                         &quot;prevEl&quot;: &quot;.btn-prev&quot;,
                                         &quot;nextEl&quot;: &quot;.btn-next&quot;
                                         },
                                         &quot;breakpoints&quot;: {
                                         &quot;991&quot;: {
                                         &quot;allowTouchMove&quot;: false
                                         }
                                         }
                                         }">
                                        <a class="swiper-wrapper" href="#!">
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/06.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/06.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/06.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                        </a>
                                        <div class="d-flex flex-column gap-2 align-items-start position-absolute top-0 start-0 z-1 pt-1 pt-sm-0 ps-1 ps-sm-0 mt-2 mt-sm-3 ms-2 ms-sm-3">
                                            <span class="badge text-bg-primary">New</span>
                                        </div>
                                        <div class="position-absolute top-0 end-0 z-1 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                            <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Remove from wishlist">
                                                <i class="fi-heart-filled text-danger animate-target fs-sm"></i>
                                            </button>
                                        </div>
                                        <div class="position-absolute top-50 start-0 z-1 translate-middle-y d-none d-lg-block hover-effect-target opacity-0 ms-3">
                                            <button type="button" class="btn btn-sm btn-prev btn-icon btn-light bg-light rounded-circle animate-slide-start" aria-label="Prev">
                                                <i class="fi-chevron-left fs-lg animate-target"></i>
                                            </button>
                                        </div>
                                        <div class="position-absolute top-50 end-0 z-1 translate-middle-y d-none d-lg-block hover-effect-target opacity-0 me-3">
                                            <button type="button" class="btn btn-sm btn-next btn-icon btn-light bg-light rounded-circle animate-slide-end" aria-label="Next">
                                                <i class="fi-chevron-right fs-lg animate-target"></i>
                                            </button>
                                        </div>
                                        <div class="swiper-pagination bottom-0 mb-2" data-bs-theme="light"></div>
                                    </div>
                                </div>
                                <div class="card-body p-3">
                                    <div class="pb-1 mb-2">
                                        <span class="badge text-body-emphasis bg-body-secondary">For sale</span>
                                    </div>
                                    <div class="h5 mb-2">$375,000</div>
                                    <h3 class="fs-sm fw-normal text-body mb-2">
                                        <a class="stretched-link text-body" href="#!">929 Hart St, Brooklyn, NY 11237</a>
                                    </h3>
                                    <div class="h6 fs-sm mb-0">108 sq.m</div>
                                </div>
                                <div class="card-footer d-flex gap-2 border-0 bg-transparent pt-0 pb-3 px-3 mt-n1">
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        3<i class="fi-bed-single fs-base text-secondary-emphasis"></i>
                                    </div>
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        2<i class="fi-shower fs-base text-secondary-emphasis"></i>
                                    </div>
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        1<i class="fi-car-garage fs-base text-secondary-emphasis"></i>
                                    </div>
                                </div>
                            </article>
                        </div>

                        <!-- Item -->
                        <div class="col">
                            <article class="card hover-effect-opacity h-100">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="swiper z-2" data-swiper="{
                                         &quot;pagination&quot;: {
                                         &quot;el&quot;: &quot;.swiper-pagination&quot;
                                         },
                                         &quot;navigation&quot;: {
                                         &quot;prevEl&quot;: &quot;.btn-prev&quot;,
                                         &quot;nextEl&quot;: &quot;.btn-next&quot;
                                         },
                                         &quot;breakpoints&quot;: {
                                         &quot;991&quot;: {
                                         &quot;allowTouchMove&quot;: false
                                         }
                                         }
                                         }">
                                        <a class="swiper-wrapper" href="#!">
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/03.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/03.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/03.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                        </a>
                                        <div class="d-flex flex-column gap-2 align-items-start position-absolute top-0 start-0 z-1 pt-1 pt-sm-0 ps-1 ps-sm-0 mt-2 mt-sm-3 ms-2 ms-sm-3">
                                            <span class="badge text-bg-info">Featured</span>
                                            <span class="badge text-bg-primary">New</span>
                                        </div>
                                        <div class="position-absolute top-0 end-0 z-1 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                            <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Remove from wishlist">
                                                <i class="fi-heart-filled text-danger animate-target fs-sm"></i>
                                            </button>
                                        </div>
                                        <div class="position-absolute top-50 start-0 z-1 translate-middle-y d-none d-lg-block hover-effect-target opacity-0 ms-3">
                                            <button type="button" class="btn btn-sm btn-prev btn-icon btn-light bg-light rounded-circle animate-slide-start" aria-label="Prev">
                                                <i class="fi-chevron-left fs-lg animate-target"></i>
                                            </button>
                                        </div>
                                        <div class="position-absolute top-50 end-0 z-1 translate-middle-y d-none d-lg-block hover-effect-target opacity-0 me-3">
                                            <button type="button" class="btn btn-sm btn-next btn-icon btn-light bg-light rounded-circle animate-slide-end" aria-label="Next">
                                                <i class="fi-chevron-right fs-lg animate-target"></i>
                                            </button>
                                        </div>
                                        <div class="swiper-pagination bottom-0 mb-2" data-bs-theme="light"></div>
                                    </div>
                                </div>
                                <div class="card-body p-3">
                                    <div class="pb-1 mb-2">
                                        <span class="badge text-body-emphasis bg-body-secondary">For rent</span>
                                    </div>
                                    <div class="h5 mb-2">$1,890</div>
                                    <h3 class="fs-sm fw-normal text-body mb-2">
                                        <a class="stretched-link text-body" href="#!">3811 Ditmars Blvd Astoria, NY 11105</a>
                                    </h3>
                                    <div class="h6 fs-sm mb-0">75 sq.m</div>
                                </div>
                                <div class="card-footer d-flex gap-2 border-0 bg-transparent pt-0 pb-3 px-3 mt-n1">
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        2<i class="fi-bed-single fs-base text-secondary-emphasis"></i>
                                    </div>
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        1<i class="fi-shower fs-base text-secondary-emphasis"></i>
                                    </div>
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        1<i class="fi-car-garage fs-base text-secondary-emphasis"></i>
                                    </div>
                                </div>
                            </article>
                        </div>

                        <!-- Item -->
                        <div class="col">
                            <article class="card hover-effect-opacity h-100">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="swiper z-2" data-swiper="{
                                         &quot;pagination&quot;: {
                                         &quot;el&quot;: &quot;.swiper-pagination&quot;
                                         },
                                         &quot;navigation&quot;: {
                                         &quot;prevEl&quot;: &quot;.btn-prev&quot;,
                                         &quot;nextEl&quot;: &quot;.btn-next&quot;
                                         },
                                         &quot;breakpoints&quot;: {
                                         &quot;991&quot;: {
                                         &quot;allowTouchMove&quot;: false
                                         }
                                         }
                                         }">
                                        <a class="swiper-wrapper" href="#!">
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/05.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/05.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/05.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                        </a>
                                        <div class="d-flex flex-column gap-2 align-items-start position-absolute top-0 start-0 z-1 pt-1 pt-sm-0 ps-1 ps-sm-0 mt-2 mt-sm-3 ms-2 ms-sm-3">
                                            <span class="badge text-bg-info d-inline-flex align-items-center">
                                                Verified
                                                <i class="fi-shield ms-1"></i>
                                            </span>
                                        </div>
                                        <div class="position-absolute top-0 end-0 z-1 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                            <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Remove from wishlist">
                                                <i class="fi-heart-filled text-danger animate-target fs-sm"></i>
                                            </button>
                                        </div>
                                        <div class="position-absolute top-50 start-0 z-1 translate-middle-y d-none d-lg-block hover-effect-target opacity-0 ms-3">
                                            <button type="button" class="btn btn-sm btn-prev btn-icon btn-light bg-light rounded-circle animate-slide-start" aria-label="Prev">
                                                <i class="fi-chevron-left fs-lg animate-target"></i>
                                            </button>
                                        </div>
                                        <div class="position-absolute top-50 end-0 z-1 translate-middle-y d-none d-lg-block hover-effect-target opacity-0 me-3">
                                            <button type="button" class="btn btn-sm btn-next btn-icon btn-light bg-light rounded-circle animate-slide-end" aria-label="Next">
                                                <i class="fi-chevron-right fs-lg animate-target"></i>
                                            </button>
                                        </div>
                                        <div class="swiper-pagination bottom-0 mb-2" data-bs-theme="light"></div>
                                    </div>
                                </div>
                                <div class="card-body p-3">
                                    <div class="pb-1 mb-2">
                                        <span class="badge text-body-emphasis bg-body-secondary">For rent</span>
                                    </div>
                                    <div class="h5 mb-2">$1,250</div>
                                    <h3 class="fs-sm fw-normal text-body mb-2">
                                        <a class="stretched-link text-body" href="#!">444 Park Ave, Brooklyn, NY 11205</a>
                                    </h3>
                                    <div class="h6 fs-sm mb-0">54 sq.m</div>
                                </div>
                                <div class="card-footer d-flex gap-2 border-0 bg-transparent pt-0 pb-3 px-3 mt-n1">
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        1<i class="fi-bed-single fs-base text-secondary-emphasis"></i>
                                    </div>
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        1<i class="fi-shower fs-base text-secondary-emphasis"></i>
                                    </div>
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        0<i class="fi-car-garage fs-base text-secondary-emphasis"></i>
                                    </div>
                                </div>
                            </article>
                        </div>

                        <!-- Item -->
                        <div class="col">
                            <article class="card hover-effect-opacity h-100">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="swiper z-2" data-swiper="{
                                         &quot;pagination&quot;: {
                                         &quot;el&quot;: &quot;.swiper-pagination&quot;
                                         },
                                         &quot;navigation&quot;: {
                                         &quot;prevEl&quot;: &quot;.btn-prev&quot;,
                                         &quot;nextEl&quot;: &quot;.btn-next&quot;
                                         },
                                         &quot;breakpoints&quot;: {
                                         &quot;991&quot;: {
                                         &quot;allowTouchMove&quot;: false
                                         }
                                         }
                                         }">
                                        <a class="swiper-wrapper" href="#!">
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/01.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/01.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/01.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                        </a>
                                        <div class="position-absolute top-0 end-0 z-1 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                            <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Remove from wishlist">
                                                <i class="fi-heart-filled text-danger animate-target fs-sm"></i>
                                            </button>
                                        </div>
                                        <div class="position-absolute top-50 start-0 z-1 translate-middle-y d-none d-lg-block hover-effect-target opacity-0 ms-3">
                                            <button type="button" class="btn btn-sm btn-prev btn-icon btn-light bg-light rounded-circle animate-slide-start" aria-label="Prev">
                                                <i class="fi-chevron-left fs-lg animate-target"></i>
                                            </button>
                                        </div>
                                        <div class="position-absolute top-50 end-0 z-1 translate-middle-y d-none d-lg-block hover-effect-target opacity-0 me-3">
                                            <button type="button" class="btn btn-sm btn-next btn-icon btn-light bg-light rounded-circle animate-slide-end" aria-label="Next">
                                                <i class="fi-chevron-right fs-lg animate-target"></i>
                                            </button>
                                        </div>
                                        <div class="swiper-pagination bottom-0 mb-2" data-bs-theme="light"></div>
                                    </div>
                                </div>
                                <div class="card-body p-3">
                                    <div class="pb-1 mb-2">
                                        <span class="badge text-body-emphasis bg-body-secondary">For rent</span>
                                    </div>
                                    <div class="h5 mb-2">$1,620</div>
                                    <h3 class="fs-sm fw-normal text-body mb-2">
                                        <a class="stretched-link text-body" href="#!">40 S 9th St, Brooklyn, NY 11249</a>
                                    </h3>
                                    <div class="h6 fs-sm mb-0">65 sq.m</div>
                                </div>
                                <div class="card-footer d-flex gap-2 border-0 bg-transparent pt-0 pb-3 px-3 mt-n1">
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        2<i class="fi-bed-single fs-base text-secondary-emphasis"></i>
                                    </div>
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        1<i class="fi-shower fs-base text-secondary-emphasis"></i>
                                    </div>
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        1<i class="fi-car-garage fs-base text-secondary-emphasis"></i>
                                    </div>
                                </div>
                            </article>
                        </div>

                        <!-- Item -->
                        <div class="col">
                            <article class="card hover-effect-opacity h-100">
                                <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
                                    <div class="swiper z-2" data-swiper="{
                                         &quot;pagination&quot;: {
                                         &quot;el&quot;: &quot;.swiper-pagination&quot;
                                         },
                                         &quot;navigation&quot;: {
                                         &quot;prevEl&quot;: &quot;.btn-prev&quot;,
                                         &quot;nextEl&quot;: &quot;.btn-next&quot;
                                         },
                                         &quot;breakpoints&quot;: {
                                         &quot;991&quot;: {
                                         &quot;allowTouchMove&quot;: false
                                         }
                                         }
                                         }">
                                        <a class="swiper-wrapper" href="#!">
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/04.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/04.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                            <div class="swiper-slide">
                                                <div class="ratio d-block" style="--fn-aspect-ratio: calc(248 / 362 * 100%)">
                                                    <img src="{{ contextPath }}/fe/img/listings/real-estate/04.jpg" alt="Image">
                                                    <span class="position-absolute top-0 start-0 w-100 h-100 z-1" style="background: linear-gradient(180deg, rgba(0,0,0, 0) 0%, rgba(0,0,0, .11) 100%)"></span>
                                                </div>
                                            </div>
                                        </a>
                                        <div class="position-absolute top-0 end-0 z-1 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                                            <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Remove from wishlist">
                                                <i class="fi-heart-filled text-danger animate-target fs-sm"></i>
                                            </button>
                                        </div>
                                        <div class="position-absolute top-50 start-0 z-1 translate-middle-y d-none d-lg-block hover-effect-target opacity-0 ms-3">
                                            <button type="button" class="btn btn-sm btn-prev btn-icon btn-light bg-light rounded-circle animate-slide-start" aria-label="Prev">
                                                <i class="fi-chevron-left fs-lg animate-target"></i>
                                            </button>
                                        </div>
                                        <div class="position-absolute top-50 end-0 z-1 translate-middle-y d-none d-lg-block hover-effect-target opacity-0 me-3">
                                            <button type="button" class="btn btn-sm btn-next btn-icon btn-light bg-light rounded-circle animate-slide-end" aria-label="Next">
                                                <i class="fi-chevron-right fs-lg animate-target"></i>
                                            </button>
                                        </div>
                                        <div class="swiper-pagination bottom-0 mb-2" data-bs-theme="light"></div>
                                    </div>
                                </div>
                                <div class="card-body p-3">
                                    <div class="pb-1 mb-2">
                                        <span class="badge text-body-emphasis bg-body-secondary">For rent</span>
                                    </div>
                                    <div class="h5 mb-2">$1,170</div>
                                    <h3 class="fs-sm fw-normal text-body mb-2">
                                        <a class="stretched-link text-body" href="#!">67-04 Myrtle Ave Glendale, NY 11385</a>
                                    </h3>
                                    <div class="h6 fs-sm mb-0">42 sq.m</div>
                                </div>
                                <div class="card-footer d-flex gap-2 border-0 bg-transparent pt-0 pb-3 px-3 mt-n1">
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        1<i class="fi-bed-single fs-base text-secondary-emphasis"></i>
                                    </div>
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        1<i class="fi-shower fs-base text-secondary-emphasis"></i>
                                    </div>
                                    <div class="d-flex align-items-center fs-sm gap-1 me-1">
                                        0<i class="fi-car-garage fs-base text-secondary-emphasis"></i>
                                    </div>
                                </div>
                            </article>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

{% endblock %}