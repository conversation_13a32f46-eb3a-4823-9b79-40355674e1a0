{% extends "fe/include/base.html" %}

{% set currentPage = 'ACCOUNT_BUSINESS_EDIT' %}
{% set seoTitle = '' %}
{% set seoDescription = '' %}

{% block title %}{{ seoTitle }}{% endblock %}

{% block canonical %}
<meta name="robots" content="index, follow">
<meta name="description" content="{{ seoDescription }}">    
<link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
<meta property="og:locale"          content="it_IT" />
<meta property="og:url"             content="{{ publicUrl }}" />
<meta property="og:type"            content="website" />
<meta property="og:title"           content="{{ seoTitle }}" />
<meta property="og:description"     content="{{ seoDescription }}" />
<meta property="og:image"           content="" />
<meta property="og:image:width"     content="1200" />
<meta property="og:image:height"    content="630" />
<meta property="og:image:alt"       content="{{ seoTitle }}" />
{% endblock %}

{% block pagecss %}    
    <link rel="stylesheet" href="{{ contextPath }}/fe/vendor/leaflet/leaflet.css">
    <link rel="stylesheet" href="{{ contextPath }}/fe/vendor/vanilla-steps/vanilla-steps.css">
    <link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet" type="text/css" media="all">
    <link href="https://www.siteria.it/libs/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">
{% endblock %}

{% block content %}

    <!-- Page content -->
    <main class="content-wrapper">
        <div class="container pt-3 pt-sm-4 pt-md-5 pb-5">
            <div class="row pt-lg-2 pt-xl-3 pb-1 pb-sm-2 pb-md-3 pb-lg-4 pb-xl-5 justify-content-center">

                {% if not showDraftChoice %}
                    <!-- Sidebar navigation -->
                    {% include "fe/include/snippets/account-business-new-aside.html" %}
                {% endif %}
                
                <!-- Content area -->
                <div class="col-lg-9 col-xl-9">
                    
                    {% if showDraftChoice %}
                        <!-- Sezione Scelta Bozza -->
                        <div class="card border-0 shadow-sm">
                            <div class="card-body p-4">
                                <h2 class="h4 mb-3">🐠 Ehi, hai lasciato qualcosa a galla!</h2>
                                <p class="text-muted mb-4">C'è una bozza della tua attività che ti aspetta. Vuoi riprenderla da dove l'avevi lasciata o preferisci tuffarti in qualcosa di completamente nuovo?</p>

                                {% if draftBusiness is not empty %}
                                <div class="alert alert-info mb-4">
                                    <h6 class="alert-heading">🏊‍♂️ Dettagli della tua Attività</h6>
                                    <p class="mb-1"><strong>Nome Attività:</strong>
                                        {% if draftBusiness.fullname is not empty %}{{ draftBusiness.fullname }}{% else %}🌊 Non ancora impostato{% endif %}
                                    </p>
                                    <p class="mb-1"><strong>Creata il:</strong>
                                        {% if draftBusiness.creation is not empty %}{{ draftBusiness.creation|date('dd/MM/yyyy HH:mm') }}{% else %}Sconosciuto{% endif %}
                                    </p>
                                    <p class="mb-0"><strong>Ultimo Aggiornamento:</strong>
                                        {% if draftBusiness.lastUpdate is not empty %}{{ draftBusiness.lastUpdate|date('dd/MM/yyyy HH:mm') }}{% else %}Sconosciuto{% endif %}
                                    </p>
                                </div>
                                {% endif %}

                                <div class="row g-3">
                                    <div class="col-12 col-md-6">
                                        <form method="post" action="{{ routes('ACCOUNT_BUSINESS_DRAFT_CHOICE') }}">
                                            <input type="hidden" name="choice" value="start_fresh">
                                            <input type="hidden" name="draftId" value="{{ draftBusiness.id }}">
                                            <button type="submit" class="btn btn-outline-secondary btn-lg w-100">
                                                <i class="fi-plus me-2"></i>
                                                Ricomincia da Capo
                                            </button>
                                        </form>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <form method="post" action="{{ routes('ACCOUNT_BUSINESS_DRAFT_CHOICE') }}">
                                            <input type="hidden" name="choice" value="use_draft">
                                            <input type="hidden" name="draftId" value="{{ draftBusiness.id }}">
                                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                                <i class="fi-edit me-2"></i>
                                                Continua con la Bozza
                                            </button>
                                        </form>
                                    </div>
                                </div>

                                <div class="text-center mt-3">
                                    <small class="text-muted">
                                        ⚠️ Nota: Ricominciare da capo eliminerà permanentemente la bozza esistente dalle tue acque.
                                    </small>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <!-- Business Form Section -->
                        <div class="step-app" id="demo">
                            <form class="step-content" method="post" action="{{ routes('ACCOUNT_BUSINESS_EDIT_SAVE') }}" id="form-business-edit">
                                
                                <!-- Hidden field for business ID -->
                                {% if business is not empty and business.id is not empty %}
                                    <input type="hidden" name="oid" value="{{ business.id }}">
                                {% endif %}

                                <div class="step-tab-panel" data-step="step1">
                                    {% include "fe/include/snippets/account-business-new-step-1.html" %}        
                                </div>
                                <div class="step-tab-panel" data-step="step2">
                                    {% include "fe/include/snippets/account-business-new-step-2.html" %}        
                                </div>
                                <div class="step-tab-panel" data-step="step3">
                                    {% include "fe/include/snippets/account-business-new-step-3.html" %}        
                                </div>
                                <div class="step-tab-panel" data-step="step4">
                                    {% include "fe/include/snippets/account-business-new-step-4.html" %}        
                                </div>
                                <div class="step-tab-panel" data-step="step5">
                                    {% include "fe/include/snippets/account-business-new-step-5.html" %}        
                                </div>                            

                            </form>
                                                                           
                        </div>
                    {% endif %}
                    
                </div>
            </div>
        </div>
    </main>
    
    {% if not showDraftChoice %}
        {% include "fe/include/snippets/account-business-new-footer.html" %}
    {% endif %}
        
    
{% endblock %}

{% block pagescripts %}
    <script src="https://siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
    <script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBzbbAKQoJEad0bNS1qFMksTDfT60qJUnU&libraries=places&language=it-IT&callback=initAutocomplete"></script>
    {% if showDraftChoice is empty %}
    <script src="{{ contextPath }}/fe/vendor/vanilla-steps/vanilla-steps.js?{{ buildNumber }}"></script>
    <script src="{{ contextPath }}/fe/js/pages/account-business-edit.js?{{ buildNumber }}"></script>
    {% endif %}
{% endblock %}