<input type="hidden" id="totalCount" value="{{ totalCount }}">
{% for business in businesses %}
<!-- Listing -->
<div class="col">
    <article class="card h-100 hover-effect-scale hover-effect-opacity bg-transparent">
        <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">
            <div class="ratio hover-effect-target"
                 style="--fn-aspect-ratio: calc(198 / 304 * 100%)">
                <img src="{{ routes('BE_IMAGE') }}?oid={{ business.imageIds[0] }}" alt="{{ business.fullname }}">
            </div>
            <div class="position-absolute top-0 end-0 z-2 hover-effect-target opacity-0 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">
                <button type="button"
                        class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse"
                        aria-label="Add to wishlist">
                    <i class="fi-heart animate-target fs-sm"></i>
                </button>
            </div>
        </div>
        <div class="card-body pt-3 pb-2 px-3">
            <span class="badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2">{{ business.category }}</span>
            <h3 class="h5 pt-1 mb-2">
                <a class="hover-effect-underline stretched-link" href="{{ business.url }}">{{ business.fullname }}</a>
            </h3>
            <p class="fs-sm mb-0">{{ business.description }}</p>
        </div>
        <div class="card-footer bg-transparent border-0 pt-0 pb-3 px-3">
            <div class="d-flex align-items-center gap-3">
                <div class="d-flex align-items-center gap-1">
                    <i class="fi-star-filled text-warning"></i>
                    <span class="fs-sm text-secondary-emphasis">{{ business.rating }}</span>
                    <span class="fs-xs text-body-secondary align-self-end">({{ business.reviewCount }})</span>
                </div>
                <div class="d-flex align-items-center gap-1 min-w-0 fs-sm">
                    <i class="fi-map-pin"></i>
                    <span class="text-truncate">{{ business.distance }} km from center</span>
                </div>
            </div>
            <div class="h6 pt-3 mb-0">${{ business.price }}</div>
        </div>
    </article>
</div>
{% endfor %}

<!--&lt;!&ndash; Listing &ndash;&gt;-->
<!--<div class="col">-->
<!--    <article class="card h-100 hover-effect-scale hover-effect-opacity bg-transparent">-->
<!--        <div class="card-img-top position-relative bg-body-tertiary overflow-hidden">-->
<!--            <div class="ratio hover-effect-target" style="&#45;&#45;fn-aspect-ratio: calc(198 / 304 * 100%)">-->
<!--                <img src="assets/img/listings/city-guide/v1/02.jpg" alt="Image">-->
<!--            </div>-->
<!--            <div class="position-absolute top-0 end-0 z-2 hover-effect-target opacity-0 pt-1 pt-sm-0 pe-1 pe-sm-0 mt-2 mt-sm-3 me-2 me-sm-3">-->
<!--                <button type="button" class="btn btn-sm btn-icon btn-light bg-light border-0 rounded-circle animate-pulse" aria-label="Add to wishlist">-->
<!--                    <i class="fi-heart animate-target fs-sm"></i>-->
<!--                </button>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="card-body pt-3 pb-2 px-3">-->
<!--            <span class="badge text-body-emphasis bg-secondary-subtle text-decoration-none mb-2">Entertainment</span>-->
<!--            <h3 class="h5 pt-1 mb-2">-->
<!--                <a class="hover-effect-underline stretched-link" href="single-entry-city-guide.html">Mountain Lake Tour</a>-->
<!--            </h3>-->
<!--            <p class="fs-sm mb-0">Enjoy breathtaking views, fresh air, and a peaceful escape into the wilderness.</p>-->
<!--        </div>-->
<!--        <div class="card-footer bg-transparent border-0 pt-0 pb-3 px-3">-->
<!--            <div class="d-flex align-items-center gap-3">-->
<!--                <div class="d-flex align-items-center gap-1">-->
<!--                    <i class="fi-star-filled text-warning"></i>-->
<!--                    <span class="fs-sm text-secondary-emphasis">4.5</span>-->
<!--                    <span class="fs-xs text-body-secondary align-self-end">(214)</span>-->
<!--                </div>-->
<!--                <div class="d-flex align-items-center gap-1 min-w-0 fs-sm">-->
<!--                    <i class="fi-map-pin"></i>-->
<!--                    <span class="text-truncate">13 km from center</span>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="h6 pt-3 mb-0">$60</div>-->
<!--        </div>-->
<!--    </article>-->
<!--</div>-->