{% extends "fe/include/base.html" %}

{% set currentPage = 'ACCOUNT_INFO' %}
{% set seoTitle = '' %}
{% set seoDescription = '' %}

{% block title %}{{ seoTitle }}{% endblock %}

{% block canonical %}
<meta name="robots" content="index, follow">
<meta name="description" content="{{ seoDescription }}">    
<link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
<meta property="og:locale"          content="it_IT" />
<meta property="og:url"             content="{{ publicUrl }}" />
<meta property="og:type"            content="website" />
<meta property="og:title"           content="{{ seoTitle }}" />
<meta property="og:description"     content="{{ seoDescription }}" />
<meta property="og:image"           content="" />
<meta property="og:image:width"     content="1200" />
<meta property="og:image:height"    content="630" />
<meta property="og:image:alt"       content="{{ seoTitle }}" />
{% endblock %}

{% block pagecss %}
    <link rel="stylesheet" href="{{ contextPath }}/fe/vendor/choices.js/choices.min.css">
    <link rel="stylesheet" href="{{ contextPath }}/fe/vendor/flatpickr/flatpickr.min.css">    
{% endblock %}

{% block content %}

    <!-- Page content -->
    <main class="content-wrapper">
        <div class="container pt-4 pt-sm-5 pb-5 mb-xxl-3">
            <div class="row pt-2 pt-sm-0 pt-lg-2 pb-2 pb-sm-3 pb-md-4 pb-lg-5">


                <!-- Sidebar navigation that turns into offcanvas on screens < 992px wide (lg breakpoint) -->                
                {% include "fe/include/snippets/account-aside.html" %}

                <!-- Account settings content -->
                <div class="col-lg-9">
                    <h1 class="h2 pb-2 pb-lg-3">Il mio profilo</h1>

                    <!-- Nav pills -->
                    <div class="nav overflow-x-auto mb-3">
                        <ul class="nav nav-pills flex-nowrap gap-2 pb-2 mb-1" role="tablist">
                            <li class="nav-item me-1" role="presentation">
                                <button type="button" class="nav-link text-nowrap active" id="personal-info-tab" data-bs-toggle="pill" data-bs-target="#personal-info" role="tab" aria-controls="personal-info" aria-selected="true">
                                    Dati personali
                                </button>
                            </li>
                            <li class="nav-item me-1" role="presentation">
                                <button class="nav-link text-nowrap" id="security-tab" data-bs-toggle="pill" data-bs-target="#security" type="button" role="tab" aria-controls="security" aria-selected="false">
                                    Password e sicurezza
                                </button>
                            </li>
                            {# FUTURE DEV
                            <li class="nav-item" role="presentation">
                                <button class="nav-link text-nowrap" id="notifications-tab" data-bs-toggle="pill" data-bs-target="#notifications" type="button" role="tab" aria-controls="notifications" aria-selected="false">
                                    Notifiche
                                </button>
                            </li>
                            #}
                        </ul>
                    </div>

                    <div class="tab-content">

                        <!-- Personal info tab -->
                        <div class="tab-pane fade show active" id="personal-info" role="tabpanel" aria-labelledby="personal-info-tab">
                            <div class="vstack gap-4">

                                <!-- Profile completeness info card -->
                                <div class="card bg-warning-subtle border-0 mb-2">
                                    <div class="card-body d-flex align-items-center">
                                        <div class="circular-progress text-warning flex-shrink-0 ms-n2 ms-sm-0" role="progressbar" style="--fn-progress: 65" aria-label="Avanzamento profilo" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100">
                                            <svg class="progress-circle">
                                            <circle class="progress-background d-none-dark" r="0" style="stroke: #fff"></circle>
                                            <circle class="progress-background d-none d-block-dark" r="0" style="stroke: rgba(255,255,255, .1)"></circle>
                                            <circle class="progress-bar" r="0"></circle>
                                            </svg>
                                            <h5 class="position-absolute top-50 start-50 translate-middle text-center mb-0">65%</h5>
                                        </div>
                                        <div class="ps-3 ps-sm-4">
                                            <h3 class="h6 pb-1 mb-2">Completa il tuo profilo</h3>
                                            <ul class="list-unstyled flex-row flex-wrap fs-sm mb-0">
                                                <li class="d-flex me-3">
                                                    <i class="fi-plus fs-base me-2" style="margin-top: .1875rem"></i>
                                                    Aggiungi le lingue che parli
                                                </li>
                                                <li class="d-flex me-3">
                                                    <i class="fi-plus fs-base me-2" style="margin-top: .1875rem"></i>
                                                    Verifica il tuo indirizzo email
                                                </li>
                                                <li class="d-flex">
                                                    <i class="fi-plus fs-base me-2" style="margin-top: .1875rem"></i>
                                                    Aggiungi la data di nascita
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>


                                <!-- Profile picture (Avatar) -->
                                <form action="{{ routes('ACCOUNT_IMAGE_SAVE') }}" method="POST" id="form-account-image">
                                    <div class="d-flex align-items-start align-items-sm-center mb-2">
                                        <div class="hover-effect-opacity overflow-hidden" style="width: 124px">
                                            <!--<img src="{{ contextPath }}/fe/img/account/avatar-lg.jpg" alt="Avatar">-->
                                            <input id="imageId" name="imageId" type="file" class="filepond">
                                            <!--<div class="hover-effect-target position-absolute top-0 start-0 d-flex align-items-center justify-content-center w-100 h-100 opacity-0">
                                                <button type="button" class="btn btn-icon btn-sm btn-light position-relative z-2" aria-label="Rimuovi">
                                                    <i class="fi-trash fs-base"></i>
                                                </button>
                                                <span class="position-absolute top-0 start-0 w-100 h-100 bg-black bg-opacity-25 z-1"></span>
                                            </div>-->
                                        </div>
                                        <div class="ps-3 ps-sm-4">
                                            <p class="fs-sm" style="max-width: 440px">
                                                La tua foto profilo sarà visibile sul tuo profilo e nell'elenco della
                                                directory. Tipi di immagini ammessi PNG o JPG.
                                            </p>                                            
                                        </div>
                                    </div>
                                </form>


                                <!-- Settings form -->
                                <form class="needs-validation" novalidate="" action="{{ routes('ACCOUNT_INFO_SAVE') }}" method="POST" id="form-account-edit">
                                    <div class="row row-cols-1 row-cols-sm-2 g-4 mb-4">
                                        <div class="col position-relative">
                                            <label for="name" class="form-label fs-base">Nome *</label>
                                            <input type="text" class="form-control form-control-lg" name="name" id="name" required="" placeholder="Nome" value="{{ user.name }}" maxlength="80">
                                            <div class="invalid-tooltip bg-transparent p-0">Inserisci il tuo nome</div>
                                        </div>
                                        <div class="col position-relative">
                                            <label for="lastname" class="form-label fs-base">Cognome *</label>
                                            <input type="text" class="form-control form-control-lg" name="lastname" id="lastname" required="" placeholder="Cognome" value="{{ user.lastname }}" maxlength="80">
                                            <div class="invalid-tooltip bg-transparent p-0">Inserisci il tuo cognome</div>
                                        </div>
                                        <div class="col position-relative">
                                            <label for="email" class="form-label d-flex align-items-center fs-base">Indirizzo email * <span class="badge text-danger bg-danger-subtle ms-2">Verifica email</span></label>
                                            <input type="email" class="form-control form-control-lg" name="email" id="email" required="" placeholder="Email" value="{{ user.email }}" maxlength="120">
                                            <div class="invalid-tooltip bg-transparent p-0">Inserisci un indirizzo email valido</div>
                                        </div>
                                        <div class="col position-relative">
                                            <label for="phoneNumber" class="form-label d-flex align-items-center fs-base">Numero di telefono</label>
                                            <input type="tel" class="form-control form-control-lg" name="phoneNumber" id="phoneNumber" placeholder="Numero di telefono" value="{{ user.phoneNumber }}" maxlength="20">
                                            <div class="invalid-tooltip bg-transparent p-0">Inserisci un numero di telefono valido</div>
                                        </div>
                                        <div class="col">
                                            <label class="form-label fs-base">Lingue parlate</label>
                                            <select class="form-select form-select-lg" name="languages" id="languages" data-select="" multiple="" aria-label="Languages select">
                                                <option value="">Seleziona lingua</option>
                                                <option value="Italian" {{ user.languages is not empty and user.languages contains 'Italian' ? 'selected' : '' }}>Italiano</option>
                                                <option value="English" {{ user.languages is not empty and user.languages contains 'English' ? 'selected' : '' }}>Inglese</option>
                                                <option value="Spanish" {{ user.languages is not empty and user.languages contains 'Spanish' ? 'selected' : '' }}>Spagnolo</option>
                                                <option value="French" {{ user.languages is not empty and user.languages contains 'French' ? 'selected' : '' }}>Francese</option>
                                                <option value="German" {{ user.languages is not empty and user.languages contains 'German' ? 'selected' : '' }}>Tedesco</option>
                                            </select>
                                        </div>
                                        <div class="col">
                                            <label for="birthDate" class="form-label fs-base">Data di nascita</label>
                                            <div class="position-relative">
                                                <input type="text" class="form-control form-control-lg form-icon-end"
                                                       name="birthDate" id="birthDate"
                                                       data-input-format='{"date": true, "delimiter": "/", "datePattern": ["d", "m", "Y"]}'
                                                       data-datepicker='{"dateFormat": "d/m/Y", "locale": "it", "maxDate": "16/06/2011", "minDate": "01/01/1924", "allowInput": true}'
                                                       placeholder="gg/mm/aaaa" required
                                                       value="{{ user.birthDate | date('d/m/Y') }}">
                                                <i class="fi-calendar fs-lg position-absolute top-50 end-0 translate-middle-y me-3"></i>
                                                <div class="invalid-tooltip bg-transparent p-0">Inserisci data nascita</div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--<div class="position-relative mb-4">
                                        <label for="address" class="form-label fs-base">Indirizzo *</label>
                                        <input type="text" class="form-control form-control-lg" id="address" required="">
                                        <div class="invalid-tooltip bg-transparent p-0">Inserisci il tuo indirizzo</div>
                                    </div>-->
                                    <div class="pb-2 mb-4">
                                        <label for="informations" class="form-label fs-base">Informazioni su di te</label>
                                        <textarea class="form-control form-control-lg bg-transparent" name="informations" id="informations" rows="6">{{ user.informations }}</textarea>
                                    </div>
                                    <div class="d-flex gap-3">
                                        <a class="btn btn-lg btn-secondary" href="{{ routes('ACCOUNT_INFO') }}">Annulla</a>
                                        <button type="submit" class="btn btn-lg btn-primary">Salva modifiche</button>
                                    </div>
                                </form>

                            </div>
                        </div>


                        <!-- Password and security tab -->
                        <div class="tab-pane fade" id="security" role="tabpanel" aria-labelledby="security-tab">
                            <p class="mb-sm-4">Il tuo indirizzo email attuale è <span class="fw-medium text-primary">{{ user.email }}</span></p>

                            <!-- Change password form -->
                            <form class="needs-validation" novalidate="" action="{{ routes('ACCOUNT_PASSWORD_SAVE') }}" method="POST" id="form-account-password-edit">
                                <div class="row row-cols-1 row-cols-sm-2 g-4 mb-4">
                                    <div class="col">
                                        <label for="currentPassword" class="form-label fs-base">Password attuale</label>
                                        <div class="password-toggle">
                                            <input type="password" class="form-control form-control-lg" name="currentPassword" id="currentPassword" required="">
                                            <div class="invalid-tooltip bg-transparent p-0">Password errata. Riprova.</div>
                                            <label class="password-toggle-button" aria-label="Mostra/nascondi password">
                                                <input type="checkbox" class="btn-check">
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row row-cols-1 row-cols-sm-2 g-4 mb-4">
                                    <div class="col">
                                        <label for="newPassword" class="form-label fs-base">Nuova password <span class="fs-sm fw-normal text-body-secondary">(Min 8 caratteri)</span></label>
                                        <div class="password-toggle">
                                            <input type="password" class="form-control form-control-lg" minlength="8" name="newPassword" id="newPassword" required="">
                                            <div class="invalid-tooltip bg-transparent p-0">Assicurati che la password sia lunga almeno 8 caratteri.</div>
                                            <label class="password-toggle-button" aria-label="Mostra/nascondi password">
                                                <input type="checkbox" class="btn-check">
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <label for="newPasswordConfirmation" class="form-label fs-base">Conferma nuova password</label>
                                        <div class="password-toggle">
                                            <input type="password" class="form-control form-control-lg" minlength="8" name="newPasswordConfirmation" id="newPasswordConfirmation" required="">
                                            <div class="invalid-tooltip bg-transparent p-0">Le password non corrispondono.</div>
                                            <label class="password-toggle-button" aria-label="Mostra/nascondi password">
                                                <input type="checkbox" class="btn-check">
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex gap-3">
                                    <button type="reset" class="btn btn-lg btn-secondary">Annulla</button>
                                    <button type="submit" class="btn btn-lg btn-primary">Aggiorna password</button>
                                </div>
                            </form>

                            <!-- Delete account -->
                            <form action="{{ routes('ACCOUNT_DELETE') }}" method="POST" id="form-account-delete">
                                <h3 class="h4 pt-5 mt-n2 mt-md-0 mt-lg-2 mt-xl-3 mb-sm-4">Elimina account</h3>
                                <p class="fs-sm mb-sm-4">Quando elimini il tuo account, il tuo profilo pubblico verrà
                                    disattivato immediatamente. Se cambi idea entro 14 giorni, accedi con la tua email e
                                    password e ti invieremo un link per riattivare l'account.</p>
                                <div class="form-check mb-4">
                                    <input type="checkbox" class="form-check-input" name="confirmDeletion"
                                           id="confirmDeletion" required>
                                    <label for="confirmDeletion" class="form-check-label">Sì, voglio eliminare il mio
                                        account</label>
                                </div>
                                <button type="submit" class="btn btn-lg btn-outline-danger">Elimina account</button>
                            </form>
                        </div>

                        {# FUTURE DEV
                        <!-- Notification settings tab -->
                        <div class="tab-pane fade" id="notifications" role="tabpanel" aria-labelledby="notifications-tab">

                            <!-- Item -->
                            <div class="d-sm-flex align-items-center justify-content-between border-bottom pb-4">
                                <div class="me-4 mb-md-2">
                                    <h3 class="h6 mb-2">New rental alerts</h3>
                                    <p class="fs-sm pb-1 pb-sm-0 mb-sm-0">New rentals that match your <a class="text-body" href="account-favorites.html">Favorites</a></p>
                                </div>
                                <div class="d-flex gap-4 gap-xl-5 mb-md-2">
                                    <div class="form-check form-switch d-flex align-items-center ps-0 mb-0">
                                        <label for="email-1" class="form-check-label">Email</label>
                                        <input type="checkbox" class="form-check-input ms-2" role="switch" id="email-1" checked="">
                                    </div>
                                    <div class="form-check form-switch d-flex align-items-center ps-0 mb-0">
                                        <label for="phone-1" class="form-check-label mb-1">Phone</label>
                                        <input type="checkbox" class="form-check-input ms-2" role="switch" id="phone-1">
                                    </div>
                                </div>
                            </div>

                            <!-- Item -->
                            <div class="d-sm-flex align-items-center justify-content-between border-bottom py-4">
                                <div class="me-4 my-md-2">
                                    <h3 class="h6 mb-2">Rental status updates</h3>
                                    <p class="fs-sm pb-1 pb-sm-0 mb-sm-0">Updates like price changes and off-market status on your <a class="text-body" href="account-favorites.html">Favorites</a></p>
                                </div>
                                <div class="d-flex gap-4 gap-xl-5 my-md-2">
                                    <div class="form-check form-switch d-flex align-items-center ps-0 mb-0">
                                        <label for="email-2" class="form-check-label">Email</label>
                                        <input type="checkbox" class="form-check-input ms-2" role="switch" id="email-2" checked="">
                                    </div>
                                    <div class="form-check form-switch d-flex align-items-center ps-0 mb-0">
                                        <label for="phone-2" class="form-check-label mb-1">Phone</label>
                                        <input type="checkbox" class="form-check-input ms-2" role="switch" id="phone-2">
                                    </div>
                                </div>
                            </div>

                            <!-- Item -->
                            <div class="d-sm-flex align-items-center justify-content-between border-bottom py-4">
                                <div class="me-4 my-md-2">
                                    <h3 class="h6 mb-2">Finder recommendations</h3>
                                    <p class="fs-sm pb-1 pb-sm-0 mb-sm-0">Rentals we think you'll like. These recommendations may be slightly outside your search criteria</p>
                                </div>
                                <div class="d-flex gap-4 gap-xl-5 my-md-2">
                                    <div class="form-check form-switch d-flex align-items-center ps-0 mb-0">
                                        <label for="email-3" class="form-check-label">Email</label>
                                        <input type="checkbox" class="form-check-input ms-2" role="switch" id="email-3">
                                    </div>
                                    <div class="form-check form-switch d-flex align-items-center ps-0 mb-0">
                                        <label for="phone-3" class="form-check-label mb-1">Phone</label>
                                        <input type="checkbox" class="form-check-input ms-2" role="switch" id="phone-3">
                                    </div>
                                </div>
                            </div>

                            <!-- Item -->
                            <div class="d-sm-flex align-items-center justify-content-between border-bottom py-4">
                                <div class="me-4 my-md-2">
                                    <h3 class="h6 mb-2">Featured news</h3>
                                    <p class="fs-sm pb-1 pb-sm-0 mb-sm-0">News and tips you may be interested in</p>
                                </div>
                                <div class="d-flex gap-4 gap-xl-5 my-md-2">
                                    <div class="form-check form-switch d-flex align-items-center ps-0 mb-0">
                                        <label for="email-4" class="form-check-label">Email</label>
                                        <input type="checkbox" class="form-check-input ms-2" role="switch" id="email-4">
                                    </div>
                                    <div class="form-check form-switch d-flex align-items-center ps-0 mb-0">
                                        <label for="phone-4" class="form-check-label mb-1">Phone</label>
                                        <input type="checkbox" class="form-check-input ms-2" role="switch" id="phone-4" checked="">
                                    </div>
                                </div>
                            </div>

                            <!-- Item -->
                            <div class="d-sm-flex align-items-center justify-content-between border-bottom py-4">
                                <div class="me-4 my-md-2">
                                    <h3 class="h6 mb-2">Finder extras</h3>
                                    <p class="fs-sm pb-1 pb-sm-0 mb-sm-0">Occasional notifications about new features to make finding the perfect rental easy</p>
                                </div>
                                <div class="d-flex gap-4 gap-xl-5 my-md-2">
                                    <div class="form-check form-switch d-flex align-items-center ps-0 mb-0">
                                        <label for="email-5" class="form-check-label">Email</label>
                                        <input type="checkbox" class="form-check-input ms-2" role="switch" id="email-5" checked="">
                                    </div>
                                    <div class="form-check form-switch d-flex align-items-center ps-0 mb-0">
                                        <label for="phone-5" class="form-check-label mb-1">Phone</label>
                                        <input type="checkbox" class="form-check-input ms-2" role="switch" id="phone-5" checked="">
                                    </div>
                                </div>
                            </div>
                        </div>
                        #}
                    </div>
                </div>
            </div>
        </div>
    </main>

{% endblock %}

{% block pagescripts %}
<script class="reload-script-on-load">
    addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
    addVariables('imageId', '{{ user.imageId }}');
</script>

{% include "be/include/snippets/plugins/filepond.html" %}
<script src="{{ contextPath }}/fe/js/pages/account-info.js"></script>
{% endblock %}