{% extends "fe/include/base.html" %}
{% set currentPage = 'PHOTO_COLLECTION' %}

{% set seoTitle = 'Immobili' %}
{% set seoDescription = 'Scopri i progetti immobiliari realizzati da Domus Nova: residenziali, commerciali e industriali in Veneto. 35 anni di esperienza in costruzioni di qualità che hanno plasmato il territorio.' %}

{% block title %}{{ seoTitle }} | {{ varsCompanyName }}{% endblock %}

{% block canonical %}
    <meta name="robots" content="index, follow">
    <meta name="description" content="{{ seoDescription }}">    
    <link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
<meta property="og:locale"          content="it_IT" />
<meta property="og:url"             content="{{ publicUrl }}" />
<meta property="og:type"            content="website" />
<meta property="og:title"           content="{{ seoTitle }} | {{ varsCompanyName }}" />
<meta property="og:description"     content="{{ seoDescription }}" />
<meta property="og:image"           content="{{ contextPath }}/fe/imgs/services/servizi.jpg" />
<meta property="og:image:width"     content="1200" />
<meta property="og:image:height"    content="630" />
<meta property="og:image:alt"       content="{{ seoTitle }} | {{ varsCompanyName }}" />
{% endblock %}

{% block content %}

    <!-- Header Section -->
    <section class="page-section pb-100 pb-sm-60 bg-gray-light-1 bg-light-alpha-90 parallax-5" style="background-image: url({{ contextPath }}/fe/imgs/services/servizi.jpg)">
        <div class="position-absolute top-0 bottom-0 start-0 end-0 bg-gradient-white"></div>
        <div class="container position-relative pt-50">

            <!-- Section Content -->
            <div class="text-center">
                <div class="row">

                    <!-- Page Title -->
                    <div class="col-md-8 offset-md-2">

                        <h2 class="section-caption mb-30 mb-xs-20 wow fadeInUp">
                            IMMOBILI
                        </h2>

                        <h1 class="hs-title-1 mb-30 wow fadeInUp" data-wow-delay="0.2s">
                            I nostri <span class="color-primary-2">progetti</span> per voi
                        </h1>
                        
                        <div class="row">
                            <div class="col-md-10 offset-md-1 col-lg-8 offset-lg-2">
                                <p class="section-descr mb-0 wow fadeInUp" data-wow-delay="0.4s" data-wow-duration="1.2s">
                                    Esplora i nostri progetti e scopri come abbiamo trasformato la visione dei nostri clienti in spazi di valore destinati a durare nel tempo.
                                </p>
                            </div>
                        </div>

                    </div>
                    <!-- End Page Title -->

                </div>                            
            </div>
            <!-- End Section Content -->

        </div>
    </section>
    <!-- End Header Section -->

    <!-- Portfolio Section -->
    <section class="page-section pt-0" id="portfolio">
        <div class="container position-relative wow fadeInUp" data-wow-delay="0.6s">                       
            
            {% set photocollection = lookup(table="Photo", checkPublished=false, skip=0, orderBy="year", orderType="desc") %}
         
            {% if photocollection is not empty %}
                <!-- Works Filter -->
                <div class="works-filter text-center mb-60 mb-sm-40 z-index-1">
                    <a href="#" class="filter active" role="button" aria-pressed="true" data-filter="*">Tutti</a>
                    {% set categorylist = categorylist('Photo', language) %}
                    {% for category in categorylist %}
                        <a href="#{{ category | replace({' ' : ''}) }}" class="filter" role="button" aria-pressed="false" data-filter=".{{ category | replace({' ' : ''}) }}">{{ category }}</a>                                
                    {% endfor %}
                </div>
                <!-- End Works Filter -->

                <!-- Works Grid -->
                <ul class="works-grid work-grid-2 work-grid-gut-lg masonry" id="work-grid">

                    {% for photo in photocollection %}
                    <!-- Work Item (External Page) -->
                    <li class="work-item mix {{ photo.category | replace({' ' : ''}) }}">
                        <a href="{{ routes('PHOTO_COLLECTION') + '/' + photo.identifier }}" class="work-ext-link">
                            <div class="work-img">
                                <div class="work-img-bg wow-p scalexIn"></div>
                                <img src="{{ routes('BE_IMAGE') }}?oid={{ photo.imageIds[0] }}" alt="{{ photo.title }}" class="wow-p fadeIn" data-wow-delay="1s" loading="lazy"/>
                            </div>
                            <div class="work-intro text-start">
                                <h3 class="work-title">{{ photo.title | abbreviate(100) }}</h3>
                                <div class="work-descr">
                                    {{ photo.city }}
                                </div>
                            </div>
                        </a>
                    </li>
                    <!-- End Work Item -->
                    {% endfor %}
                </ul>
                <!-- End Works Grid -->
            {% endif %}
        </div>
    </section>
    <!-- - section PORTFOLIO GRID [sidebar filter fixed] -->
    <!-- - PAGE CONTENT -->
        
{% endblock %}

{% block pagescripts %}
    
{% endblock %}