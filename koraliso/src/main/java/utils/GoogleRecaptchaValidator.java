package utils;

import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;

import javax.net.ssl.HttpsURLConnection;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;

public class GoogleRecaptchaValidator {

    private static final String RECAPTCHA_SERVICE_URL = "https://www.google.com/recaptcha/api/siteverify";
    private static final String SECRET_KEY = Defaults.GOOGLE_RECAPTCHA_SECRET;

    public static boolean isValid(String clientRecaptchaResponse) throws IOException, ParseException {
        if (clientRecaptchaResponse == null || clientRecaptchaResponse.isEmpty()) {
            return false;
        }

        URL url = new URL(RECAPTCHA_SERVICE_URL);
        HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Accept-Language", "en-US,en;q=0.5");

        String postParams = "secret=" + SECRET_KEY + "&response=" + clientRecaptchaResponse;

        connection.setDoOutput(true);
        DataOutputStream outputStream = new DataOutputStream(connection.getOutputStream());
        outputStream.writeBytes(postParams);
        outputStream.flush();
        outputStream.close();

        int responseCode = connection.getResponseCode();
        System.out.println("Response Code: " + responseCode);

        BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        StringBuilder response = new StringBuilder();
        String inputLine;

        while ((inputLine = in.readLine()) != null) {
            response.append(inputLine);
        }

        in.close();

        System.out.println("Response from Google: " + response.toString());

        JSONParser parser = new JSONParser();
        JSONObject jsonResponse = (JSONObject) parser.parse(response.toString());

        boolean success = (boolean) jsonResponse.get("success");
        double score = (double) jsonResponse.get("score");

        System.out.println("Success: " + success);
        System.out.println("Score: " + score);

        return success && score >= 0.5;
    }
}
