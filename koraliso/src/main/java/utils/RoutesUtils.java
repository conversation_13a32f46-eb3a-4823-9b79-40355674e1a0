package utils;

import core.Routes;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

/**
 *
 * <AUTHOR>
 */
public class RoutesUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(RoutesUtils.class.getName());
    
    public static String contextPath(Request request) {
        return (EnvironmentUtils.isLocal() || EnvironmentUtils.hasNotDomainRedirect(request)) ? request.contextPath() : "";
    }
    
    public static String generateIdentifier() {
        final int digits = 8;

        long rnd = UUID.randomUUID().getLeastSignificantBits();
        rnd = Math.abs(rnd);

        String identifier = rnd + "";
        identifier = StringUtils.left(identifier, digits);
        identifier = StringUtils.leftPad(identifier, digits, '0');

        return identifier;
    }
    
    public static String baseUrl(Request request) {
        if (request == null) {
            return null;
        }

        // Log all headers to debug the issue
//        for (String headerName : request.headers()) {
//            LOGGER.info(headerName + ": " + request.headers(headerName));
//        }
//        
        String scheme = request.headers("scheme");        
        if (StringUtils.isBlank(scheme)) {
            scheme = request.headers("x-forwarded-proto");
        }
        if (scheme == null || scheme.isEmpty()) {
            scheme = request.scheme();
        }
        String host = request.headers("host");
        if (host == null || host.isEmpty()) {
            host = request.host();
        }

        return scheme + "://" + host + RoutesUtils.contextPath(request);
    }
    
    public static String publicUrl(Request request) {
        if (request == null) {
            return null;
        }

        String scheme = request.headers("scheme");        
        if (StringUtils.isBlank(scheme)) {
            scheme = request.headers("x-forwarded-proto");
        }

        if (StringUtils.isBlank(scheme)) {
            scheme = request.scheme();
        }

        // Ottieni l'host
        String host = request.headers("host");
        if (StringUtils.isBlank(host)) {
            host = request.host();
        }

        // Costruisci l'URL pubblico includendo il protocollo, l'host, il contesto dell'applicazione e il pathInfo
        String contextPath = RoutesUtils.contextPath(request);
        String pathInfo = request.pathInfo();
        String result = scheme + "://" + host + (contextPath != null ? contextPath : "") + (pathInfo != null ? pathInfo : "");

        return result;
    }
    
    public static String getLocalizedFullPath(Request request, String path, String language) {
        language = StringUtils.defaultIfBlank(language, language(request));
        return RoutesUtils.contextPath(request) + Routes.getLocalizedPathByLanguage(path, language);
    }

    public static String language(Request request) {
        String language = StringUtils.substringBetween((request != null ? request.servletPath() : ""), "/", "/");
        if (StringUtils.isNotBlank(language)) {
            if (!Defaults.AVAILABLE_LANGUAGES.contains(language)) {
                language = Defaults.LANGUAGE;
            }
        }
        language = StringUtils.defaultIfBlank(language, Defaults.LANGUAGE);
        return language;
    }

    public static String language(String path) {
        return StringUtils.substringBetween(path, "/", "/");
    }

    public static String language(String path, String language) {
        return StringUtils.replace(path, ":language", StringUtils.defaultIfBlank(language, Defaults.LANGUAGE));
    }
    
}
