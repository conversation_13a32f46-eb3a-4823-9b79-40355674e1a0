package utils;

import core.Core;
import dao.BaseDao;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Utility class for collection initialization operations
 */
public class InitializationUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(InitializationUtils.class.getName());

    /**
     * Get statistics about collections and their document counts
     */
    public static void printCollectionStatistics() {
        try {
            LOGGER.info("=== Collection Statistics ===");
            
            for (String collectionName : Defaults.COLLECTION_NAMES) {
                try {
                    boolean exists = BaseDao.collectionExists(collectionName);
                    if (exists) {
                        com.mongodb.client.MongoCollection<Document> collection = 
                            Core.mongoDatabase.getCollection(collectionName.toLowerCase());
                        long count = collection.countDocuments();
                        LOGGER.info("Collection '{}': {} documents", collectionName, count);
                    } else {
                        LOGGER.info("Collection '{}': does not exist", collectionName);
                    }
                } catch (Exception ex) {
                    LOGGER.error("Error getting statistics for collection: " + collectionName, ex);
                }
            }
            
            LOGGER.info("=== End Statistics ===");
        } catch (Exception ex) {
            LOGGER.error("Error printing collection statistics", ex);
        }
    }

    /**
     * Check if all required collections exist
     */
    public static boolean allCollectionsExist() {
        try {
            for (String collectionName : Defaults.COLLECTION_NAMES) {
                if (!BaseDao.collectionExists(collectionName)) {
                    LOGGER.warn("Required collection '{}' does not exist", collectionName);
                    return false;
                }
            }
            return true;
        } catch (Exception ex) {
            LOGGER.error("Error checking collection existence", ex);
            return false;
        }
    }

    /**
     * Get list of missing collections
     */
    public static List<String> getMissingCollections() {
        List<String> missing = new ArrayList<>();
        try {
            for (String collectionName : Defaults.COLLECTION_NAMES) {
                if (!BaseDao.collectionExists(collectionName)) {
                    missing.add(collectionName);
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Error getting missing collections", ex);
        }
        return missing;
    }

    /**
     * Validate that all collections have at least one document
     */
    public static boolean validateCollectionContent() {
        try {
            boolean allValid = true;
            
            for (String collectionName : Defaults.COLLECTION_NAMES) {
                try {
                    if (BaseDao.collectionExists(collectionName)) {
                        com.mongodb.client.MongoCollection<Document> collection = 
                            Core.mongoDatabase.getCollection(collectionName.toLowerCase());
                        long count = collection.countDocuments();
                        
                        if (count == 0) {
                            LOGGER.warn("Collection '{}' exists but is empty", collectionName);
                            allValid = false;
                        } else {
                            LOGGER.debug("Collection '{}' has {} documents", collectionName, count);
                        }
                    } else {
                        LOGGER.warn("Collection '{}' does not exist", collectionName);
                        allValid = false;
                    }
                } catch (Exception ex) {
                    LOGGER.error("Error validating collection: " + collectionName, ex);
                    allValid = false;
                }
            }
            
            return allValid;
        } catch (Exception ex) {
            LOGGER.error("Error validating collection content", ex);
            return false;
        }
    }
}
