package pojo;

import org.bson.types.ObjectId;

import java.util.List;

public class Business extends BasePojo {
    // Internal
    private String status;
    private ObjectId userId;

    // Step 1
    private Boolean diving;
    private Boolean freedivingApnea;
    private Boolean experience;
    private Boolean sport;
    private String fullname;
    private String description;

    // Step 2
    private List<String> divingCertifications;
    private Boolean divingReef;
    private Boolean divingWrecks;
    private Boolean divingNightDives;
    private Boolean divingSeaBaptism;
    private Boolean divingOpenWater;
    private Boolean divingShoreDeparture;
    private Boolean divingUnderIce;
    private Boolean divingCurrents;
    private Boolean divingAltitude;
    private Boolean divingSidemount;
    private Boolean divingScooter;
    private Boolean divingSharkCage;
    private Boolean divingCenotes;
    private Boolean divingBigAnimals;
    private Boolean divingSardineRun;
    private Boolean divingPoolY40;
    private Boolean divingFreshWater;
    private Boolean divingFocusBio;
    private Boolean divingFocusPhoto;
    private Boolean divingFocusVideo;
    private Boolean divingDeepDives;
    private Boolean divingRebreather;
    private Boolean divingDeco;
    private Boolean divingExtreme;
    private Boolean divingWildlifeTech;
    private Boolean divingUnderwaterPhotography;
    private Boolean divingUnderwaterVideo;
    private Boolean divingEditingCourse;
    private Boolean divingGoProUsage;
    private List<String> divingVacationTypes;
    private List<String> divingRecCourses;
    private Boolean divingRecInflatable;
    private Boolean divingRecRigidBoat;
    private Boolean divingRecCruise;
    private Boolean divingRecCatamaran;
    private Boolean divingTecAir;
    private Boolean divingTecNitrox40;
    private Boolean divingTecAdvancedNitrox;
    private Boolean divingTecTrimixNormoxic;
    private Boolean divingTecTrimixHypoxic;
    private Boolean divingTecPureOxygen;
    private Boolean divingTecHeliox;
    private Boolean divingTecOpenCircuit;
    private Boolean divingTecSemiClosedCircuit;
    private Boolean divingTecClosedCircuit;
    private List<String> divingTecEquipmentConfiguration;
    private String divingTecMaxDepth;
    private Boolean divingEquipmentRental;
    private Boolean divingEquipmentPurchase;
    private Boolean divingPrivateLessons;
    private Boolean divingEquipmentStorage;
    private Boolean divingRinseTanks;
    private List<String> freedivingApneaCourses;
    private Boolean freedivingApneaTrainingPlacePool;
    private Boolean freedivingApneaTrainingPlaceLake;
    private Boolean freedivingApneaTrainingPlaceSea;
    private List<String> freedivingApneaInstructorCourses;
    private List<String> freedivingApneaSpecialties;
    private List<String> freedivingApneaWorkshops;
    private List<String> freedivingApneaStages;
    private Boolean freedivingApneaYoga;
    private Boolean freedivingApneaCertifiedInstructors;
    private Boolean freedivingApneaPhotoVideo;
    private Boolean freedivingApneaEquipmentRental;
    private Boolean freedivingApneaEquipmentPurchase;
    private Boolean experienceSnorkeling;
    private Boolean experienceBoatTrip;
    private Boolean experienceTurtleWatching;
    private Boolean experienceDolphinWatching;
    private Boolean experienceWhaleWatching;
    private Boolean experienceSunsetTrip;
    private Boolean experienceIsletExcursion;
    private Boolean experienceNatureTour;
    private Boolean experienceBoatYoga;
    private Boolean experienceNightBoatTrip;
    private Boolean experienceDinnerTasting;
    private Boolean experiencePhotoTour;
    private Boolean experiencePrivateTours;
    private Boolean sportSurf;
    private Boolean sportWindsurf;
    private Boolean sportKitesurf;
    private Boolean sportSup;
    private Boolean sportKayak;
    private Boolean sportCanoe;
    private Boolean sportBodyboard;
    private Boolean sportWakeboard;
    private Boolean sportSkimboard;
    private Boolean sportParasailing;
    private Boolean sportEquipmentRental;
    private Boolean sportEquipmentPurchase;
    private Boolean sportCourses;
    private Boolean sportPrivateLessons;
    private String miscLargeGroupsAvailability;
    private Boolean miscLargeGroupsDiscount;
    private Boolean miscWifi;
    private Boolean miscChangeRoomsShowers;
    private Boolean miscRelaxArea;
    private Boolean miscFreeWater;
    private Boolean miscEmergencyKit;
    private Boolean miscHotelRestaurantDeals;
    private Boolean miscTransfer;

    // Step 3
    private List<ObjectId> imageIds;
    private String linkYoutube;

    // Step 4
    private String fulladdress;
    private String country;
    private String address;
    private String city;
    private String provinceCode;
    private String zip;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public Boolean getDiving() {
        return diving;
    }

    public void setDiving(Boolean diving) {
        this.diving = diving;
    }

    public Boolean getFreedivingApnea() {
        return freedivingApnea;
    }

    public void setFreedivingApnea(Boolean freedivingApnea) {
        this.freedivingApnea = freedivingApnea;
    }

    public Boolean getExperience() {
        return experience;
    }

    public void setExperience(Boolean experience) {
        this.experience = experience;
    }

    public Boolean getSport() {
        return sport;
    }

    public void setSport(Boolean sport) {
        this.sport = sport;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getDivingCertifications() {
        return divingCertifications;
    }

    public void setDivingCertifications(List<String> divingCertifications) {
        this.divingCertifications = divingCertifications;
    }

    public Boolean getDivingReef() {
        return divingReef;
    }

    public void setDivingReef(Boolean divingReef) {
        this.divingReef = divingReef;
    }

    public Boolean getDivingWrecks() {
        return divingWrecks;
    }

    public void setDivingWrecks(Boolean divingWrecks) {
        this.divingWrecks = divingWrecks;
    }

    public Boolean getDivingNightDives() {
        return divingNightDives;
    }

    public void setDivingNightDives(Boolean divingNightDives) {
        this.divingNightDives = divingNightDives;
    }

    public Boolean getDivingSeaBaptism() {
        return divingSeaBaptism;
    }

    public void setDivingSeaBaptism(Boolean divingSeaBaptism) {
        this.divingSeaBaptism = divingSeaBaptism;
    }

    public Boolean getDivingOpenWater() {
        return divingOpenWater;
    }

    public void setDivingOpenWater(Boolean divingOpenWater) {
        this.divingOpenWater = divingOpenWater;
    }

    public Boolean getDivingShoreDeparture() {
        return divingShoreDeparture;
    }

    public void setDivingShoreDeparture(Boolean divingShoreDeparture) {
        this.divingShoreDeparture = divingShoreDeparture;
    }

    public Boolean getDivingUnderIce() {
        return divingUnderIce;
    }

    public void setDivingUnderIce(Boolean divingUnderIce) {
        this.divingUnderIce = divingUnderIce;
    }

    public Boolean getDivingCurrents() {
        return divingCurrents;
    }

    public void setDivingCurrents(Boolean divingCurrents) {
        this.divingCurrents = divingCurrents;
    }

    public Boolean getDivingAltitude() {
        return divingAltitude;
    }

    public void setDivingAltitude(Boolean divingAltitude) {
        this.divingAltitude = divingAltitude;
    }

    public Boolean getDivingSidemount() {
        return divingSidemount;
    }

    public void setDivingSidemount(Boolean divingSidemount) {
        this.divingSidemount = divingSidemount;
    }

    public Boolean getDivingScooter() {
        return divingScooter;
    }

    public void setDivingScooter(Boolean divingScooter) {
        this.divingScooter = divingScooter;
    }

    public Boolean getDivingSharkCage() {
        return divingSharkCage;
    }

    public void setDivingSharkCage(Boolean divingSharkCage) {
        this.divingSharkCage = divingSharkCage;
    }

    public Boolean getDivingCenotes() {
        return divingCenotes;
    }

    public void setDivingCenotes(Boolean divingCenotes) {
        this.divingCenotes = divingCenotes;
    }

    public Boolean getDivingBigAnimals() {
        return divingBigAnimals;
    }

    public void setDivingBigAnimals(Boolean divingBigAnimals) {
        this.divingBigAnimals = divingBigAnimals;
    }

    public Boolean getDivingSardineRun() {
        return divingSardineRun;
    }

    public void setDivingSardineRun(Boolean divingSardineRun) {
        this.divingSardineRun = divingSardineRun;
    }

    public Boolean getDivingPoolY40() {
        return divingPoolY40;
    }

    public void setDivingPoolY40(Boolean divingPoolY40) {
        this.divingPoolY40 = divingPoolY40;
    }

    public Boolean getDivingFreshWater() {
        return divingFreshWater;
    }

    public void setDivingFreshWater(Boolean divingFreshWater) {
        this.divingFreshWater = divingFreshWater;
    }

    public Boolean getDivingFocusBio() {
        return divingFocusBio;
    }

    public void setDivingFocusBio(Boolean divingFocusBio) {
        this.divingFocusBio = divingFocusBio;
    }

    public Boolean getDivingFocusPhoto() {
        return divingFocusPhoto;
    }

    public void setDivingFocusPhoto(Boolean divingFocusPhoto) {
        this.divingFocusPhoto = divingFocusPhoto;
    }

    public Boolean getDivingFocusVideo() {
        return divingFocusVideo;
    }

    public void setDivingFocusVideo(Boolean divingFocusVideo) {
        this.divingFocusVideo = divingFocusVideo;
    }

    public Boolean getDivingDeepDives() {
        return divingDeepDives;
    }

    public void setDivingDeepDives(Boolean divingDeepDives) {
        this.divingDeepDives = divingDeepDives;
    }

    public Boolean getDivingRebreather() {
        return divingRebreather;
    }

    public void setDivingRebreather(Boolean divingRebreather) {
        this.divingRebreather = divingRebreather;
    }

    public Boolean getDivingDeco() {
        return divingDeco;
    }

    public void setDivingDeco(Boolean divingDeco) {
        this.divingDeco = divingDeco;
    }

    public Boolean getDivingExtreme() {
        return divingExtreme;
    }

    public void setDivingExtreme(Boolean divingExtreme) {
        this.divingExtreme = divingExtreme;
    }

    public Boolean getDivingWildlifeTech() {
        return divingWildlifeTech;
    }

    public void setDivingWildlifeTech(Boolean divingWildlifeTech) {
        this.divingWildlifeTech = divingWildlifeTech;
    }

    public Boolean getDivingUnderwaterPhotography() {
        return divingUnderwaterPhotography;
    }

    public void setDivingUnderwaterPhotography(Boolean divingUnderwaterPhotography) {
        this.divingUnderwaterPhotography = divingUnderwaterPhotography;
    }

    public Boolean getDivingUnderwaterVideo() {
        return divingUnderwaterVideo;
    }

    public void setDivingUnderwaterVideo(Boolean divingUnderwaterVideo) {
        this.divingUnderwaterVideo = divingUnderwaterVideo;
    }

    public Boolean getDivingEditingCourse() {
        return divingEditingCourse;
    }

    public void setDivingEditingCourse(Boolean divingEditingCourse) {
        this.divingEditingCourse = divingEditingCourse;
    }

    public Boolean getDivingGoProUsage() {
        return divingGoProUsage;
    }

    public void setDivingGoProUsage(Boolean divingGoProUsage) {
        this.divingGoProUsage = divingGoProUsage;
    }

    public List<String> getDivingVacationTypes() {
        return divingVacationTypes;
    }

    public void setDivingVacationTypes(List<String> divingVacationTypes) {
        this.divingVacationTypes = divingVacationTypes;
    }

    public List<String> getDivingRecCourses() {
        return divingRecCourses;
    }

    public void setDivingRecCourses(List<String> divingRecCourses) {
        this.divingRecCourses = divingRecCourses;
    }

    public Boolean getDivingRecInflatable() {
        return divingRecInflatable;
    }

    public void setDivingRecInflatable(Boolean divingRecInflatable) {
        this.divingRecInflatable = divingRecInflatable;
    }

    public Boolean getDivingRecRigidBoat() {
        return divingRecRigidBoat;
    }

    public void setDivingRecRigidBoat(Boolean divingRecRigidBoat) {
        this.divingRecRigidBoat = divingRecRigidBoat;
    }

    public Boolean getDivingRecCruise() {
        return divingRecCruise;
    }

    public void setDivingRecCruise(Boolean divingRecCruise) {
        this.divingRecCruise = divingRecCruise;
    }

    public Boolean getDivingRecCatamaran() {
        return divingRecCatamaran;
    }

    public void setDivingRecCatamaran(Boolean divingRecCatamaran) {
        this.divingRecCatamaran = divingRecCatamaran;
    }

    public Boolean getDivingTecAir() {
        return divingTecAir;
    }

    public void setDivingTecAir(Boolean divingTecAir) {
        this.divingTecAir = divingTecAir;
    }

    public Boolean getDivingTecNitrox40() {
        return divingTecNitrox40;
    }

    public void setDivingTecNitrox40(Boolean divingTecNitrox40) {
        this.divingTecNitrox40 = divingTecNitrox40;
    }

    public Boolean getDivingTecAdvancedNitrox() {
        return divingTecAdvancedNitrox;
    }

    public void setDivingTecAdvancedNitrox(Boolean divingTecAdvancedNitrox) {
        this.divingTecAdvancedNitrox = divingTecAdvancedNitrox;
    }

    public Boolean getDivingTecTrimixNormoxic() {
        return divingTecTrimixNormoxic;
    }

    public void setDivingTecTrimixNormoxic(Boolean divingTecTrimixNormoxic) {
        this.divingTecTrimixNormoxic = divingTecTrimixNormoxic;
    }

    public Boolean getDivingTecTrimixHypoxic() {
        return divingTecTrimixHypoxic;
    }

    public void setDivingTecTrimixHypoxic(Boolean divingTecTrimixHypoxic) {
        this.divingTecTrimixHypoxic = divingTecTrimixHypoxic;
    }

    public Boolean getDivingTecPureOxygen() {
        return divingTecPureOxygen;
    }

    public void setDivingTecPureOxygen(Boolean divingTecPureOxygen) {
        this.divingTecPureOxygen = divingTecPureOxygen;
    }

    public Boolean getDivingTecHeliox() {
        return divingTecHeliox;
    }

    public void setDivingTecHeliox(Boolean divingTecHeliox) {
        this.divingTecHeliox = divingTecHeliox;
    }

    public Boolean getDivingTecOpenCircuit() {
        return divingTecOpenCircuit;
    }

    public void setDivingTecOpenCircuit(Boolean divingTecOpenCircuit) {
        this.divingTecOpenCircuit = divingTecOpenCircuit;
    }

    public Boolean getDivingTecSemiClosedCircuit() {
        return divingTecSemiClosedCircuit;
    }

    public void setDivingTecSemiClosedCircuit(Boolean divingTecSemiClosedCircuit) {
        this.divingTecSemiClosedCircuit = divingTecSemiClosedCircuit;
    }

    public Boolean getDivingTecClosedCircuit() {
        return divingTecClosedCircuit;
    }

    public void setDivingTecClosedCircuit(Boolean divingTecClosedCircuit) {
        this.divingTecClosedCircuit = divingTecClosedCircuit;
    }

    public List<String> getDivingTecEquipmentConfiguration() {
        return divingTecEquipmentConfiguration;
    }

    public void setDivingTecEquipmentConfiguration(List<String> divingTecEquipmentConfiguration) {
        this.divingTecEquipmentConfiguration = divingTecEquipmentConfiguration;
    }

    public String getDivingTecMaxDepth() {
        return divingTecMaxDepth;
    }

    public void setDivingTecMaxDepth(String divingTecMaxDepth) {
        this.divingTecMaxDepth = divingTecMaxDepth;
    }

    public Boolean getDivingEquipmentRental() {
        return divingEquipmentRental;
    }

    public void setDivingEquipmentRental(Boolean divingEquipmentRental) {
        this.divingEquipmentRental = divingEquipmentRental;
    }

    public Boolean getDivingEquipmentPurchase() {
        return divingEquipmentPurchase;
    }

    public void setDivingEquipmentPurchase(Boolean divingEquipmentPurchase) {
        this.divingEquipmentPurchase = divingEquipmentPurchase;
    }

    public Boolean getDivingPrivateLessons() {
        return divingPrivateLessons;
    }

    public void setDivingPrivateLessons(Boolean divingPrivateLessons) {
        this.divingPrivateLessons = divingPrivateLessons;
    }

    public Boolean getDivingEquipmentStorage() {
        return divingEquipmentStorage;
    }

    public void setDivingEquipmentStorage(Boolean divingEquipmentStorage) {
        this.divingEquipmentStorage = divingEquipmentStorage;
    }

    public Boolean getDivingRinseTanks() {
        return divingRinseTanks;
    }

    public void setDivingRinseTanks(Boolean divingRinseTanks) {
        this.divingRinseTanks = divingRinseTanks;
    }

    public List<String> getFreedivingApneaCourses() {
        return freedivingApneaCourses;
    }

    public void setFreedivingApneaCourses(List<String> freedivingApneaCourses) {
        this.freedivingApneaCourses = freedivingApneaCourses;
    }

    public Boolean getFreedivingApneaTrainingPlacePool() {
        return freedivingApneaTrainingPlacePool;
    }

    public void setFreedivingApneaTrainingPlacePool(Boolean freedivingApneaTrainingPlacePool) {
        this.freedivingApneaTrainingPlacePool = freedivingApneaTrainingPlacePool;
    }

    public Boolean getFreedivingApneaTrainingPlaceLake() {
        return freedivingApneaTrainingPlaceLake;
    }

    public void setFreedivingApneaTrainingPlaceLake(Boolean freedivingApneaTrainingPlaceLake) {
        this.freedivingApneaTrainingPlaceLake = freedivingApneaTrainingPlaceLake;
    }

    public Boolean getFreedivingApneaTrainingPlaceSea() {
        return freedivingApneaTrainingPlaceSea;
    }

    public void setFreedivingApneaTrainingPlaceSea(Boolean freedivingApneaTrainingPlaceSea) {
        this.freedivingApneaTrainingPlaceSea = freedivingApneaTrainingPlaceSea;
    }

    public List<String> getFreedivingApneaInstructorCourses() {
        return freedivingApneaInstructorCourses;
    }

    public void setFreedivingApneaInstructorCourses(List<String> freedivingApneaInstructorCourses) {
        this.freedivingApneaInstructorCourses = freedivingApneaInstructorCourses;
    }

    public List<String> getFreedivingApneaSpecialties() {
        return freedivingApneaSpecialties;
    }

    public void setFreedivingApneaSpecialties(List<String> freedivingApneaSpecialties) {
        this.freedivingApneaSpecialties = freedivingApneaSpecialties;
    }

    public List<String> getFreedivingApneaWorkshops() {
        return freedivingApneaWorkshops;
    }

    public void setFreedivingApneaWorkshops(List<String> freedivingApneaWorkshops) {
        this.freedivingApneaWorkshops = freedivingApneaWorkshops;
    }

    public List<String> getFreedivingApneaStages() {
        return freedivingApneaStages;
    }

    public void setFreedivingApneaStages(List<String> freedivingApneaStages) {
        this.freedivingApneaStages = freedivingApneaStages;
    }

    public Boolean getFreedivingApneaYoga() {
        return freedivingApneaYoga;
    }

    public void setFreedivingApneaYoga(Boolean freedivingApneaYoga) {
        this.freedivingApneaYoga = freedivingApneaYoga;
    }

    public Boolean getFreedivingApneaCertifiedInstructors() {
        return freedivingApneaCertifiedInstructors;
    }

    public void setFreedivingApneaCertifiedInstructors(Boolean freedivingApneaCertifiedInstructors) {
        this.freedivingApneaCertifiedInstructors = freedivingApneaCertifiedInstructors;
    }

    public Boolean getFreedivingApneaPhotoVideo() {
        return freedivingApneaPhotoVideo;
    }

    public void setFreedivingApneaPhotoVideo(Boolean freedivingApneaPhotoVideo) {
        this.freedivingApneaPhotoVideo = freedivingApneaPhotoVideo;
    }

    public Boolean getFreedivingApneaEquipmentRental() {
        return freedivingApneaEquipmentRental;
    }

    public void setFreedivingApneaEquipmentRental(Boolean freedivingApneaEquipmentRental) {
        this.freedivingApneaEquipmentRental = freedivingApneaEquipmentRental;
    }

    public Boolean getFreedivingApneaEquipmentPurchase() {
        return freedivingApneaEquipmentPurchase;
    }

    public void setFreedivingApneaEquipmentPurchase(Boolean freedivingApneaEquipmentPurchase) {
        this.freedivingApneaEquipmentPurchase = freedivingApneaEquipmentPurchase;
    }

    public Boolean getExperienceSnorkeling() {
        return experienceSnorkeling;
    }

    public void setExperienceSnorkeling(Boolean experienceSnorkeling) {
        this.experienceSnorkeling = experienceSnorkeling;
    }

    public Boolean getExperienceBoatTrip() {
        return experienceBoatTrip;
    }

    public void setExperienceBoatTrip(Boolean experienceBoatTrip) {
        this.experienceBoatTrip = experienceBoatTrip;
    }

    public Boolean getExperienceTurtleWatching() {
        return experienceTurtleWatching;
    }

    public void setExperienceTurtleWatching(Boolean experienceTurtleWatching) {
        this.experienceTurtleWatching = experienceTurtleWatching;
    }

    public Boolean getExperienceDolphinWatching() {
        return experienceDolphinWatching;
    }

    public void setExperienceDolphinWatching(Boolean experienceDolphinWatching) {
        this.experienceDolphinWatching = experienceDolphinWatching;
    }

    public Boolean getExperienceWhaleWatching() {
        return experienceWhaleWatching;
    }

    public void setExperienceWhaleWatching(Boolean experienceWhaleWatching) {
        this.experienceWhaleWatching = experienceWhaleWatching;
    }

    public Boolean getExperienceSunsetTrip() {
        return experienceSunsetTrip;
    }

    public void setExperienceSunsetTrip(Boolean experienceSunsetTrip) {
        this.experienceSunsetTrip = experienceSunsetTrip;
    }

    public Boolean getExperienceIsletExcursion() {
        return experienceIsletExcursion;
    }

    public void setExperienceIsletExcursion(Boolean experienceIsletExcursion) {
        this.experienceIsletExcursion = experienceIsletExcursion;
    }

    public Boolean getExperienceNatureTour() {
        return experienceNatureTour;
    }

    public void setExperienceNatureTour(Boolean experienceNatureTour) {
        this.experienceNatureTour = experienceNatureTour;
    }

    public Boolean getExperienceBoatYoga() {
        return experienceBoatYoga;
    }

    public void setExperienceBoatYoga(Boolean experienceBoatYoga) {
        this.experienceBoatYoga = experienceBoatYoga;
    }

    public Boolean getExperienceNightBoatTrip() {
        return experienceNightBoatTrip;
    }

    public void setExperienceNightBoatTrip(Boolean experienceNightBoatTrip) {
        this.experienceNightBoatTrip = experienceNightBoatTrip;
    }

    public Boolean getExperienceDinnerTasting() {
        return experienceDinnerTasting;
    }

    public void setExperienceDinnerTasting(Boolean experienceDinnerTasting) {
        this.experienceDinnerTasting = experienceDinnerTasting;
    }

    public Boolean getExperiencePhotoTour() {
        return experiencePhotoTour;
    }

    public void setExperiencePhotoTour(Boolean experiencePhotoTour) {
        this.experiencePhotoTour = experiencePhotoTour;
    }

    public Boolean getExperiencePrivateTours() {
        return experiencePrivateTours;
    }

    public void setExperiencePrivateTours(Boolean experiencePrivateTours) {
        this.experiencePrivateTours = experiencePrivateTours;
    }

    public Boolean getSportSurf() {
        return sportSurf;
    }

    public void setSportSurf(Boolean sportSurf) {
        this.sportSurf = sportSurf;
    }

    public Boolean getSportWindsurf() {
        return sportWindsurf;
    }

    public void setSportWindsurf(Boolean sportWindsurf) {
        this.sportWindsurf = sportWindsurf;
    }

    public Boolean getSportKitesurf() {
        return sportKitesurf;
    }

    public void setSportKitesurf(Boolean sportKitesurf) {
        this.sportKitesurf = sportKitesurf;
    }

    public Boolean getSportSup() {
        return sportSup;
    }

    public void setSportSup(Boolean sportSup) {
        this.sportSup = sportSup;
    }

    public Boolean getSportKayak() {
        return sportKayak;
    }

    public void setSportKayak(Boolean sportKayak) {
        this.sportKayak = sportKayak;
    }

    public Boolean getSportCanoe() {
        return sportCanoe;
    }

    public void setSportCanoe(Boolean sportCanoe) {
        this.sportCanoe = sportCanoe;
    }

    public Boolean getSportBodyboard() {
        return sportBodyboard;
    }

    public void setSportBodyboard(Boolean sportBodyboard) {
        this.sportBodyboard = sportBodyboard;
    }

    public Boolean getSportWakeboard() {
        return sportWakeboard;
    }

    public void setSportWakeboard(Boolean sportWakeboard) {
        this.sportWakeboard = sportWakeboard;
    }

    public Boolean getSportSkimboard() {
        return sportSkimboard;
    }

    public void setSportSkimboard(Boolean sportSkimboard) {
        this.sportSkimboard = sportSkimboard;
    }

    public Boolean getSportParasailing() {
        return sportParasailing;
    }

    public void setSportParasailing(Boolean sportParasailing) {
        this.sportParasailing = sportParasailing;
    }

    public Boolean getSportEquipmentRental() {
        return sportEquipmentRental;
    }

    public void setSportEquipmentRental(Boolean sportEquipmentRental) {
        this.sportEquipmentRental = sportEquipmentRental;
    }

    public Boolean getSportEquipmentPurchase() {
        return sportEquipmentPurchase;
    }

    public void setSportEquipmentPurchase(Boolean sportEquipmentPurchase) {
        this.sportEquipmentPurchase = sportEquipmentPurchase;
    }

    public Boolean getSportCourses() {
        return sportCourses;
    }

    public void setSportCourses(Boolean sportCourses) {
        this.sportCourses = sportCourses;
    }

    public Boolean getSportPrivateLessons() {
        return sportPrivateLessons;
    }

    public void setSportPrivateLessons(Boolean sportPrivateLessons) {
        this.sportPrivateLessons = sportPrivateLessons;
    }

    public String getMiscLargeGroupsAvailability() {
        return miscLargeGroupsAvailability;
    }

    public void setMiscLargeGroupsAvailability(String miscLargeGroupsAvailability) {
        this.miscLargeGroupsAvailability = miscLargeGroupsAvailability;
    }

    public Boolean getMiscLargeGroupsDiscount() {
        return miscLargeGroupsDiscount;
    }

    public void setMiscLargeGroupsDiscount(Boolean miscLargeGroupsDiscount) {
        this.miscLargeGroupsDiscount = miscLargeGroupsDiscount;
    }

    public Boolean getMiscWifi() {
        return miscWifi;
    }

    public void setMiscWifi(Boolean miscWifi) {
        this.miscWifi = miscWifi;
    }

    public Boolean getMiscChangeRoomsShowers() {
        return miscChangeRoomsShowers;
    }

    public void setMiscChangeRoomsShowers(Boolean miscChangeRoomsShowers) {
        this.miscChangeRoomsShowers = miscChangeRoomsShowers;
    }

    public Boolean getMiscRelaxArea() {
        return miscRelaxArea;
    }

    public void setMiscRelaxArea(Boolean miscRelaxArea) {
        this.miscRelaxArea = miscRelaxArea;
    }

    public Boolean getMiscFreeWater() {
        return miscFreeWater;
    }

    public void setMiscFreeWater(Boolean miscFreeWater) {
        this.miscFreeWater = miscFreeWater;
    }

    public Boolean getMiscEmergencyKit() {
        return miscEmergencyKit;
    }

    public void setMiscEmergencyKit(Boolean miscEmergencyKit) {
        this.miscEmergencyKit = miscEmergencyKit;
    }

    public Boolean getMiscHotelRestaurantDeals() {
        return miscHotelRestaurantDeals;
    }

    public void setMiscHotelRestaurantDeals(Boolean miscHotelRestaurantDeals) {
        this.miscHotelRestaurantDeals = miscHotelRestaurantDeals;
    }

    public Boolean getMiscTransfer() {
        return miscTransfer;
    }

    public void setMiscTransfer(Boolean miscTransfer) {
        this.miscTransfer = miscTransfer;
    }

    public List<ObjectId> getImageIds() {
        return imageIds;
    }

    public void setImageIds(List<ObjectId> imageIds) {
        this.imageIds = imageIds;
    }

    public String getLinkYoutube() {
        return linkYoutube;
    }

    public void setLinkYoutube(String linkYoutube) {
        this.linkYoutube = linkYoutube;
    }

    public String getFulladdress() {
        return fulladdress;
    }

    public void setFulladdress(String fulladdress) {
        this.fulladdress = fulladdress;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getZip() {
        return zip;
    }

    public void setZip(String zip) {
        this.zip = zip;
    }
}
