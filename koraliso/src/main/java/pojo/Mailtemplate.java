package pojo;

import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Mailtemplate extends BasePojo {

    private String key;
    private String object;
    private String description;
    private String from;
    private List<String> to;
    private List<String> cc;
    private List<String> ccn;
    private ObjectId smtpId;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public List<String> getTo() {
        return to;
    }

    public void setTo(List<String> to) {
        this.to = to;
    }

    public List<String> getCc() {
        return cc;
    }

    public void setCc(List<String> cc) {
        this.cc = cc;
    }

    public List<String> getCcn() {
        return ccn;
    }

    public void setCcn(List<String> ccn) {
        this.ccn = ccn;
    }

    public ObjectId getSmtpId() {
        return smtpId;
    }

    public void setSmtpId(ObjectId smtpId) {
        this.smtpId = smtpId;
    }
    
}
