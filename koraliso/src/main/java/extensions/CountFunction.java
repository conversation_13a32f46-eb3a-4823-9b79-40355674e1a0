package extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import dao.BaseDao;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class CountFunction implements Function {

    private static final Logger LOGGER = LoggerFactory.getLogger(CountFunction.class.getName());

    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("table");
        names.add("language");
        return names;
    }

    @Override
    public Long execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {

        String table = (String) args.get("table");
        String language = (String) args.get("language");

        // language
        if (StringUtils.isBlank(language)) {
            language = (String) context.getVariable("language");
        }

        return countElements(table, language);
    }

    private Long countElements(String table, String language) {
        Long amount = null;

        try {
            Class<?> clazz = Class.forName("pojo." + table);
            if (StringUtils.isNotBlank(language)) {
                amount = BaseDao.<Object>countDocuments((Class<Object>) clazz, language);
            } else {
                amount = BaseDao.<Object>countDocuments((Class<Object>) clazz);
            }
        } catch (Exception ex) {
            LOGGER.error("Can't load items from collection " + table, ex);
        }

        return amount;
    }

}
