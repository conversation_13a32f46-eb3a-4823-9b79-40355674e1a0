package extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gt;
import static com.mongodb.client.model.Filters.lt;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.QueryOptions;

/**
 *
 * <AUTHOR>
 */
public class PrevFunction implements Function {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(NextFunction.class.getName());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("table");
        names.add("language");
        names.add("checkPublished");
        names.add("currentElement");
        names.add("orderBy"); //fieldname
        names.add("orderType"); //asc, desc
        return names;
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        String table = (String) args.get("table");
        String language = (String) args.get("language");
        Boolean checkPublished = (Boolean) args.getOrDefault("checkPublished", true);
        ObjectId currentElementId = (ObjectId) args.get("currentElement");
        String orderBy = (String) args.getOrDefault("orderBy", "creation");
        String orderType = (String) args.getOrDefault("orderType", "desc");
        
        // language
        if (StringUtils.isBlank(language)) {
            language = (String) context.getVariable("language");
        }
        
        if (StringUtils.isBlank(table)) {
            LOGGER.error("missing table");
            return null;
        }
        if (currentElementId == null) {
            LOGGER.error("missing id");
            return null;
        }
        
        return getPrevElement(table, language, checkPublished, currentElementId, orderBy, orderType);
    }
    
    
    private Object getPrevElement(String table, String language, Boolean checkPublished, ObjectId currentElementId, String orderBy, String orderType) {
        Object nextElement = null;

        try {
            Class<?> clazz = Class.forName("pojo." + table);
            List<Bson> filters = new ArrayList<>();
            if (BooleanUtils.isTrue(checkPublished)) {
                filters.add(DaoFilters.getFilter("status", DaoFiltersOperation.EQ, "published"));
            }
            
            Object currentElement = BaseDao.getDocumentById(currentElementId, clazz, language);
            if (currentElement == null) {
                LOGGER.error("Current element not found");
                return null;
            }

            Object currentValue = getCurrentValue(currentElement, orderBy);

            
            if (BooleanUtils.isTrue(checkPublished)) {
                filters.add(eq("status", "published"));
            }

            if ("asc".equalsIgnoreCase(orderType)) {
                filters.add(lt(orderBy, currentValue));
            } else {
                filters.add(gt(orderBy, currentValue));
            }
            
            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, orderBy, orderType);

            nextElement = BaseDao.getDocumentsByFilters(clazz, queryOptions, language).stream().findFirst().orElse(null);

        } catch (Exception ex) {
            LOGGER.error("Can't load items from collection " + table, ex);
        }

        return nextElement;
    }
    
    private Object getCurrentValue(Object currentElement, String orderBy) {
        try {
            // Utilizza la riflessione per ottenere il valore del campo
            return currentElement.getClass().getMethod("get" + StringUtils.capitalize(orderBy)).invoke(currentElement);
        } catch (Exception e) {
            LOGGER.error("Error getting value for orderBy field", e);
            return null;
        }
    }
}
