package extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import dao.BaseDao;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.DocumentDescriptor;

/**
 *
 * <AUTHOR>
 */
public class BlobFunction implements Function {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("oid");
        return names;
    }

    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        ObjectId oid = (ObjectId) args.get("oid");
        if (oid != null) {
            try {
                // TODO: aggiungere filtro lingua oltre all'ObjectId
                DocumentDescriptor document = BaseDao.getDocumentById(oid, DocumentDescriptor.class);
                if (StringUtils.isNotBlank(document.getFilePath())) {
                    File file = new File(document.getFilePath());
                    try (FileInputStream fis = new FileInputStream(file);  ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                        byte[] buffer = new byte[1024];
                        int bytesRead;

                        while ((bytesRead = fis.read(buffer)) != -1) {
                            bos.write(buffer, 0, bytesRead);
                        }

                        document.setContent(bos.toByteArray());
                    }
                }
                
                return document;
            } catch (Exception ex) {
                LOGGER.error("Unable to get blob for id " + oid, ex);
            }
        }

        return null;
    }
}
