package core;

import com.mongodb.client.MongoCollection;
import controller.*;

import java.io.InputStream;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.SerializationFeature;
import dao.BaseDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Filter;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateEngine;
import spark.TemplateViewRoute;
import spark.servlet.SparkApplication;
import utils.Defaults;
import utils.EnvironmentUtils;
import utils.RoutesUtils;

/**
 *
 * <AUTHOR>
 */
public class Application implements SparkApplication {

    private static final Logger LOGGER = LoggerFactory.getLogger(Application.class.getName());
    private static boolean reloadApplication = false;

    @Override
    public void init() {
        initApplication();
    }

    public static void reloadApplication() {
        reloadApplication = true;
        initApplication();
        reloadApplication = false;
        Routes.reloadPaths();
        initApplication();
    }

    public static void initApplication() {
        // configure static resources
        // ... remember to put index.html in your static folder if you want a
        //     project default page
        if (EnvironmentUtils.isLocal()) {
            Spark.staticFiles.externalLocation("/projects/" + Defaults.PROJECT_NAME + "/" + Defaults.PROJECT_NAME + "/src/main/resources/public");
            // no caching
        } else {
            Spark.staticFileLocation("/public");
            Spark.staticFiles.expireTime(Defaults.STATIC_RESOURCE_EXPIRATION_TIME);
        }

        // initialize collections from JSON files
        initializeCollections();

        // default exception handling (simply logs exceptions)
        Spark.exception(Exception.class, (exception, request, response) -> {
            LOGGER.error("exception ", exception);

            Map<String, Object> attributes = new HashMap<>();
            Core.initializeRouteFrontEnd(request, response, attributes);
            response.redirect(RoutesUtils.contextPath(request) + "/" + attributes.get("language") + "/404");
        });

        Spark.notFound(HomeController.error_page);

        // clean-up useless slashes
        //Spark.before("*", Filters.removeTrailingSlashes);

        // check connections (when mongodb or redis aren't working)
        //Spark.before("*", addUserToRequest);

        // root default (when a static index.html doesn't exists)
        Spark.before("/", defaultPageRedirect);

        // login / signup
        get         (Routes.BE_LOGIN,                   LoginController.be_login, Core.engine);
        post        (Routes.BE_LOGIN_DO,                LoginController.be_login_do);
        get         (Routes.BE_SIGNUP,                  LoginController.be_signup, Core.engine);
        post        (Routes.BE_SIGNUP_SAVE,             LoginController.be_signup_save);
        get         (Routes.BE_LOGOUT,                  LoginController.be_logout, Core.engine);

        // dashboard
        get         (Routes.BE_DASHBOARD,               DashboardController.be_dashboard, Core.engine);

        // settings
        get         (Routes.BE_SETTINGS_LANGUAGES,      SettingsController.be_settings_languages, Core.engine);
        post        (Routes.BE_SETTINGS_SAVE,           SettingsController.be_settings_save);

        get         (Routes.BE_SETTINGS_COMPANY,        CompanyController.be_company, Core.engine);
        post        (Routes.BE_SETTINGS_COMPANY_SAVE,   CompanyController.be_company_save);

        get         (Routes.BE_SETTINGS_SMTP_COLLECTION,SmtpController.be_smtp_collection, Core.engine);
        get         (Routes.BE_SETTINGS_SMTP,           SmtpController.be_smtp, Core.engine);
        get         (Routes.BE_SETTINGS_SMTP_DATA,      SmtpController.be_smtp_data);
        post        (Routes.BE_SETTINGS_SMTP_SAVE,      SmtpController.be_smtp_save);

        get         (Routes.BE_SETTINGS_USER_COLLECTION,SettingsUserController.be_settings_user_collection, Core.engine);
        get         (Routes.BE_SETTINGS_USER,           SettingsUserController.be_settings_user, Core.engine);
        get         (Routes.BE_SETTINGS_USER_DATA,      SettingsUserController.be_settings_user_data);
        post        (Routes.BE_SETTINGS_USER_SAVE,      SettingsUserController.be_settings_user_save);
        
        // user
        get         (Routes.BE_USER_COLLECTION,         UserController.be_user_collection, Core.engine);
        get         (Routes.BE_USER,                    UserController.be_user, Core.engine);
        get         (Routes.BE_USER_DATA,               UserController.be_user_data);
        post        (Routes.BE_USER_SAVE,               UserController.be_user_save);
        post        (Routes.BE_USER_OPERATE,            UserController.be_user_operate);

        // business
        get         (Routes.BE_BUSINESS_COLLECTION,     BusinessController.be_business_collection, Core.engine);
        get         (Routes.BE_BUSINESS,                BusinessController.be_business, Core.engine);
        get         (Routes.BE_BUSINESS_DATA,           BusinessController.be_business_data);
        post        (Routes.BE_BUSINESS_OPERATE,        BusinessController.be_business_operate);
        
        // blog
        get         (Routes.BE_ARTICLE_COLLECTION,      ArticleController.be_article_collection, Core.engine);
        get         (Routes.BE_ARTICLE,                 ArticleController.be_article, Core.engine);
        get         (Routes.BE_ARTICLE_DATA,            ArticleController.be_article_data);
        post        (Routes.BE_ARTICLE_SAVE,            ArticleController.be_article_save);
        post        (Routes.BE_ARTICLE_OPERATE,         ArticleController.be_article_operate);

        // event
        get         (Routes.BE_EVENT_COLLECTION,        EventController.be_event_collection, Core.engine);
        get         (Routes.BE_EVENT,                   EventController.be_event, Core.engine);
        get         (Routes.BE_EVENT_DATA,              EventController.be_event_data);
        post        (Routes.BE_EVENT_SAVE,              EventController.be_event_save);
        post        (Routes.BE_EVENT_OPERATE,           EventController.be_event_operate);

        // project
        get         (Routes.BE_PROJECT_COLLECTION,      ProjectController.be_project_collection, Core.engine);
        get         (Routes.BE_PROJECT,                 ProjectController.be_project, Core.engine);
        get         (Routes.BE_PROJECT_DATA,            ProjectController.be_project_data);
        post        (Routes.BE_PROJECT_SAVE,            ProjectController.be_project_save);
        post        (Routes.BE_PROJECT_OPERATE,         ProjectController.be_project_operate);

        
        // gallery
        get         (Routes.BE_PHOTO_COLLECTION,        PhotoController.be_photo_collection, Core.engine);
        get         (Routes.BE_PHOTO,                   PhotoController.be_photo, Core.engine);
        get         (Routes.BE_PHOTO_DATA,              PhotoController.be_photo_data);
        post        (Routes.BE_PHOTO_SAVE,              PhotoController.be_photo_save);
        post        (Routes.BE_PHOTO_OPERATE,           PhotoController.be_photo_operate);
        
        // category
        get         (Routes.BE_CATEGORY_COLLECTION,     CategoryController.be_category_collection, Core.engine);
        get         (Routes.BE_CATEGORY,                CategoryController.be_category, Core.engine);
        get         (Routes.BE_CATEGORY_DATA,           CategoryController.be_category_data);
        post        (Routes.BE_CATEGORY_SAVE,           CategoryController.be_category_save);
        post        (Routes.BE_CATEGORY_OPERATE,        CategoryController.be_category_operate);

        // mailtemplate
        get         (Routes.BE_MAILTEMPLATE_COLLECTION, MailtemplateController.be_mailtemplate_collection, Core.engine);
        get         (Routes.BE_MAILTEMPLATE,            MailtemplateController.be_mailtemplate, Core.engine);
        get         (Routes.BE_MAILTEMPLATE_DATA,       MailtemplateController.be_mailtemplate_data);
        post        (Routes.BE_MAILTEMPLATE_SAVE,       MailtemplateController.be_mailtemplate_save);

        // contact
        get         (Routes.BE_CONTACT_COLLECTION,      ContactController.be_contact_collection, Core.engine);
        get         (Routes.BE_CONTACT,                 ContactController.be_contact, Core.engine);
        get         (Routes.BE_CONTACT_DATA,            ContactController.be_contact_data);

        // script
        get         (Routes.BE_SCRIPT_COLLECTION,       ScriptController.be_script_collection, Core.engine);
        get         (Routes.BE_SCRIPT,                  ScriptController.be_script, Core.engine);
        get         (Routes.BE_SCRIPT_DATA,             ScriptController.be_script_data);
        post        (Routes.BE_SCRIPT_SAVE,             ScriptController.be_script_save);

        // label
        get         (Routes.BE_LABELS,                  LabelController.be_labels, Core.engine);
        get         (Routes.BE_LABELS_DATA,             LabelController.be_labels_data);
        post        (Routes.BE_LABELS_SAVE,             LabelController.be_labels_save);

        // path
        get         (Routes.BE_PATHS,                   PathController.be_paths, Core.engine);
        get         (Routes.BE_PATHS_DATA,              PathController.be_paths_data);
        post        (Routes.BE_PATHS_SAVE,              PathController.be_paths_save);

        /*  DA FIXARE */

        // smtp
        post        (Routes.BE_SEND_MAIL,              SmtpController.send_mail);

        // slider
        get         (Routes.BE_SLIDER,              SliderController.slider, Core.engine);
        get         (Routes.BE_SLIDER_EDIT,         SliderController.slider_edit, Core.engine);
        post        (Routes.BE_SLIDER_EDIT_SAVE,    SliderController.slider_edit_save);

        // files
        get         (Routes.BE_IMAGE,               ImageController.image);
        post        (Routes.BE_IMAGE_SAVE,          ImageController.image_save);
        get         (Routes.BE_IMAGE_BLOB,          ImageController.image_blob);

        // home
        get         ("HOME",                        HomeController.home, Core.engine);
        get         ("BUSINESS_DETAIL",             HomeController.business_detail, Core.engine);        
        get         ("COMING_SOON",                 HomeController.coming_soon, Core.engine);        
        get         ("ABOUT",                       HomeController.about, Core.engine);
        get         ("MENU",                        HomeController.menu, Core.engine);
        get         ("SERVICES",                    HomeController.services, Core.engine);
        get         ("PHOTO_COLLECTION",            HomeController.photo_collection, Core.engine);
//        get         ("PHOTO_CATEGORY",              HomeController.photo_category, Core.engine);
        get         ("PHOTO",                       HomeController.photo, Core.engine);
//        get         ("NEWS_COLLECTION",             HomeController.news_collection, Core.engine);
//        get         ("NEWS",                        HomeController.news, Core.engine);
        get         ("CONTACTS",                    HomeController.contacts, Core.engine);
        get         ("JOB"     ,                    HomeController.job, Core.engine);

        get         ("404",                         HomeController.error_404, Core.engine);
        
        get         ("ACCOUNT_LOGIN",               AccountController.account_login, Core.engine);        
        post        ("LOGIN_DO",                    AccountController.login_do);
        get         ("ACCOUNT_REGISTER",            AccountController.account_register, Core.engine);        
        post        ("REGISTER_DO",                 AccountController.register_do);
        get         ("ACCOUNT_LOGOUT",              AccountController.account_logout, Core.engine);
        get         ("ACCOUNT_RECOVER",             AccountController.account_recover, Core.engine);        
        post        ("RECOVER_SEND",                AccountController.recover_send);
        get         ("ACCOUNT_VERIFY",              AccountController.account_verify, Core.engine);                
        get         ("ACCOUNT_INFO",                AccountController.account_info, Core.engine);
        post        ("ACCOUNT_INFO_SAVE",           AccountController.account_info_save);
        post        ("ACCOUNT_PASSWORD_SAVE",       AccountController.account_password_save);
        post        ("ACCOUNT_DELETE",              AccountController.account_delete);
        post        ("ACCOUNT_IMAGE_SAVE",          AccountController.account_image_save);
        get         ("ACCOUNT_FAVOURITES",          AccountController.account_favourites, Core.engine);
        get         ("ACCOUNT_BUSINESSES",          AccountController.account_businesses, Core.engine);
        get         ("ACCOUNT_BUSINESS_EDIT",       BusinessController.account_business_edit, Core.engine);
        post        ("ACCOUNT_BUSINESS_EDIT_SAVE",  BusinessController.account_business_edit_save);
        post        ("ACCOUNT_BUSINESS_DRAFT_CHOICE", BusinessController.account_business_draft_choice);
        get         ("ACCOUNT_BUSINESS_INFO",       AccountController.account_business_info, Core.engine);
        get         ("ACCOUNT_BUSINESS_SERVICES",   AccountController.account_business_services, Core.engine);
        get         ("ACCOUNT_BUSINESS_BOOKINGS",   AccountController.account_business_bookings, Core.engine);
        get         ("ACCOUNT_BUSINESS_REVIEWS",    AccountController.account_business_reviews, Core.engine);

        get         ("RESULTS",                     ResultController.results, Core.engine);
        post        ("RESULTS_DATA",                ResultController.results_data, Core.engine);

    }

    public static Filter defaultPageRedirect = (Request request, Response response) -> {
        response.redirect(RoutesUtils.contextPath(request) + "/it/coming-soon");
        Spark.halt();
    };

    @Override
    public void destroy() {
        Core.destroy();
    }

    private static void get(String path, Route route) {
        List<String> localizedPaths = Routes.getLocalizedPath(path);
        for (String localizedPath : localizedPaths) {
            Spark.get(localizedPath, route);
        }
    }

    private static void get(String path, TemplateViewRoute route, TemplateEngine engine) {
        List<String> localizedPaths = Routes.getLocalizedPath(path);
        for (String localizedPath : localizedPaths) {
            if (StringUtils.isNotBlank(localizedPath)) {
                if (reloadApplication) {
                    Spark.unmap(localizedPath);
                } else {
                    Spark.get(localizedPath, route, engine);
                }
            }
        }
    }

    private static void post(String path, Route route) {
        List<String> localizedPaths = Routes.getLocalizedPath(path);
        for (String localizedPath : localizedPaths) {
            if (StringUtils.isNotBlank(localizedPath)) {
                if (reloadApplication) {
                    Spark.unmap(localizedPath);
                } else {
                    Spark.post(localizedPath, route);
                }
            }
        }
    }

    private static void post(String path, TemplateViewRoute route, TemplateEngine engine) {
        List<String> localizedPaths = Routes.getLocalizedPath(path);
        for (String localizedPath : localizedPaths) {
            if (StringUtils.isNotBlank(localizedPath)) {
                if (reloadApplication) {
                    Spark.unmap(localizedPath);
                } else {
                    Spark.post(localizedPath, route, engine);
                }
            }
        }
    }

//    private void get(String path, Route route) {
//        String[] paths = Paths.localizedPaths(path);
//        for (String pth : paths) {
//            Spark.get(pth, route);
//        }
//    }
//
//    private void getHtml(String path, TemplateViewRoute route) {
//        getHtml(path, route, FlashMessageType.none);
//    }
//
//    private void getHtml(String path, TemplateViewRoute route, FlashMessageType flash) {
//        String[] paths = Paths.localizedPaths(path);
//        for (String pth : paths) {
//            Spark.get(pth, route, Manager.engine);
//            if (flash != null) switch (flash) {
//                case successAndError:
//                    Spark.get(Paths.success(pth), route, Manager.engine);
//                    Spark.get(Paths.error(pth), route, Manager.engine);
//                    break;
//                case success:
//                    Spark.get(Paths.success(pth), route, Manager.engine);
//                    break;
//                case error:
//                    Spark.get(Paths.error(pth), route, Manager.engine);
//                    break;
//                default:
//                    break;
//            }
//        }
//    }
//
//    private void getJson(String path, Route route) {
//        String[] paths = Paths.localizedPaths(path);
//        for (String pth : paths) {
//            Spark.get(pth, route, Manager.jsonTransformer);
//        }
//    }
//
//    private void postJson(String path, Route route) {
//        String[] paths = Paths.localizedPaths(path);
//        for (String pth : paths) {
//            Spark.post(pth, route, Manager.jsonTransformer);
//        }
//    }
//
//    private void post(String path, Route route) {
//        String[] paths = Paths.localizedPaths(path);
//        for (String pth : paths) {
//            Spark.post(pth, route);
//        }
//    }

    /**
     * Initialize collections from JSON files in resources/mongo folder
     */
    private static void initializeCollections() {
        try {
            LOGGER.info("Starting collection initialization...");

            // Check if mongo folder exists in resources
            boolean mongoFolderExists = checkMongoFolderExists();
            LOGGER.info("Mongo folder exists in resources: {}", mongoFolderExists);

            for (String collectionName : Defaults.COLLECTION_NAMES) {
                LOGGER.info("Processing collection: " + collectionName);

                // Check if collection exists in database
                boolean collectionExists = BaseDao.collectionExists(collectionName);
                LOGGER.info("Collection '{}' exists in database: {}", collectionName, collectionExists);

                if (mongoFolderExists) {
                    // Check for JSON file in resources/mongo folder
                    String jsonFileName = "/mongo/" + collectionName + ".json";
                    InputStream jsonStream = Application.class.getResourceAsStream(jsonFileName);

                    if (jsonStream != null) {
                        LOGGER.info("Found JSON file for collection: " + collectionName);
                        processJsonFile(collectionName, jsonStream);
                    } else {
                        LOGGER.info("No JSON file found for collection: {}, creating from database content", collectionName);
                        // File doesn't exist, create it from database content
                        updateJsonFileWithDatabaseContent(collectionName);
                    }
                } else {
                    LOGGER.info("Mongo folder doesn't exist, creating JSON file from database content for collection: " + collectionName);
                    // Folder doesn't exist, create it and the JSON file from database content
                    updateJsonFileWithDatabaseContent(collectionName);
                }
            }

            LOGGER.info("Collection initialization completed.");
        } catch (Exception ex) {
            LOGGER.error("Error during collection initialization", ex);
        }
    }

    /**
     * Process JSON file for a specific collection
     */
    private static void processJsonFile(String collectionName, InputStream jsonStream) {
        try {
            // Read JSON content
            List<Document> jsonDocuments = new ArrayList<>();

            // Parse JSON - handle both single document and array of documents
            String jsonContent = new String(jsonStream.readAllBytes());
            jsonStream.close();

            if (jsonContent.trim().startsWith("[")) {
                // Array of documents - parse each document separately
                jsonContent = jsonContent.trim();
                jsonContent = jsonContent.substring(1, jsonContent.length() - 1); // Remove [ and ]

                // Split by document boundaries (simple approach)
                String[] docStrings = jsonContent.split("\\},\\s*\\{");
                for (int i = 0; i < docStrings.length; i++) {
                    String docStr = docStrings[i].trim();
                    if (i > 0) docStr = "{" + docStr;
                    if (i < docStrings.length - 1) docStr = docStr + "}";

                    try {
                        Document doc = Document.parse(docStr);
                        jsonDocuments.add(doc);
                    } catch (Exception e) {
                        LOGGER.warn("Failed to parse document: " + docStr, e);
                    }
                }
            } else {
                // Single document
                Document singleDoc = Document.parse(jsonContent);
                jsonDocuments.add(singleDoc);
            }

            LOGGER.info("Found {} documents in JSON file for collection: {}", jsonDocuments.size(), collectionName);

            // Process each document
            for (Document jsonDoc : jsonDocuments) {
                ObjectId docId = jsonDoc.getObjectId("_id");
                if (docId != null) {
                    // Check if document exists in database
                    boolean docExists = BaseDao.documentExistsById(collectionName, docId);

                    if (!docExists) {
                        // Insert document into database
                        BaseDao.insertDocumentToCollection(collectionName, jsonDoc);
                        LOGGER.info("Inserted document with _id: {} into collection: {}", docId, collectionName);
                    } else {
                        LOGGER.debug("Document with _id: {} already exists in collection: {}", docId, collectionName);
                    }
                } else {
                    LOGGER.warn("Document without _id found in JSON file for collection: {}", collectionName);
                }
            }

            // Update JSON file with current database state
            updateJsonFileWithDatabaseContent(collectionName);

        } catch (Exception ex) {
            LOGGER.error("Error processing JSON file for collection: " + collectionName, ex);
        }
    }

    /**
     * Check if mongo folder exists in resources
     */
    private static boolean checkMongoFolderExists() {
        try {
            InputStream mongoFolder = Application.class.getResourceAsStream("/mongo/");
            return mongoFolder != null;
        } catch (Exception ex) {
            LOGGER.debug("Error checking mongo folder existence", ex);
            return false;
        }
    }

    /**
     * Update JSON file with current database content
     */
    private static void updateJsonFileWithDatabaseContent(String collectionName) {
        try {
            // Get all documents from the collection
            MongoCollection<Document> collection = Core.mongoDatabase.getCollection(collectionName.toLowerCase());
            List<Document> allDocs = new ArrayList<>();
            collection.find().into(allDocs);

            LOGGER.info("Updating JSON file for collection '{}' with {} documents from database",
                       collectionName, allDocs.size());

            // Create the file path for the JSON file
            String projectPath = getProjectPath();
            String mongoFolderPath = projectPath + "/src/main/resources/mongo";
            String jsonFilePath = mongoFolderPath + "/" + collectionName + ".json";

            // Create mongo directory if it doesn't exist
            File mongoDir = new File(mongoFolderPath);
            if (!mongoDir.exists()) {
                boolean created = mongoDir.mkdirs();
                LOGGER.info("Created mongo directory: {}", created);
            }

            // Convert documents to JSON and write to file
            writeDocumentsToJsonFile(allDocs, jsonFilePath, collectionName);

        } catch (Exception ex) {
            LOGGER.error("Error updating JSON file for collection: " + collectionName, ex);
        }
    }

    /**
     * Get the project path for file operations
     */
    private static String getProjectPath() {
        try {
            // Try to get the project path from system property or current working directory
            String projectPath = System.getProperty("user.dir");

            // If we're in the koraliso subdirectory, go up one level
            if (projectPath.endsWith(Defaults.PROJECT_NAME)) {
                return projectPath;
            } else if (projectPath.contains(Defaults.PROJECT_NAME)) {
                // If we're in the parent directory, add koraliso
                return projectPath + "/" + Defaults.PROJECT_NAME;
            } else {
                // Default fallback
                return projectPath + "/" + Defaults.PROJECT_NAME;
            }
        } catch (Exception ex) {
            LOGGER.error("Error getting project path", ex);
            return "./" + Defaults.PROJECT_NAME; // fallback
        }
    }

    /**
     * Write documents to JSON file
     */
    private static void writeDocumentsToJsonFile(List<Document> documents, String filePath, String collectionName) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.enable(SerializationFeature.INDENT_OUTPUT);

            File jsonFile = new File(filePath);

            if (documents.isEmpty()) {
                // Write empty array if no documents
                try (FileWriter writer = new FileWriter(jsonFile)) {
                    writer.write("[]");
                }
                LOGGER.info("Created empty JSON file for collection '{}' at: {}", collectionName, filePath);
            } else {
                // Convert documents to JSON format
                List<Map<String, Object>> jsonDocuments = new ArrayList<>();
                for (Document doc : documents) {
                    // Convert BSON document to Map for proper JSON serialization
                    Map<String, Object> docMap = convertDocumentToMap(doc);
                    jsonDocuments.add(docMap);
                }

                // Write to file
                try (FileWriter writer = new FileWriter(jsonFile)) {
                    mapper.writeValue(writer, jsonDocuments);
                }

                LOGGER.info("Successfully wrote {} documents to JSON file for collection '{}' at: {}",
                           documents.size(), collectionName, filePath);
            }

        } catch (Exception ex) {
            LOGGER.error("Error writing documents to JSON file: " + filePath, ex);
        }
    }

    /**
     * Convert BSON Document to Map for JSON serialization
     */
    private static Map<String, Object> convertDocumentToMap(Document doc) {
        Map<String, Object> map = new HashMap<>();

        for (Map.Entry<String, Object> entry : doc.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof ObjectId) {
                // Convert ObjectId to MongoDB extended JSON format
                Map<String, String> oidMap = new HashMap<>();
                oidMap.put("$oid", value.toString());
                map.put(key, oidMap);
            } else if (value instanceof java.util.Date) {
                // Convert Date to MongoDB extended JSON format
                Map<String, String> dateMap = new HashMap<>();
                dateMap.put("$date", ((java.util.Date) value).toInstant().toString());
                map.put(key, dateMap);
            } else if (value instanceof Document) {
                // Recursively convert nested documents
                map.put(key, convertDocumentToMap((Document) value));
            } else if (value instanceof List) {
                // Handle lists (arrays)
                List<Object> list = (List<Object>) value;
                List<Object> convertedList = new ArrayList<>();
                for (Object item : list) {
                    if (item instanceof Document) {
                        convertedList.add(convertDocumentToMap((Document) item));
                    } else {
                        convertedList.add(item);
                    }
                }
                map.put(key, convertedList);
            } else {
                // Keep primitive types as-is
                map.put(key, value);
            }
        }

        return map;
    }
}
