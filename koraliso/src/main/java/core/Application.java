package core;

import controller.*;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import dao.BaseDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Filter;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateEngine;
import spark.TemplateViewRoute;
import spark.servlet.SparkApplication;
import utils.Defaults;
import utils.EnvironmentUtils;
import utils.RoutesUtils;

/**
 *
 * <AUTHOR>
 */
public class Application implements SparkApplication {

    private static final Logger LOGGER = LoggerFactory.getLogger(Application.class.getName());
    private static boolean reloadApplication = false;

    @Override
    public void init() {
        initApplication();
    }

    public static void reloadApplication() {
        reloadApplication = true;
        initApplication();
        reloadApplication = false;
        Routes.reloadPaths();
        initApplication();
    }

    public static void initApplication() {
        // configure static resources
        // ... remember to put index.html in your static folder if you want a
        //     project default page
        if (EnvironmentUtils.isLocal()) {
            Spark.staticFiles.externalLocation("/projects/" + Defaults.PROJECT_NAME + "/" + Defaults.PROJECT_NAME + "/src/main/resources/public");
            // no caching
        } else {
            Spark.staticFileLocation("/public");
            Spark.staticFiles.expireTime(Defaults.STATIC_RESOURCE_EXPIRATION_TIME);
        }

        // default exception handling (simply logs exceptions)
        Spark.exception(Exception.class, (exception, request, response) -> {
            LOGGER.error("exception ", exception);

            Map<String, Object> attributes = new HashMap<>();
            Core.initializeRouteFrontEnd(request, response, attributes);
            response.redirect(RoutesUtils.contextPath(request) + "/" + attributes.get("language") + "/404");
        });

        Spark.notFound(HomeController.error_page);

        // clean-up useless slashes
        //Spark.before("*", Filters.removeTrailingSlashes);

        // check connections (when mongodb or redis aren't working)
        //Spark.before("*", addUserToRequest);

        // root default (when a static index.html doesn't exists)
        Spark.before("/", defaultPageRedirect);

        // login / signup
        get         (Routes.BE_LOGIN,                   LoginController.be_login, Core.engine);
        post        (Routes.BE_LOGIN_DO,                LoginController.be_login_do);
        get         (Routes.BE_SIGNUP,                  LoginController.be_signup, Core.engine);
        post        (Routes.BE_SIGNUP_SAVE,             LoginController.be_signup_save);
        get         (Routes.BE_LOGOUT,                  LoginController.be_logout, Core.engine);

        // dashboard
        get         (Routes.BE_DASHBOARD,               DashboardController.be_dashboard, Core.engine);

        // settings
        get         (Routes.BE_SETTINGS_LANGUAGES,      SettingsController.be_settings_languages, Core.engine);
        post        (Routes.BE_SETTINGS_SAVE,           SettingsController.be_settings_save);

        get         (Routes.BE_SETTINGS_COMPANY,        CompanyController.be_company, Core.engine);
        post        (Routes.BE_SETTINGS_COMPANY_SAVE,   CompanyController.be_company_save);

        get         (Routes.BE_SETTINGS_SMTP_COLLECTION,SmtpController.be_smtp_collection, Core.engine);
        get         (Routes.BE_SETTINGS_SMTP,           SmtpController.be_smtp, Core.engine);
        get         (Routes.BE_SETTINGS_SMTP_DATA,      SmtpController.be_smtp_data);
        post        (Routes.BE_SETTINGS_SMTP_SAVE,      SmtpController.be_smtp_save);

        get         (Routes.BE_SETTINGS_USER_COLLECTION,SettingsUserController.be_settings_user_collection, Core.engine);
        get         (Routes.BE_SETTINGS_USER,           SettingsUserController.be_settings_user, Core.engine);
        get         (Routes.BE_SETTINGS_USER_DATA,      SettingsUserController.be_settings_user_data);
        post        (Routes.BE_SETTINGS_USER_SAVE,      SettingsUserController.be_settings_user_save);
        
        // user
        get         (Routes.BE_USER_COLLECTION,         UserController.be_user_collection, Core.engine);
        get         (Routes.BE_USER,                    UserController.be_user, Core.engine);
        get         (Routes.BE_USER_DATA,               UserController.be_user_data);
        post        (Routes.BE_USER_SAVE,               UserController.be_user_save);
        post        (Routes.BE_USER_OPERATE,            UserController.be_user_operate);

        // business
        get         (Routes.BE_BUSINESS_COLLECTION,     BusinessController.be_business_collection, Core.engine);
        get         (Routes.BE_BUSINESS,                BusinessController.be_business, Core.engine);
        get         (Routes.BE_BUSINESS_DATA,           BusinessController.be_business_data);
        post        (Routes.BE_BUSINESS_OPERATE,        BusinessController.be_business_operate);
        
        // blog
        get         (Routes.BE_ARTICLE_COLLECTION,      ArticleController.be_article_collection, Core.engine);
        get         (Routes.BE_ARTICLE,                 ArticleController.be_article, Core.engine);
        get         (Routes.BE_ARTICLE_DATA,            ArticleController.be_article_data);
        post        (Routes.BE_ARTICLE_SAVE,            ArticleController.be_article_save);
        post        (Routes.BE_ARTICLE_OPERATE,         ArticleController.be_article_operate);

        // event
        get         (Routes.BE_EVENT_COLLECTION,        EventController.be_event_collection, Core.engine);
        get         (Routes.BE_EVENT,                   EventController.be_event, Core.engine);
        get         (Routes.BE_EVENT_DATA,              EventController.be_event_data);
        post        (Routes.BE_EVENT_SAVE,              EventController.be_event_save);
        post        (Routes.BE_EVENT_OPERATE,           EventController.be_event_operate);

        // project
        get         (Routes.BE_PROJECT_COLLECTION,      ProjectController.be_project_collection, Core.engine);
        get         (Routes.BE_PROJECT,                 ProjectController.be_project, Core.engine);
        get         (Routes.BE_PROJECT_DATA,            ProjectController.be_project_data);
        post        (Routes.BE_PROJECT_SAVE,            ProjectController.be_project_save);
        post        (Routes.BE_PROJECT_OPERATE,         ProjectController.be_project_operate);

        
        // gallery
        get         (Routes.BE_PHOTO_COLLECTION,        PhotoController.be_photo_collection, Core.engine);
        get         (Routes.BE_PHOTO,                   PhotoController.be_photo, Core.engine);
        get         (Routes.BE_PHOTO_DATA,              PhotoController.be_photo_data);
        post        (Routes.BE_PHOTO_SAVE,              PhotoController.be_photo_save);
        post        (Routes.BE_PHOTO_OPERATE,           PhotoController.be_photo_operate);
        
        // category
        get         (Routes.BE_CATEGORY_COLLECTION,     CategoryController.be_category_collection, Core.engine);
        get         (Routes.BE_CATEGORY,                CategoryController.be_category, Core.engine);
        get         (Routes.BE_CATEGORY_DATA,           CategoryController.be_category_data);
        post        (Routes.BE_CATEGORY_SAVE,           CategoryController.be_category_save);
        post        (Routes.BE_CATEGORY_OPERATE,        CategoryController.be_category_operate);

        // mailtemplate
        get         (Routes.BE_MAILTEMPLATE_COLLECTION, MailtemplateController.be_mailtemplate_collection, Core.engine);
        get         (Routes.BE_MAILTEMPLATE,            MailtemplateController.be_mailtemplate, Core.engine);
        get         (Routes.BE_MAILTEMPLATE_DATA,       MailtemplateController.be_mailtemplate_data);
        post        (Routes.BE_MAILTEMPLATE_SAVE,       MailtemplateController.be_mailtemplate_save);

        // contact
        get         (Routes.BE_CONTACT_COLLECTION,      ContactController.be_contact_collection, Core.engine);
        get         (Routes.BE_CONTACT,                 ContactController.be_contact, Core.engine);
        get         (Routes.BE_CONTACT_DATA,            ContactController.be_contact_data);

        // script
        get         (Routes.BE_SCRIPT_COLLECTION,       ScriptController.be_script_collection, Core.engine);
        get         (Routes.BE_SCRIPT,                  ScriptController.be_script, Core.engine);
        get         (Routes.BE_SCRIPT_DATA,             ScriptController.be_script_data);
        post        (Routes.BE_SCRIPT_SAVE,             ScriptController.be_script_save);

        // label
        get         (Routes.BE_LABELS,                  LabelController.be_labels, Core.engine);
        get         (Routes.BE_LABELS_DATA,             LabelController.be_labels_data);
        post        (Routes.BE_LABELS_SAVE,             LabelController.be_labels_save);

        // path
        get         (Routes.BE_PATHS,                   PathController.be_paths, Core.engine);
        get         (Routes.BE_PATHS_DATA,              PathController.be_paths_data);
        post        (Routes.BE_PATHS_SAVE,              PathController.be_paths_save);

        /*  DA FIXARE */

        // smtp
        post        (Routes.BE_SEND_MAIL,              SmtpController.send_mail);

        // slider
        get         (Routes.BE_SLIDER,              SliderController.slider, Core.engine);
        get         (Routes.BE_SLIDER_EDIT,         SliderController.slider_edit, Core.engine);
        post        (Routes.BE_SLIDER_EDIT_SAVE,    SliderController.slider_edit_save);

        // files
        get         (Routes.BE_IMAGE,               ImageController.image);
        post        (Routes.BE_IMAGE_SAVE,          ImageController.image_save);
        get         (Routes.BE_IMAGE_BLOB,          ImageController.image_blob);

        // home
        get         ("HOME",                        HomeController.home, Core.engine);
        get         ("BUSINESS_DETAIL",             HomeController.business_detail, Core.engine);        
        get         ("COMING_SOON",                 HomeController.coming_soon, Core.engine);        
        get         ("ABOUT",                       HomeController.about, Core.engine);
        get         ("MENU",                        HomeController.menu, Core.engine);
        get         ("SERVICES",                    HomeController.services, Core.engine);
        get         ("PHOTO_COLLECTION",            HomeController.photo_collection, Core.engine);
//        get         ("PHOTO_CATEGORY",              HomeController.photo_category, Core.engine);
        get         ("PHOTO",                       HomeController.photo, Core.engine);
//        get         ("NEWS_COLLECTION",             HomeController.news_collection, Core.engine);
//        get         ("NEWS",                        HomeController.news, Core.engine);
        get         ("CONTACTS",                    HomeController.contacts, Core.engine);
        get         ("JOB"     ,                    HomeController.job, Core.engine);

        get         ("404",                         HomeController.error_404, Core.engine);
        
        get         ("ACCOUNT_LOGIN",               AccountController.account_login, Core.engine);        
        post        ("LOGIN_DO",                    AccountController.login_do);
        get         ("ACCOUNT_REGISTER",            AccountController.account_register, Core.engine);        
        post        ("REGISTER_DO",                 AccountController.register_do);
        get         ("ACCOUNT_LOGOUT",              AccountController.account_logout, Core.engine);
        get         ("ACCOUNT_RECOVER",             AccountController.account_recover, Core.engine);        
        post        ("RECOVER_SEND",                AccountController.recover_send);
        get         ("ACCOUNT_VERIFY",              AccountController.account_verify, Core.engine);                
        get         ("ACCOUNT_INFO",                AccountController.account_info, Core.engine);
        post        ("ACCOUNT_INFO_SAVE",           AccountController.account_info_save);
        post        ("ACCOUNT_PASSWORD_SAVE",       AccountController.account_password_save);
        post        ("ACCOUNT_DELETE",              AccountController.account_delete);
        post        ("ACCOUNT_IMAGE_SAVE",          AccountController.account_image_save);
        get         ("ACCOUNT_FAVOURITES",          AccountController.account_favourites, Core.engine);
        get         ("ACCOUNT_BUSINESSES",          AccountController.account_businesses, Core.engine);
        get         ("ACCOUNT_BUSINESS_EDIT",       BusinessController.account_business_edit, Core.engine);
        post        ("ACCOUNT_BUSINESS_EDIT_SAVE",  BusinessController.account_business_edit_save);
        post        ("ACCOUNT_BUSINESS_DRAFT_CHOICE", BusinessController.account_business_draft_choice);
        get         ("ACCOUNT_BUSINESS_INFO",       AccountController.account_business_info, Core.engine);
        get         ("ACCOUNT_BUSINESS_SERVICES",   AccountController.account_business_services, Core.engine);
        get         ("ACCOUNT_BUSINESS_BOOKINGS",   AccountController.account_business_bookings, Core.engine);
        get         ("ACCOUNT_BUSINESS_REVIEWS",    AccountController.account_business_reviews, Core.engine);

        get         ("RESULTS",                     ResultController.results, Core.engine);
        post        ("RESULTS_DATA",                ResultController.results_data, Core.engine);

    }

    public static Filter defaultPageRedirect = (Request request, Response response) -> {
        response.redirect(RoutesUtils.contextPath(request) + "/it/coming-soon");
        Spark.halt();
    };

    @Override
    public void destroy() {
        Core.destroy();
    }

    private static void get(String path, Route route) {
        List<String> localizedPaths = Routes.getLocalizedPath(path);
        for (String localizedPath : localizedPaths) {
            Spark.get(localizedPath, route);
        }
    }

    private static void get(String path, TemplateViewRoute route, TemplateEngine engine) {
        List<String> localizedPaths = Routes.getLocalizedPath(path);
        for (String localizedPath : localizedPaths) {
            if (StringUtils.isNotBlank(localizedPath)) {
                if (reloadApplication) {
                    Spark.unmap(localizedPath);
                } else {
                    Spark.get(localizedPath, route, engine);
                }
            }
        }
    }

    private static void post(String path, Route route) {
        List<String> localizedPaths = Routes.getLocalizedPath(path);
        for (String localizedPath : localizedPaths) {
            if (StringUtils.isNotBlank(localizedPath)) {
                if (reloadApplication) {
                    Spark.unmap(localizedPath);
                } else {
                    Spark.post(localizedPath, route);
                }
            }
        }
    }

    private static void post(String path, TemplateViewRoute route, TemplateEngine engine) {
        List<String> localizedPaths = Routes.getLocalizedPath(path);
        for (String localizedPath : localizedPaths) {
            if (StringUtils.isNotBlank(localizedPath)) {
                if (reloadApplication) {
                    Spark.unmap(localizedPath);
                } else {
                    Spark.post(localizedPath, route, engine);
                }
            }
        }
    }

//    private void get(String path, Route route) {
//        String[] paths = Paths.localizedPaths(path);
//        for (String pth : paths) {
//            Spark.get(pth, route);
//        }
//    }
//
//    private void getHtml(String path, TemplateViewRoute route) {
//        getHtml(path, route, FlashMessageType.none);
//    }
//
//    private void getHtml(String path, TemplateViewRoute route, FlashMessageType flash) {
//        String[] paths = Paths.localizedPaths(path);
//        for (String pth : paths) {
//            Spark.get(pth, route, Manager.engine);
//            if (flash != null) switch (flash) {
//                case successAndError:
//                    Spark.get(Paths.success(pth), route, Manager.engine);
//                    Spark.get(Paths.error(pth), route, Manager.engine);
//                    break;
//                case success:
//                    Spark.get(Paths.success(pth), route, Manager.engine);
//                    break;
//                case error:
//                    Spark.get(Paths.error(pth), route, Manager.engine);
//                    break;
//                default:
//                    break;
//            }
//        }
//    }
//
//    private void getJson(String path, Route route) {
//        String[] paths = Paths.localizedPaths(path);
//        for (String pth : paths) {
//            Spark.get(pth, route, Manager.jsonTransformer);
//        }
//    }
//
//    private void postJson(String path, Route route) {
//        String[] paths = Paths.localizedPaths(path);
//        for (String pth : paths) {
//            Spark.post(pth, route, Manager.jsonTransformer);
//        }
//    }
//
//    private void post(String path, Route route) {
//        String[] paths = Paths.localizedPaths(path);
//        for (String pth : paths) {
//            Spark.post(pth, route);
//        }
//    }
}
