package core;

import dao.BaseDao;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Path;
import pojo.PathItem;
import utils.Defaults;

/**
 *
 * <AUTHOR>
 */
public class Routes {

    private static final Logger LOGGER = LoggerFactory.getLogger(Routes.class.getName());

    // login / signup
    public static final String BE_LOGIN = "/login";
    public static final String BE_LOGIN_DO = "/login/do";
    public static final String BE_SIGNUP = "/signup";
    public static final String BE_SIGNUP_SAVE = "/signup/save";
    public static final String BE_LOGOUT = "/logout";

    // dashboard
    public static final String BE_DASHBOARD = "/be/dashboard";

    // settings
    public static final String BE_SETTINGS_LANGUAGES = "/be/settings/languages";
    public static final String BE_SETTINGS_SAVE = "/be/settings/save";

    public static final String BE_SETTINGS_COMPANY = "/be/settings/company";
    public static final String BE_SETTINGS_COMPANY_SAVE = "/be/settings/company/save";

    public static final String BE_SETTINGS_SMTP_COLLECTION = "/be/settings/smtps";
    public static final String BE_SETTINGS_SMTP = "/be/settings/smtp";
    public static final String BE_SETTINGS_SMTP_DATA = "/be/settings/smtp/data";
    public static final String BE_SETTINGS_SMTP_SAVE = "/be/settings/smtp/save";

    public static final String BE_SETTINGS_USER_COLLECTION = "/be/settings/users";
    public static final String BE_SETTINGS_USER = "/be/settings/user";
    public static final String BE_SETTINGS_USER_DATA = "/be/settings/user/data";
    public static final String BE_SETTINGS_USER_SAVE = "/be/settings/user/save";

    public static final String BE_SEND_MAIL = "/send/mail";

    // user
    public static final String BE_USER_COLLECTION = "/be/users";
    public static final String BE_USER = "/be/user";
    public static final String BE_USER_DATA = "/be/user/data";
    public static final String BE_USER_SAVE = "/be/user/save";
    public static final String BE_USER_OPERATE = "/be/user/operate";

    // business
    public static final String BE_BUSINESS_COLLECTION = "/be/businesses";
    public static final String BE_BUSINESS = "/be/business";
    public static final String BE_BUSINESS_DATA = "/be/business/data";
    public static final String BE_BUSINESS_SAVE = "/be/business/save";
    public static final String BE_BUSINESS_OPERATE = "/be/business/operate";

    // blog
    public static final String BE_ARTICLE_COLLECTION = "/be/articles";
    public static final String BE_ARTICLE = "/be/article";
    public static final String BE_ARTICLE_DATA = "/be/article/data";
    public static final String BE_ARTICLE_SAVE = "/be/article/save";
    public static final String BE_ARTICLE_OPERATE = "/be/article/operate";
    
    // event
    public static final String BE_EVENT_COLLECTION = "/be/events";
    public static final String BE_EVENT = "/be/event";
    public static final String BE_EVENT_DATA = "/be/event/data";
    public static final String BE_EVENT_SAVE = "/be/event/save";
    public static final String BE_EVENT_OPERATE = "/be/event/operate";

    // project
    public static final String BE_PROJECT_COLLECTION = "/be/projects";
    public static final String BE_PROJECT = "/be/project";
    public static final String BE_PROJECT_DATA = "/be/project/data";
    public static final String BE_PROJECT_SAVE = "/be/project/save";
    public static final String BE_PROJECT_OPERATE = "/be/project/operate";

    // gallery
    public static final String BE_PHOTO_COLLECTION = "/be/photos";
    public static final String BE_PHOTO = "/be/photo";
    public static final String BE_PHOTO_DATA = "/be/photo/data";
    public static final String BE_PHOTO_SAVE = "/be/photo/save";
    public static final String BE_PHOTO_OPERATE = "/be/photo/operate";

    // category
    public static final String BE_CATEGORY_COLLECTION = "/be/categories";
    public static final String BE_CATEGORY = "/be/category";
    public static final String BE_CATEGORY_DATA = "/be/category/data";
    public static final String BE_CATEGORY_SAVE = "/be/category/save";
    public static final String BE_CATEGORY_OPERATE = "/be/category/operate";

    // mailtemplate
    public static final String BE_MAILTEMPLATE_COLLECTION = "/be/mailtemplates";
    public static final String BE_MAILTEMPLATE = "/be/mailtemplate";
    public static final String BE_MAILTEMPLATE_DATA = "/be/mailtemplate/data";
    public static final String BE_MAILTEMPLATE_SAVE = "/be/mailtemplate/save";

    // BE_CONTACTS_COLLECTION
    public static final String BE_CONTACT_COLLECTION = "/be/contacts";
    public static final String BE_CONTACT = "/be/contact";
    public static final String BE_CONTACT_DATA = "/be/contact/data";

    // script
    public static final String BE_SCRIPT_COLLECTION = "/be/settings/scripts";
    public static final String BE_SCRIPT = "/be/settings/script";
    public static final String BE_SCRIPT_DATA = "/be/settings/script/data";
    public static final String BE_SCRIPT_SAVE = "/be/settings/script/save";

    // label
    public static final String BE_LABELS = "/be/labels";
    public static final String BE_LABELS_DATA = "/be/labels/data";
    public static final String BE_LABELS_SAVE = "/be/labels/save";

    // paths
    public static final String BE_PATHS = "/be/paths";
    public static final String BE_PATHS_DATA = "/be/paths/data";
    public static final String BE_PATHS_SAVE = "/be/paths/save";

    /*  DA FIXARE */
    // slider
    public static final String BE_SLIDER = "/be/slider";
    public static final String BE_SLIDER_EDIT = "/be/slider/edit";
    public static final String BE_SLIDER_EDIT_SAVE = "/be/slider/edit/save";

    // files
    public static final String BE_IMAGE = "/be/image";
    public static final String BE_IMAGE_SAVE = "/be/image/save";
    public static final String BE_IMAGE_BLOB = "/be/image/blob";

//    public static final String PHOTO = "/opere/:identifier";
    public static Map<String, String> paths = new HashMap<>();
    public static Map<String, String> reversedPaths = new HashMap<>();

    public static Map<String, String> getPaths() {
        if (paths.isEmpty()) { // carico solo se bisogna
            reloadPaths();
        }

        return paths;
    }

    public static Map<String, String> getReversedPaths() {
        if (reversedPaths.isEmpty()) { // carico solo se bisogna
            reloadPaths();
        }

        return reversedPaths;
    }

    public static void reloadPaths() {
        paths = new HashMap<>();
        reversedPaths = new HashMap<>();
        try {
            Routes routes = new Routes();
            Field[] declaredFields = Routes.class.getDeclaredFields();
            for (Field field : declaredFields) {
                if (Modifier.isStatic(field.getModifiers())) {
                    paths.put(field.getName(), field.get(routes).toString());
                }
            }

            // caricamento da DB
            List<Path> databasePaths = BaseDao.getDocuments(Path.class);
            if (databasePaths != null && !databasePaths.isEmpty()) {
                for (Path path : databasePaths) {
                    if (path.getItems() != null && !path.getItems().isEmpty()) {
                        for (PathItem pathInLanguage : path.getItems()) {
                            if (Defaults.AVAILABLE_LANGUAGES.size() == 1) {
                                // niente prefisso della lingua
                                if (path.getItems().size() == 1) {
                                    paths.put(StringUtils.upperCase(path.getKey()), pathInLanguage.getDescription());
                                }
                            } else {
                                paths.put(StringUtils.upperCase(path.getKey() + "_" + pathInLanguage.getLanguage()), pathInLanguage.getDescription());
                            }
                        }
                    }
                }
            }

            for (String pathKey : paths.keySet()) {
                reversedPaths.put(paths.get(pathKey), pathKey);
            }

        } catch (IllegalAccessException | IllegalArgumentException | SecurityException ex) {
            LOGGER.error("Routes.getPaths(), can't populate paths. Error: " + ex.getMessage());
        } catch (Exception ex) {
            LOGGER.error("Routes.getPaths(), can't populate paths. Error: " + ex.getMessage());
        }
    }

    public static List<String> getLocalizedPath(String key) {
        Map<String, String> allPaths = getPaths();
        Map<String, String> allReversedPaths = getReversedPaths();

        List<String> localizedPaths = new ArrayList<>();
        if (Defaults.AVAILABLE_LANGUAGES.size() == 1) {
            localizedPaths.add(allPaths.get(key));
        } else {
            boolean oneLanguageFound = false;
            for (String language : Defaults.AVAILABLE_LANGUAGES) {
                String localizedPath = allPaths.get(StringUtils.upperCase(key + "_" + language).replaceAll("/", ""));
                if (StringUtils.isNotBlank(localizedPath)) {
                    localizedPaths.add(localizedPath);
                    oneLanguageFound = true;
                }
            }

            if (!oneLanguageFound) {
                localizedPaths.add(allPaths.get(allReversedPaths.get(key)));
            }
        }

        return localizedPaths;
    }

    public static String getLocalizedPathByLanguage(String key, String language) {
        Map<String, String> allPaths = getPaths();
        Map<String, String> allReversedPaths = getReversedPaths();

        List<String> localizedPaths = new ArrayList<>();
        if (Defaults.AVAILABLE_LANGUAGES.size() == 1) {
            localizedPaths.add(allPaths.get(key));
        } else {
            List<String> languages = new ArrayList<>();
            if (StringUtils.isNotBlank(language)) {
                languages.add(language);
            } else {
                languages = Defaults.AVAILABLE_LANGUAGES;
            }

            boolean oneLanguageFound = false;
            for (String tmpLanguage : languages) {
                String localizedPath = allPaths.get(StringUtils.upperCase(key + "_" + tmpLanguage).replaceAll("/", ""));
                if (StringUtils.isNotBlank(localizedPath)) {
                    localizedPaths.add(localizedPath);
                    oneLanguageFound = true;
                }
            }

            if (!oneLanguageFound) {
                localizedPaths.add(allPaths.get(allReversedPaths.get(key)));
            }
        }

        if (!localizedPaths.isEmpty()) {
            return localizedPaths.get(0);
        } else {
            return "/" + StringUtils.defaultIfBlank(language, Defaults.LANGUAGE) + "/404";
        }
    }

//    public static Map<String, String> getPaths() {
//        if (paths.isEmpty()) { // carico solo se bisogna
//            try {
//                List<Path> pathsInDb = BaseDao.getDocuments(Path.class, Defaults.LANGUAGE);
//                for (Path path : pathsInDb) {
//                    paths.put(path.getKey(), path.getUrl());
//                }
//            } catch (Exception ex) {
//                LOGGER.error("Routes.getPaths(), can't populate paths. Error: " + ex.getMessage());
//            }
//        }
//
//        return paths;
//    }
}
