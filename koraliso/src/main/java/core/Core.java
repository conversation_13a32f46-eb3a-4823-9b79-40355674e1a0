package core;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.mitchellbosecke.pebble.loader.ServletLoader;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientOptions;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoDatabase;
import dao.BaseDao;
import enums.ProfileType;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import javax.servlet.ServletContext;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Company;
import pojo.Settings;
import pojo.User;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import spark.ModelAndView;
import spark.Request;
import spark.Response;
import utils.Defaults;
import utils.EnvironmentUtils;
import utils.RoutesUtils;

/**
 *
 * <AUTHOR>
 */
public class Core {

    // Core.engine.getEngine().getTemplateCache().invalidateAll();
    private static final Logger LOGGER = LoggerFactory.getLogger(Core.class.getName());

    // mongodb
    private static MongoClient mongoClient;
    public static MongoDatabase mongoDatabase;

    // redis
    private static JedisPool redisPool;

    // pebble
    public static PebbleTemplateEngine engine;

    // json
    private static ObjectMapper mapper;

    // build number
    private static String buildNumber;

    public static void start(ServletContext servletContext) {
        // mongodb
        MongoClientOptions options = MongoClientOptions.builder()
                .serverSelectionTimeout(1000)
                .connectTimeout(10000)
                .socketTimeout(60000)
                .build();
        mongoClient = new com.mongodb.MongoClient("localhost", options);
        mongoDatabase = mongoClient.getDatabase(Defaults.PROJECT_NAME);

        // redis
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setTestOnBorrow(true);
        redisPool = new JedisPool(poolConfig, Defaults.REDIS_HOSTNAME);

        // json
        mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SimpleModule module = new SimpleModule();
        module.addSerializer(Date.class, new DateJsonSerializer());
        module.addDeserializer(Date.class, new DateJsonDeserializer());
        module.addSerializer(ObjectId.class, new ObjectIdJsonSerializer());
        module.addDeserializer(ObjectId.class, new ObjectIdJsonDeserializer());
        mapper.registerModule(module);

        // pebble
        engine = new PebbleTemplateEngine(new ServletLoader(servletContext), EnvironmentUtils.isLocal());

        // settings (languages)
        try {
            Settings settings = BaseDao.getDocumentByClass(Settings.class);
            boolean loadDefaultLanguage = false;
            boolean loadDefaultVisibleLanguage = false;
            if (settings != null) {
                if (settings.getAvailableLanguages(true) != null && !settings.getAvailableLanguages(true).isEmpty()) {
                    Defaults.AVAILABLE_LANGUAGES = settings.getAvailableLanguages(true);
                } else {
                    loadDefaultLanguage = true;
                }
                if (settings.getVisibleLanguages() != null && !settings.getVisibleLanguages().isEmpty()) {
                    Defaults.VISIBLE_LANGUAGES = settings.getVisibleLanguages();
                } else {
                    loadDefaultVisibleLanguage = true;
                }
            } else {
                loadDefaultLanguage = true;
                loadDefaultVisibleLanguage = true;
            }

            if (loadDefaultLanguage) {
                Defaults.AVAILABLE_LANGUAGES.add(Defaults.LANGUAGE);
            }
            if (loadDefaultVisibleLanguage) {
                Defaults.VISIBLE_LANGUAGES.add(Defaults.LANGUAGE);
            }

            buildNumber = getBuildNumber(servletContext);
        } catch (Exception ex) {
            LOGGER.error("Unable to load settings", ex);
        }
    }

    public static void destroy() {
        // mongodb
        if (mongoClient != null) {
            mongoClient.close();
        }
    }

    public static ModelAndView render(String templateName, Map<String, Object> viewAttributes, Request request) {
        Map<String, Object> attributes = new HashMap<>();
        if (viewAttributes != null) {
            attributes.putAll(viewAttributes);
        }

        attributes.put("contextPath", ((EnvironmentUtils.isLocal() || EnvironmentUtils.hasNotDomainRedirect(request)) ? "/" + Defaults.PROJECT_NAME : ""));
        attributes.put("host", request != null ? request.headers("Host") : null);
        attributes.put("allLanguages", Defaults.ALL_LANGUAGES);
        attributes.put("availableLanguages", Defaults.AVAILABLE_LANGUAGES);
        attributes.put("defaultUserLanguage", Defaults.DEFAULT_USER_LANGUAGE);

        return new ModelAndView(attributes, templateName);
    }

    public static void createSession(Request request, Response response, String token) {
        if (StringUtils.isNotBlank(token)) {
            Map<String, String> hash = new HashMap<>();
            hash.put("creation", serializeToJson(new Date()));
            hash.put("ip", StringUtils.defaultIfBlank(request.headers("X-Forwarded-For"), StringUtils.defaultString(request.ip())));
            hash.put("userAgent", StringUtils.defaultString(request.userAgent()));
            try ( Jedis redis = redisPool.getResource()) {
                redis.hmset(Defaults.REDIS_PREFIX + token, hash);
                redis.expire(Defaults.REDIS_PREFIX + token, Defaults.SESSION_DURATION); // 8h in secondi
            }
            response.cookie(EnvironmentUtils.domain(request), "/", Defaults.USER_SESSION_COOKIE, token, Defaults.SESSION_DURATION, false, false);
//            response.cookie(Defaults.USER_SESSION_COOKIE, token);
        }
    }

    public static void addValueToSession(String token, String key, Object value) {
        if (StringUtils.isNotBlank(token)) {
            if (StringUtils.isNotBlank(key)) {
                String json = serializeToJson(value);
                if (StringUtils.isNotBlank(json)) {
                    try ( Jedis redis = redisPool.getResource()) {
                        redis.hset(Defaults.REDIS_PREFIX + token, key, json);
                    }
                }
            }
        }
    }

    public static void destroySession(Request request, Response response) {
        String token = null;
        if (request != null) {
            token = request.cookies().get(Defaults.USER_SESSION_COOKIE);
        }

        if (StringUtils.isNotBlank(token)) {
            try ( Jedis redis = redisPool.getResource()) {
                redis.del(Defaults.REDIS_PREFIX + token);
            }
            response.removeCookie(Defaults.USER_SESSION_COOKIE);
            response.removeCookie(Defaults.USER_SESSION_COOKIE);
        }
    }

    // usata nelle call ajax
    public static User getUserFromRequest(Request request) {
        String cookie = null;
        if (request != null) {
            cookie = request.cookies().get(Defaults.USER_SESSION_COOKIE);
        }

        if (cookie != null) {
            String json;
            try ( Jedis redis = redisPool.getResource()) {
                json = redis.hget(Defaults.REDIS_PREFIX + cookie, "user");
            }
            if (StringUtils.isNotBlank(json)) {
                return deserializeFromJson(json, User.class);
            }
        }
        return null;
    }

    public static String getSessionToken(Request request) {
        String cookie = null;
        if (request != null) {
            cookie = request.cookies().get(Defaults.USER_SESSION_COOKIE);
        }
        return cookie;
    }

    public static User initializeRouteFrontEnd(Request request, Response response, Map<String, Object> attributes) {
        return initializeRoute(request, response, attributes, true, null);
    }
    public static User initializeRouteFrontEnd(Request request, Response response, Map<String, Object> attributes, ProfileType minRole) {
        return initializeRoute(request, response, attributes, true, minRole);
    }
    public static User initializeRoute(Request request, Response response, Map<String, Object> attributes) {
        return initializeRoute(request, response, attributes, false, null);
    }
    public static User initializeRoute(Request request, Response response, Map<String, Object> attributes, ProfileType minRole) {
        return initializeRoute(request, response, attributes, false, minRole);
    }
    // usata nelle rotte per verificare che l'utente sia loggato
    public static User initializeRoute(Request request, Response response, Map<String, Object> attributes, boolean isFrontEnd, ProfileType minRole) {
        User user = Core.getUserFromRequest(request);
        if (user == null && !isFrontEnd) {
            response.redirect(RoutesUtils.contextPath(request) + Routes.BE_LOGIN);
        }
        if (minRole != null) {
            if (user != null) {
                int userRoleValue = ProfileType.getValueForRole(user.getProfileType());
                if (userRoleValue < minRole.getValue()) {
                    if (isFrontEnd) {
                        response.redirect(RoutesUtils.getLocalizedFullPath(request, "HOME", user.getLanguage()));
                    } else {
                        response.redirect(RoutesUtils.contextPath(request) + Routes.BE_LOGIN);
                    }
                }
            } else {
                if (isFrontEnd) {
                    response.redirect(RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_LOGIN", null));
                } else {
                    response.redirect(RoutesUtils.contextPath(request) + Routes.BE_LOGIN);
                }
            }
        }
        attributes.put("user", user);

        try {
            attributes.put("cmp", BaseDao.getDocumentByClass(Company.class));
        } catch (Exception ex) {
            // suppressed
        }
        if (Defaults.AVAILABLE_LANGUAGES.size() == 1) {
            attributes.put("language", Defaults.AVAILABLE_LANGUAGES.get(0));
        } else {
            String routeRequest = request.uri();
            List<String> parts = new ArrayList<>(Arrays.asList(StringUtils.split(routeRequest, "/")));
            if (parts.size() >= 1) {
                String language = parts.get(0);
                if (Defaults.AVAILABLE_LANGUAGES.contains(language)) {
                    attributes.put("language", language);
                } else {
                    attributes.put("language", Defaults.LANGUAGE);
                }
            }
        }
        attributes.put("baseUrl", RoutesUtils.baseUrl(request));
        attributes.put("recaptchaSite", Defaults.GOOGLE_RECAPTCHA_SITE);
        attributes.put("publicUrl", RoutesUtils.publicUrl(request));
        attributes.put("buildNumber", buildNumber);
        return user;
    }

    private static String getBuildNumber(ServletContext servletContext) {
        String build = null;
        String realPath = servletContext.getRealPath("/");

        // when running from netbeans, we don't have a manifest file
        File file = new File(realPath + "/" + Defaults.MANIFEST_NAME);
        if (file.exists()) {

            Properties prop = new Properties();
            try {
                try (InputStream res = servletContext.getResourceAsStream("/" + Defaults.MANIFEST_NAME)) {
                    prop.load(res);
                }
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            build = prop.getProperty("Implementation-Build");

        } else {
            LOGGER.warn("no build number when running from netbeans...");
        }

        return build;
    }

    public static <T> T fromDocument(Document document, Class<T> objectClass) {
        T result = null;
        if (document != null) {
            result = deserializeFromJson(document.toJson(), objectClass);
        }
        return result;
    }

    public static <T> List<T> fromDocumentList(FindIterable<Document> list, Class<T> objectClass) {
        List<T> result = null;
        if (list != null) {
            result = new ArrayList<>();
            for (Document document : list) {
                result.add(fromDocument(document, objectClass));
            }
        }
        return result;
    }

    public static Document toDocument(Object object) {
        Document result = null;
        if (object != null) {
            result = Document.parse(serializeToJson(object));
        }
        return result;
    }

    public static String serializeToJson(Object object) {
        try {
            return mapper.writeValueAsString(object);
        } catch (JsonProcessingException ex) {
            LOGGER.error("suppressed", ex);
        }
        return null;
    }

    public static <T> T deserializeFromJson(String json, Class<T> objectClass) {
        try {
            return mapper.readValue(json, objectClass);
        } catch (JsonProcessingException ex) {
            LOGGER.error("suppressed", ex);
        } catch (IOException ex) {
            LOGGER.error("suppressed", ex);
        }
        return null;
    }
}
