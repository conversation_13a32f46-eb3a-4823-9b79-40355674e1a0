package dao;

import com.mongodb.client.model.Filters;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.ne;
import com.mongodb.client.model.Sorts;
import java.util.List;
import org.bson.BsonDocument;
import org.bson.conversions.Bson;
import pojo.QueryOptions;

/**
 *
 * <AUTHOR>
 */
public class DaoFilters {

    public static Bson getFilter(String fieldName, DaoFiltersOperation operation, Object value) {
        Bson filter = null;

        if (null != operation) switch (operation) {
            case EQ:
                filter = eq(fieldName, value);
                break;
            case NE:
                filter = ne(fieldName, value);
                break;
            case GTE:
                filter = gte(fieldName, value);
                break;
            case LTE:
                filter = lte(fieldName, value);
                break;
            default:
                break;
        }

        return filter;
    }
    
    public static QueryOptions createQueryWithOptions(List<Bson> filters, int skip, int limit, String orderBy, String orderType) {
        Bson combinedFilter = null;
        if (filters != null) {
            combinedFilter = filters.isEmpty() ? new BsonDocument() : Filters.and(filters);
        }
        Bson sort = null;

        if (orderBy != null && !orderBy.isEmpty()) {
            sort = "desc".equalsIgnoreCase(orderType) ? Sorts.descending(orderBy) : Sorts.ascending(orderBy);
        }

        return new QueryOptions(combinedFilter, sort, skip, limit);
    }    
}
