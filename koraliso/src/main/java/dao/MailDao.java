package dao;

import static com.mongodb.MongoClient.getDefaultCodecRegistry;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import core.Core;
import java.security.InvalidParameterException;
import org.apache.commons.lang3.StringUtils;
import org.bson.codecs.configuration.CodecProvider;
import static org.bson.codecs.configuration.CodecRegistries.fromProviders;
import static org.bson.codecs.configuration.CodecRegistries.fromRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.ClassModel;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Mailtemplate;

/**
 *
 * <AUTHOR>
 */
public class MailDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(MailDao.class.getName());

    public static Mailtemplate getMailtemplateByKey(String key, String language) throws Exception {
        if (StringUtils.isBlank(key)) {
            throw new InvalidParameterException("key is empty. Can't continue");
        }

        ClassModel model = ClassModel.builder(Mailtemplate.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = Mailtemplate.class.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }
        MongoCollection<Mailtemplate> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName), Mailtemplate.class).withCodecRegistry(pojoCodecRegistry);
        return collection.find(and(eq("key", key), ne("cancelled", true), ne("archived", true)), Mailtemplate.class).first();
    }

}
