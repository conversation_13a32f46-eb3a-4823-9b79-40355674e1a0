package dao;

import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Filters.or;
import static com.mongodb.client.model.Filters.regex;
import core.Core;
import pojo.User;
import java.security.InvalidParameterException;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;

/**
 *
 * <AUTHOR>
 */
public class UserDao extends BaseDao {

    public static User loadUserByUsername(String username) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        
        MongoCollection<Document> collection = Core.mongoDatabase.getCollection("user");
        Document doc = collection.find(and(ne("cancelled", true), regex("username", Pattern.compile("(\\s|^)"+ username +"(\\s|$)", Pattern.CASE_INSENSITIVE)))).first();
        
        User user = null;
        if (doc != null) {
            user = Core.fromDocument(doc, User.class);
        }
        return user;
    }

    public static User loadUserByEmail(String email) throws Exception {
        if (StringUtils.isBlank(email)) {
            throw new InvalidParameterException("empty email");
        }

        MongoCollection<Document> collection = Core.mongoDatabase.getCollection("user");
        Document doc = collection.find(and(ne("cancelled", true), regex("email", Pattern.compile("(\\s|^)"+ email +"(\\s|$)", Pattern.CASE_INSENSITIVE)))).first();

        User user = null;
        if (doc != null) {
            user = Core.fromDocument(doc, User.class);
        }
        return user;
    }
}
