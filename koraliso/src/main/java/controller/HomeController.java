package controller;

import core.Core;
import core.Pages;
import dao.BaseDao;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Article;
import pojo.Photo;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.Defaults;
import utils.RoutesUtils;

/**
 *
 * <AUTHOR>
 */
public class HomeController {

    private static final Logger LOGGER = LoggerFactory.getLogger(HomeController.class.getName());

    public static Route error_page = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        if (StringUtils.isNotBlank((String) attributes.get("language"))) {
            response.redirect(RoutesUtils.contextPath(request) + "/" + attributes.get("language") + "/404");
        } else {
            response.redirect(RoutesUtils.contextPath(request) + "/" + Defaults.AVAILABLE_LANGUAGES.get(0) + "/404");
        }
        return "ok";
    };

    public static TemplateViewRoute error_404 = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ERROR_404, attributes, request);
    };

    public static TemplateViewRoute home = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.HOME, attributes, request);
    };
    
    public static TemplateViewRoute business_detail = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.BUSINESS_DETAIL, attributes, request);
    };
    
    public static TemplateViewRoute coming_soon = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.COMING_SOON, attributes, request);
    };

    public static TemplateViewRoute about = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ABOUT, attributes, request);
    };
    
    public static TemplateViewRoute menu = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.MENU, attributes, request);
    };
    
    public static TemplateViewRoute services = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.SERVICES, attributes, request);
    };

    public static TemplateViewRoute contacts = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.CONTACTS, attributes, request);
    };
    
    public static TemplateViewRoute job = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.JOB, attributes, request);
    };

    public static TemplateViewRoute photo_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.PHOTO_COLLECTION, attributes, request);
    };
    
    public static TemplateViewRoute photo_category = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // identifier
        String category = request.params("category");
        attributes.put("category", category);
        
        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.PHOTO_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute photo = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        // identifier
        String identifier = request.params("identifier");
        attributes.put("identifier", identifier);

        Photo photo = null;
        if (StringUtils.isNotBlank(identifier)) {
            photo = BaseDao.getDocumentByIdentifier(identifier, Photo.class, attributes.get("language").toString());
        }

        attributes.put("photo", photo);

        return Core.render(Pages.PHOTO, attributes, request);
    };

    public static TemplateViewRoute news_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.NEWS_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute news = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        // identifier
        String identifier = request.params("identifier");
        attributes.put("identifier", identifier);

        Article news = null;
        if (StringUtils.isNotBlank(identifier)) {
            news = BaseDao.getDocumentByIdentifier(identifier, Article.class, attributes.get("language").toString());
        }

        attributes.put("news", news);

        return Core.render(Pages.NEWS, attributes, request);
    };
    
}
