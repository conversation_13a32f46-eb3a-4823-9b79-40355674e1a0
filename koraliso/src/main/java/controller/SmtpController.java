package controller;

import com.mitchellbosecke.pebble.PebbleEngine;
import com.mitchellbosecke.pebble.loader.StringLoader;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.MailDao;
import enums.MailType;
import enums.ProfileType;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Contact;
import pojo.Mailtemplate;
import pojo.Smtp;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;
import utils.GoogleRecaptchaValidator;
import utils.MailUtils;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class SmtpController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmtpController.class.getName());

    public static TemplateViewRoute be_smtp_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_SETTINGS_SMTP_COLLECTION, attributes, request);
    };

    public static Route be_smtp_data = (Request request, Response response) -> {
        // logged user
        User user = Core.getUserFromRequest(request);
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Smtp> smtpList;
        if (loadArchived) {
            smtpList = BaseDao.getArchivedDocuments(Smtp.class);
        } else {
            smtpList = BaseDao.getDocuments(Smtp.class);
        }

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!smtpList.isEmpty()) {
            for (Smtp smtp : smtpList) {
                json.append("[");
                json.append("\"<a href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_SETTINGS_SMTP + "?smtpId=").append(smtp.getId()).append("'>").append(smtp.getHostname()).append("</a>\",");
                json.append("\"").append(smtp.getPort() != null ? smtp.getPort() : "").append("\",");
                json.append("\"").append(smtp.getAuthentication() != null ? smtp.getAuthentication() : "").append("\",");
                json.append("\"").append(smtp.getUsername() != null ? smtp.getUsername() : "").append("\",");
                json.append("\"").append(smtp.getEncryption() != null ? smtp.getEncryption() : "").append("\",");
                json.append("\"").append(smtp.getStartTls() != null ? smtp.getStartTls() : "").append("\",");
                json.append("\"").append(smtp.getSender() != null ? smtp.getSender() : "").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static TemplateViewRoute be_smtp = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes);
        if (user == null) {
            return Core.render(Pages.BE_LOGIN, attributes, request);
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("smtpId"));
        if (oid != null) {
            Smtp loadedSmtp = BaseDao.getDocumentById(oid, Smtp.class);
            attributes.put("smtp", loadedSmtp);
        }

        return Core.render(Pages.BE_SETTINGS_SMTP, attributes, request);
    };

    public static Route be_smtp_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);
        ObjectId oid = RequestUtils.toObjectId(request.queryParams("smtpId"));
        Smtp newSmtp;
        if (oid != null) {
            newSmtp = BaseDao.getDocumentById(oid, Smtp.class);
            RequestUtils.mergeFromParams(params, newSmtp);
        } else {
            newSmtp = RequestUtils.createFromParams(params, Smtp.class);
        }

        if (newSmtp != null) {
            if (!params.containsKey("authentication")) {
                newSmtp.setAuthentication(false);
            }
            if (!params.containsKey("encryption")) {
                newSmtp.setEncryption(false);
            }
            if (!params.containsKey("startTls")) {
                newSmtp.setStartTls(false);
            }

            if (oid == null) {
                BaseDao.insertDocument(newSmtp);
            } else {
                BaseDao.updateDocument(newSmtp);
            }
        }

        response.redirect(RoutesUtils.contextPath(request) + Routes.BE_SETTINGS_SMTP_COLLECTION);
        return null;
    };

    public static Route send_mail = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        // TODO: salvare i files in file temporanei e poi cancellarli dopo aver inviato la mail
        String to = null, object = null, content = null, language;
        Smtp smtp = null;
        boolean validMail = false;
        if (params.containsKey("recaptchaToken")) {
            if (GoogleRecaptchaValidator.isValid(params.get("recaptchaToken"))) {
                if (params.containsKey("type")) {
                    String type = params.get("type");
                    language = params.get("language");
                    if (StringUtils.isNotBlank(language)) {
                        if (EnumUtils.isValidEnum(MailType.class, type)) {
                            Mailtemplate template = MailDao.getMailtemplateByKey(type, language);

                            if (template != null) {
                                to = StringUtils.join(template.getTo(), ",");
                                object = template.getObject();
                                if (template.getSmtpId() != null) {
                                    smtp = BaseDao.getDocumentById(template.getSmtpId(), Smtp.class);
                                }
                                Map<String, Object> templateAttributes = new HashMap<>();
                                for (String attribute : params.keySet()) {
                                    templateAttributes.put(attribute, params.get(attribute));
                                }

                                StringWriter writer = new StringWriter();
                                PebbleEngine pebble = new PebbleEngine.Builder().loader(new StringLoader()).build();
                                pebble.getTemplate(template.getDescription()).evaluate(writer, templateAttributes);
                                content = writer.toString();

                                // se tutto ok allora mando mail
                                if (StringUtils.isNotBlank(to) && StringUtils.isNotBlank(object)
                                        && StringUtils.isNotBlank(content) && smtp != null) {
                                    validMail = true;
                                }
                            } else {
                                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "invalid template");
                            }
                        } else {
                            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "invalid type template");
                        }
                    } else {
                        throw Spark.halt(HttpStatus.BAD_REQUEST_400, "invalid language");
                    }
                }
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "invalid recaptcha");
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "invalid recaptcha");
        }

//        List<File> attachments = new ArrayList<>();
//        attachments.add(new File("D:/NewMonthly.png"));
        if (validMail) {
            MailUtils.sendMail(to, object, content, null, smtp);

            Contact contact = new Contact();
//            contact.setEmail(params.get("email"));
//            contact.setName(params.get("name"));
//            contact.setLastname(params.get("lastname"));
//            contact.setFullname(params.get("fullname"));
//            contact.setMessage(params.get("message"));
//            contact.setPhone(params.get("phone"));
            RequestUtils.mergeFromParams(params, contact);

            contact.setSubject(object);
            contact.setTemplate(params.get("type"));
            contact.setMail(content);
            try {
                BaseDao.insertDocument(contact);
            } catch (Exception ex) {
                LOGGER.error("unable save contact, execption is: ", ex);
            }

        } else {
            LOGGER.warn("Can't send the mail, one or more parameters are invalid");
        }

        return null;
    };
}
