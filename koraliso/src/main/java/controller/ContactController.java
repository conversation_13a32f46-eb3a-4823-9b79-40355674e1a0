package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import enums.ProfileType;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Contact;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.DateTimeUtils;
import utils.RequestUtils;
import utils.RoutesUtils;

/**
 *
 * <AUTHOR>
 */
public class ContactController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ContactController.class.getName());

    public static TemplateViewRoute be_contact_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_CONTACT_COLLECTION, attributes, request);
    };

    public static Route be_contact_data = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Contact> contactList;
        if (loadArchived) {
            contactList = BaseDao.getArchivedDocuments(Contact.class);
        } else {
            contactList = BaseDao.getDocuments(Contact.class);
        }

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!contactList.isEmpty()) {
            for (Contact contact : contactList) {
                json.append("[");
                json.append("\"<a href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_CONTACT + "?contactId=").append(contact.getId()).append("'>").append(contact.getTemplate()).append("</a>\",");
                json.append("\"").append(contact.getName()!= null ? contact.getName() : "").append("\",");
                json.append("\"").append(contact.getLastname()!= null ? contact.getLastname(): "").append("\",");
                json.append("\"").append(contact.getFullname()!= null ? contact.getFullname(): "").append("\",");
                json.append("\"").append(contact.getEmail()!= null ? contact.getEmail(): "").append("\",");
                json.append("\"").append(contact.getSubject()!= null ? contact.getSubject(): "").append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(contact.getCreation(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static TemplateViewRoute be_contact = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        ObjectId oid = RequestUtils.toObjectId(request.queryParams("contactId"));
        if (oid != null) {
            Contact loadedContact = BaseDao.getDocumentById(oid, Contact.class);
            attributes.put("contact", loadedContact);
        }

        return Core.render(Pages.BE_CONTACT, attributes, request);
    };

}
