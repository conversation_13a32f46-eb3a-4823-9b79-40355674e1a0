package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.ProfileType;
import enums.StatusType;
import extensions.LabelsFunction;

import java.sql.Array;
import java.util.*;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Business;
import pojo.Project;
import pojo.QueryOptions;
import pojo.User;
import spark.*;
import utils.*;

/**
 *
 * <AUTHOR>
 */
public class BusinessController {

    private static final Logger LOGGER = LoggerFactory.getLogger(BusinessController.class.getName());

    public static TemplateViewRoute be_business_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_BUSINESS_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_business = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("businessId"));
        if (oid != null) {
            Business loadedBusiness = BaseDao.getDocumentById(oid, Business.class);
            attributes.put("business", loadedBusiness);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Business loadedBusiness = BaseDao.getDocumentByParentId(parentId, Business.class);
                if (loadedBusiness != null) {
                    attributes.put("business", loadedBusiness);
                }
            }
        }

        return Core.render(Pages.BE_BUSINESS, attributes, request);
    };

    public static Route be_business_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Business> loadedBusinesses;
        if (loadArchived) {
            loadedBusinesses = BaseDao.getArchivedDocuments(Business.class);
        } else {
            loadedBusinesses = BaseDao.getDocuments(Business.class);
        }

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!loadedBusinesses.isEmpty()) {
            for (Business business : loadedBusinesses) {
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota
                json.append("\"<a target='_blank' businessId='").append(business.getId()).append("' href='").append(RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_BUSINESS_EDIT", null)).append("?oid=").append(business.getId()).append("&step=5'>").append(business.getFullname()).append("</a>\",");
                List<String> categoryList = new ArrayList<>();
                if (BooleanUtils.isTrue(business.getDiving())) {
                    categoryList.add("Diving");
                }
                if (BooleanUtils.isTrue(business.getFreedivingApnea())) {
                    categoryList.add("Freediving Apnea");
                }
                if (BooleanUtils.isTrue(business.getExperience())) {
                    categoryList.add("Experience");
                }
                if (BooleanUtils.isTrue(business.getSport())) {
                    categoryList.add("Sport");
                }
                json.append("\"").append(StringUtils.join(categoryList, ", ")).append("\",");
                json.append("\"").append(business.getStatus() != null ? business.getStatus() : "").append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(business.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static TemplateViewRoute account_business_edit = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        // TODO: CAMBIARE DA UNCONFIRMED A STANDARD (CONFERMATO TRAMITE MAIL)
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.CUSTOMER);

        if (user != null) {
            String language = RoutesUtils.language(request);
            Business business = null;
            ObjectId businessId = null;

            // Check if there's an "oid" parameter in the URL
            String oidParam = request.queryParams("oid");
            if (oidParam != null && !oidParam.trim().isEmpty() && !"-".equals(oidParam)) {
                try {
                    businessId = new ObjectId(oidParam);
                    business = BaseDao.getDocumentById(businessId, Business.class);

                    if (business == null) {
                        // Business not found, redirect to create new one
                        String redirectUrl = RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_BUSINESS_EDIT", language);
                        response.redirect(redirectUrl);
                        return null;
                    } else {
                        // check if the user can view/edit it
                        ProfileType minRole = ProfileType.OPERATOR;
                        int userRoleValue = ProfileType.getValueForRole(user.getProfileType());

                        if (business.getUserId() != null && !business.getUserId().equals(user.getId()) && userRoleValue < minRole.getValue()) {
                            String redirectUrl = RoutesUtils.getLocalizedFullPath(request, "HOME", language);
                            response.redirect(redirectUrl);
                            return null;
                        }
                    }
                } catch (IllegalArgumentException ex) {
                    // Invalid ObjectId, redirect to create new one
                    String redirectUrl = RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_BUSINESS_EDIT", language);
                    response.redirect(redirectUrl);
                    return null;
                }
            } else {
                // Check if there's any Business created by the user with status "draft"
                List<Bson> filters = new ArrayList<>();
                filters.add(DaoFilters.getFilter("userId", DaoFiltersOperation.EQ, user.getId()));
                filters.add(DaoFilters.getFilter("status", DaoFiltersOperation.EQ, "draft"));
                QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);
                business = BaseDao.getDocumentByFilters(Business.class, queryOptions);

                if (business != null) {
                    // Draft business found - ask user what to do
                    attributes.put("draftBusiness", business);
                    attributes.put("showDraftChoice", true);

                    // Don't redirect, show the draft choice page
                } else {
                    // Create new Business
                    business = new Business();
                    business.setStatus(StatusType.DRAFT.name().toLowerCase());
                    business.setUserId(user.getId());

                    try {
                        businessId = BaseDao.insertDocument(business);
                        business.setId(businessId);

                        // Redirect to the same page with the new business ID
                        String redirectUrl = RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_BUSINESS_EDIT", language);
                        redirectUrl += "?oid=" + businessId.toString();
                        response.redirect(redirectUrl);
                        return null;
                    } catch (Exception ex) {
                        LOGGER.error("Error creating new business", ex);
                        throw Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500, LabelsFunction.description(language, "error.unable.to.create.business"));
                    }
                }
            }

            // Add business to attributes for the template
            attributes.put("business", business);

            // manage gallery param
            attributes.put("gallery", StringUtils.isNotBlank(request.queryParams("gallery")));
        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        return Core.render(Pages.ACCOUNT_BUSINESS_EDIT, attributes, request);
    };

    public static Route account_business_edit_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        if (user != null) {
            String language = RoutesUtils.language(request);
            Map<String, UploadedFile> files = new HashMap<>();
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, files);

            // The oid parameter must be present in the form submission
            String oidParam = params.get("oid");
            if (oidParam == null || oidParam.trim().isEmpty() || "-".equals(oidParam)) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.business.id.required"));
            }

            ObjectId businessId;
            Business business;

            try {
                businessId = new ObjectId(oidParam);
                business = BaseDao.getDocumentById(businessId, Business.class);

                if (business == null) {
                    throw Spark.halt(HttpStatus.NOT_FOUND_404, LabelsFunction.description(language, "error.business.not.found"));
                }
            } catch (IllegalArgumentException ex) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.business.id"));
            }

            // Merge form parameters into the business object
            business = RequestUtils.mergeFromParams(params, business);

            try {
                // Update the business in the database
                BaseDao.updateDocument(business);

                if (!files.isEmpty()) {
                    BaseDao.deleteImages(business, "imageIds");
                    BaseDao.saveImages(new ArrayList<>(files.values()), business, "imageIds");
                }

                // Redirect to the business-new page with the business ID
                /*String redirectUrl = RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_BUSINESS_EDIT", language);
                redirectUrl += "?oid=" + businessId.toString();*/
                String redirectUrl = "ok";

                if (params.containsKey("status")) {
                    if (StringUtils.isNotBlank(business.getStatus())) {
                        if (StringUtils.equalsIgnoreCase(business.getStatus(), StatusType.UNCONFIRMED.name().toLowerCase())) {
                            redirectUrl = RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_BUSINESSES", language);
                        }
                    }
                }

                return redirectUrl;
            } catch (Exception ex) {
                LOGGER.error("Error updating business", ex);
                throw Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500, LabelsFunction.description(language, "error.unable.to.update.business"));
            }

        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static Route account_business_draft_choice = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        if (user != null) {
            String language = RoutesUtils.language(request);
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, null);

            String choice = params.get("choice");
            String draftId = params.get("draftId");

            if (choice == null || draftId == null) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.request"));
            }

            String redirectUrl = RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_BUSINESS_EDIT", language);

            if (StringUtils.equals("use_draft", choice)) {
                // User wants to use the existing draft
                redirectUrl += "?oid=" + draftId;
            } else if (StringUtils.equals("start_fresh", choice)) {
                // User wants to start fresh - delete the draft and create new
                try {
                    ObjectId draftObjectId = new ObjectId(draftId);
                    Business draftBusiness = BaseDao.getDocumentById(draftObjectId, Business.class);

                    if (draftBusiness != null) {
                        // Delete the draft business
                        BaseDao.deleteDocument(draftBusiness);
                    }
                } catch (Exception ex) {
                    LOGGER.error("Error handling draft choice", ex);
                    throw Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500, LabelsFunction.description(language, "error.unable.to.process.choice"));
                }
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.choice"));
            }

            response.redirect(redirectUrl);
            return null;

        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static Route be_business_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        String businessIds = params.get("businessIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(businessIds)) {
            String[] ids = businessIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Business business = BaseDao.getDocumentById(oid, Business.class);
                    if (business != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(business);
                                BaseDao.insertLog(user, business, LogType.DELETE);
                                break;
                            case "archive":
                                business.setArchived(true);
                                BaseDao.updateDocument(business);
                                BaseDao.insertLog(user, business, LogType.UPDATE);
                                break;
                            case "unarchive":
                                business.setArchived(false);
                                BaseDao.updateDocument(business);
                                BaseDao.insertLog(user, business, LogType.UPDATE);
                                break;
                            case "confirm":
                                if (StringUtils.equalsIgnoreCase(business.getStatus(), StatusType.UNCONFIRMED.name().toLowerCase())) {
                                    business.setStatus(StatusType.CONFIRMED.name().toLowerCase());
                                    BaseDao.updateDocument(business);
                                    BaseDao.insertLog(user, business, LogType.UPDATE);

                                    // TODO: MANDARE MAIL CONFERMA
                                    break;
                                }
                        }
                    }
                }
            }
        }

        return "ok";
    };
}