package controller;

import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.ProfileType;
import enums.StatusType;
import extensions.LabelsFunction;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Business;
import pojo.QueryOptions;
import pojo.User;
import spark.*;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class ResultController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResultController.class.getName());

    public static TemplateViewRoute results = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.RESULTS, attributes, request);
    };

    public static TemplateViewRoute results_data = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);

        // Parse request parameters
        Map<String, String> params = new HashMap<>();
        Map<String, UploadedFile> files = new HashMap<>();
        RequestUtils.parseRequest(request, params, files);

        // Build filters list
        List<Bson> filters = new ArrayList<>();

        // Always filter by confirmed status
        filters.add(DaoFilters.getFilter("status", DaoFiltersOperation.EQ, StatusType.CONFIRMED.name().toLowerCase()));

        for (String filterName : params.keySet()) {
            String value = params.get(filterName);
            Object filterValue;

            if (BooleanUtils.toBooleanObject(value) != null) {
                filterValue = BooleanUtils.toBoolean(value);
            } else if (value.contains("|")) {
                filterValue = Arrays.asList(value.split("\\|"));
            } else {
                filterValue = value;
            }

            filters.add(DaoFilters.getFilter(filterName, DaoFiltersOperation.EQ, filterValue));
        }

        // Pagination parameters
        int page = 1;
        int limit = 12; // Default items per page

        try {
            if (StringUtils.isNotBlank(params.get("page"))) {
                page = Integer.parseInt(params.get("page"));
            }
            if (StringUtils.isNotBlank(params.get("limit"))) {
                limit = Integer.parseInt(params.get("limit"));
            }
        } catch (NumberFormatException ex) {
            LOGGER.warn("Invalid pagination parameters", ex);
        }

        int skip = (page - 1) * limit;

        // Sorting parameters
        String orderBy = StringUtils.defaultIfBlank(params.get("orderBy"), "creationDate");
        String orderType = StringUtils.defaultIfBlank(params.get("orderType"), "desc");

        // Create query options
        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, skip, limit, orderBy, orderType);

        try {
            // Execute query
            List<Business> businesses = BaseDao.getDocumentsByFilters(Business.class, queryOptions);
            long totalCount = BaseDao.countDocumentsByFilters(Business.class, queryOptions);

            // loadmore
            boolean loadmore = false;
            if (totalCount > skip + limit) {
                loadmore = true;
            }

            // Add results to attributes
            attributes.put("businesses", businesses);
            attributes.put("totalCount", totalCount);
            attributes.put("currentPage", page);
            attributes.put("limit", limit);
            attributes.put("loadmore", loadmore);

            // Add filter parameters back to attributes for form state
            attributes.put("filters", params);
        } catch (Exception ex) {
            LOGGER.error("Error executing business query", ex);
            attributes.put("businesses", new ArrayList<Business>());
            attributes.put("totalCount", 0);
            attributes.put("error", "Unable to load results. Please try again.");
        }

        return Core.render(Pages.RESULTS_DATA, attributes, request);
    };
}