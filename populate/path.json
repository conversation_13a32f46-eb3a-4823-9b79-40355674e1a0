{
    "_id" : ObjectId("664ddd6116f45b083040b80e"),
    "creation" : ISODate("2024-05-22T11:56:17.933Z"),
    "lastUpdate" : ISODate("2024-05-22T11:56:17.933Z"),
    "key" : "CONTACTS",
    "items" : [ 
        {
            "language" : "it",
            "description" : "/it/contatti"
        }, 
        {
            "language" : "en",
            "description" : "/en/contacts"
        }
    ]
}
{
    "_id" : ObjectId("664ddd6116f45b083040b80f"),
    "creation" : ISODate("2024-05-22T11:56:17.933Z"),
    "lastUpdate" : ISODate("2024-05-22T11:56:17.933Z"),
    "key" : "HOME",
    "items" : [ 
        {
            "language" : "it",
            "description" : "/it/home"
        }, 
        {
            "language" : "en",
            "description" : "/en/home"
        }
    ]
}
{
    "_id" : ObjectId("664ddd6116f45b083040b810"),
    "creation" : ISODate("2024-05-22T11:56:17.933Z"),
    "lastUpdate" : ISODate("2024-05-22T11:56:17.933Z"),
    "key" : "PHOTO_COLLECTION",
    "items" : [ 
        {
            "language" : "it",
            "description" : "/it/foto"
        }, 
        {
            "language" : "en",
            "description" : "/en/photos"
        }
    ]
}
{
    "_id" : ObjectId("664ddd6116f45b083040b811"),
    "creation" : ISODate("2024-05-22T11:56:17.933Z"),
    "lastUpdate" : ISODate("2024-05-22T11:56:17.933Z"),
    "key" : "ABOUT",
    "items" : [ 
        {
            "language" : "it",
            "description" : "/it/chi-siamo"
        }, 
        {
            "language" : "en",
            "description" : "/en/about"
        }
    ]
}
{
    "_id" : ObjectId("664ddd6116f45b083040b812"),
    "creation" : ISODate("2024-05-22T11:56:17.933Z"),
    "lastUpdate" : ISODate("2024-05-22T11:56:17.933Z"),
    "key" : "PHOTO",
    "items" : [ 
        {
            "language" : "it",
            "description" : "/it/foto/:category/:identifier"
        }, 
        {
            "language" : "en",
            "description" : "/en/photos/:category/:identifier"
        }
    ]
}
{
    "_id" : ObjectId("66f16d5ddf0d5e3bab5aa9a1"),
    "creation" : ISODate("2024-05-22T11:56:17.933Z"),
    "lastUpdate" : ISODate("2024-05-22T11:56:17.933Z"),
    "key" : "PHOTO_CATEGORY",
    "items" : [ 
        {
            "language" : "it",
            "description" : "/it/foto/:category"
        }, 
        {
            "language" : "en",
            "description" : "/en/photos/:category"
        }
    ]
}
{
    "_id" : ObjectId("67fe58545a5655085998d299"),
    "creation" : ISODate("2025-04-15T13:00:04.544Z"),
    "lastUpdate" : ISODate("2025-04-15T13:00:04.544Z"),
    "key" : "ACCOUNT_RECOVER",
    "items" : [ 
        {
            "language" : "it",
            "description" : "/it/recupera"
        }, 
        {
            "language" : "en",
            "description" : "/en/recover"
        }
    ]
}
{
    "_id" : ObjectId("67fe58545a5655085998d295"),
    "creation" : ISODate("2025-04-15T13:00:04.544Z"),
    "lastUpdate" : ISODate("2025-04-15T13:00:04.544Z"),
    "key" : "RECOVER_SEND",
    "items" : [ 
        {
            "language" : "it",
            "description" : "/it/recupera/invia"
        }, 
        {
            "language" : "en",
            "description" : "/en/recover/send"
        }
    ]
}
{
    "_id" : ObjectId("67fe58545a5655085998d292"),
    "creation" : ISODate("2025-04-15T13:00:04.544Z"),
    "lastUpdate" : ISODate("2025-04-15T13:00:04.544Z"),
    "key" : "ACCOUNT_REGISTER",
    "items" : [ 
        {
            "language" : "it",
            "description" : "/it/registrati"
        }, 
        {
            "language" : "en",
            "description" : "/en/register"
        }
    ]
}
{
    "_id" : ObjectId("67fe58545a5655085998d29b"),
    "creation" : ISODate("2025-04-15T13:00:04.544Z"),
    "lastUpdate" : ISODate("2025-04-15T13:00:04.544Z"),
    "key" : "ACCOUNT_LOGIN",
    "items" : [ 
        {
            "language" : "it",
            "description" : "/it/accesso"
        }, 
        {
            "language" : "en",
            "description" : "/en/login"
        }
    ]
}
{
    "_id" : ObjectId("67fe58545a5655085998d293"),
    "creation" : ISODate("2025-04-15T13:00:04.544Z"),
    "lastUpdate" : ISODate("2025-04-15T13:00:04.544Z"),
    "key" : "LOGIN_DO",
    "items" : [ 
        {
            "language" : "it",
            "description" : "/it/login/ok"
        }, 
        {
            "language" : "en",
            "description" : "/en/login/ok"
        }
    ]
}
{
    "_id" : ObjectId("67fe58545a5655085998d29a"),
    "creation" : ISODate("2025-04-15T13:00:04.544Z"),
    "lastUpdate" : ISODate("2025-04-15T13:00:04.544Z"),
    "key" : "REGISTER_DO",
    "items" : [ 
        {
            "language" : "it",
            "description" : "/it/registrati/salva"
        }, 
        {
            "language" : "en",
            "description" : "/en/register/save"
        }
    ]
}
{
	"_id": ObjectId("664dfc1a16f45b083040a101"),
	"creation": ISODate("2024-05-22T11:56:17.933Z"),
	"lastUpdate": ISODate("2024-05-22T11:56:17.933Z"),
	"key": "ACCOUNT_LOGIN",
	"items": [
		{ "language": "it", "description": "/it/account/login" },
		{ "language": "en", "description": "/en/account/login" }
	]
}
{
	"_id": ObjectId("664dfc2b16f45b083040a102"),
	"creation": ISODate("2024-05-22T11:56:17.933Z"),
	"lastUpdate": ISODate("2024-05-22T11:56:17.933Z"),
	"key": "ACCOUNT_INFO",
	"items": [
		{ "language": "it", "description": "/it/account/info" },
		{ "language": "en", "description": "/en/account/info" }
	]
}
{
	"_id": ObjectId("664dfc3c16f45b083040a103"),
	"creation": ISODate("2024-05-22T11:56:17.933Z"),
	"lastUpdate": ISODate("2024-05-22T11:56:17.933Z"),
	"key": "ACCOUNT_FAVOURITES",
	"items": [
		{ "language": "it", "description": "/it/account/favourites" },
		{ "language": "en", "description": "/en/account/favourites" }
	]
}
{
	"_id": ObjectId("664dfc4d16f45b083040a104"),
	"creation": ISODate("2024-05-22T11:56:17.933Z"),
	"lastUpdate": ISODate("2024-05-22T11:56:17.933Z"),
	"key": "ACCOUNT_BUSINESSES",
	"items": [
		{ "language": "it", "description": "/it/account/businesses" },
		{ "language": "en", "description": "/en/account/businesses" }
	]
}
{
	"_id": ObjectId("664dfc5e16f45b083040a105"),
	"creation": ISODate("2024-05-22T11:56:17.933Z"),
	"lastUpdate": ISODate("2024-05-22T11:56:17.933Z"),
	"key": "ACCOUNT_BUSINESS_EDIT",
	"items": [
		{ "language": "it", "description": "/it/account/business-new" },
		{ "language": "en", "description": "/en/account/business-new" }
	]
}
{
	"_id": ObjectId("664dfc6f16f45b083040a106"),
	"creation": ISODate("2024-05-22T11:56:17.933Z"),
	"lastUpdate": ISODate("2024-05-22T11:56:17.933Z"),
	"key": "ACCOUNT_BUSINESS_INFO",
	"items": [
		{ "language": "it", "description": "/it/account/business-info" },
		{ "language": "en", "description": "/en/account/business-info" }
	]
}
{
	"_id": ObjectId("664dfc8016f45b083040a107"),
	"creation": ISODate("2024-05-22T11:56:17.933Z"),
	"lastUpdate": ISODate("2024-05-22T11:56:17.933Z"),
	"key": "ACCOUNT_BUSINESS_SERVICES",
	"items": [
		{ "language": "it", "description": "/it/account/business-services" },
		{ "language": "en", "description": "/en/account/business-services" }
	]
}
{
	"_id": ObjectId("664dfc9116f45b083040a108"),
	"creation": ISODate("2024-05-22T11:56:17.933Z"),
	"lastUpdate": ISODate("2024-05-22T11:56:17.933Z"),
	"key": "ACCOUNT_BUSINESS_BOOKINGS",
	"items": [
		{ "language": "it", "description": "/it/account/business-bookings" },
		{ "language": "en", "description": "/en/account/business-bookings" }
	]
}
{
	"_id": ObjectId("664dfca216f45b083040a109"),
	"creation": ISODate("2024-05-22T11:56:17.933Z"),
	"lastUpdate": ISODate("2024-05-22T11:56:17.933Z"),
	"key": "ACCOUNT_BUSINESS_REVIEWS",
	"items": [
		{ "language": "it", "description": "/it/account/business-reviews" },
		{ "language": "en", "description": "/en/account/business-reviews" }
	]
}

