@echo off

:: check for mongoimport presence
if not exist c:\opt\mongodb\server\bin\mongoimport.exe (
	echo ERROR: unable to find mongoimport tool
	goto :end
)


:: check for mongo presence
if not exist c:\opt\mongodb\server\bin\mongo.exe (
	echo ERROR: unable to find mongo tool
	goto :end
)


:: population
c:\opt\mongodb\server\bin\mongoimport.exe --db koraliso --legacy --collection user --drop --file \projects\koraliso\populate\user.json

c:\opt\mongodb\server\bin\mongoimport.exe --db koraliso --legacy --collection company --drop --file \projects\koraliso\populate\company.json
c:\opt\mongodb\server\bin\mongoimport.exe --db koraliso --legacy --collection path --drop --file \projects\koraliso\populate\path.json
c:\opt\mongodb\server\bin\mongoimport.exe --db koraliso --legacy --collection settings --drop --file \projects\koraliso\populate\settings.json
c:\opt\mongodb\server\bin\mongoimport.exe --db koraliso --legacy --collection label --drop --file \projects\koraliso\populate\label.json


	if errorlevel 1 (
		echo ERROR: unable to update password on user collection
		goto :end
	)


:: the end
:end
pause
